package com.sentinel.nocalhost.intellicode.ui;

import org.junit.Test;
import org.junit.Before;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

/**
 * MessageFlowManager单元测试
 */
public class MessageFlowManagerTest {
    
    @Mock
    private MarkdownRenderer mockRenderer;
    
    private MessageFlowManager messageFlowManager;
    
    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        messageFlowManager = new MessageFlowManager(mockRenderer);
    }
    
    @Test
    public void testInitialState() {
        assertEquals(MessageFlowManager.MessageState.IDLE, messageFlowManager.getCurrentState());
    }
    
    @Test
    public void testStartSession() {
        messageFlowManager.startSession("session-1");
        assertEquals(MessageFlowManager.MessageState.IDLE, messageFlowManager.getCurrentState());
    }
    
    @Test
    public void testAssistantMessageFlow() {
        // 开始assistant消息
        messageFlowManager.handleAssistantMessage("Hello");
        assertEquals(MessageFlowManager.MessageState.ASSISTANT_MESSAGE, messageFlowManager.getCurrentState());
        
        // 继续assistant消息
        messageFlowManager.handleAssistantMessage(" World");
        assertEquals(MessageFlowManager.MessageState.ASSISTANT_MESSAGE, messageFlowManager.getCurrentState());
        
        // 验证renderer调用
        verify(mockRenderer, times(1)).startAssistantMessage();
        verify(mockRenderer, times(2)).appendAssistantChunk(any());
    }
    
    @Test
    public void testToolExecutionInterruptsAssistant() {
        // 开始assistant消息
        messageFlowManager.handleAssistantMessage("Hello");
        assertEquals(MessageFlowManager.MessageState.ASSISTANT_MESSAGE, messageFlowManager.getCurrentState());
        
        // 开始tool执行，应该中断assistant消息
        messageFlowManager.startToolExecution("test-tool", "input");
        assertEquals(MessageFlowManager.MessageState.TOOL_EXECUTION, messageFlowManager.getCurrentState());
        
        // 验证assistant消息被正确结束
        verify(mockRenderer, times(1)).finishAssistantMessage();
        verify(mockRenderer, times(1)).addToolStart("test-tool", "input");
    }
    
    @Test
    public void testAssistantMessageDuringToolExecution() {
        // 开始tool执行
        messageFlowManager.startToolExecution("test-tool", "input");
        assertEquals(MessageFlowManager.MessageState.TOOL_EXECUTION, messageFlowManager.getCurrentState());
        
        // 在tool执行期间收到assistant消息，应该被缓存
        messageFlowManager.handleAssistantMessage("cached message");
        assertEquals(MessageFlowManager.MessageState.TOOL_EXECUTION, messageFlowManager.getCurrentState());
        
        // 结束tool执行，缓存的消息应该被处理
        messageFlowManager.finishToolExecution("test-tool", "output", true);
        assertEquals(MessageFlowManager.MessageState.ASSISTANT_MESSAGE, messageFlowManager.getCurrentState());
        
        // 验证缓存的assistant消息被正确处理
        verify(mockRenderer, times(1)).addToolEnd("test-tool", "output", true);
        verify(mockRenderer, times(1)).startAssistantMessage();
        verify(mockRenderer, times(1)).appendAssistantChunk("cached message");
    }
    
    @Test
    public void testErrorHandling() {
        // 在assistant消息进行中发生错误
        messageFlowManager.handleAssistantMessage("Hello");
        assertEquals(MessageFlowManager.MessageState.ASSISTANT_MESSAGE, messageFlowManager.getCurrentState());
        
        // 处理错误
        RuntimeException error = new RuntimeException("Test error");
        messageFlowManager.handleError(error);
        assertEquals(MessageFlowManager.MessageState.IDLE, messageFlowManager.getCurrentState());
        
        // 验证assistant消息被正确结束，错误被记录
        verify(mockRenderer, times(1)).finishAssistantMessage();
        verify(mockRenderer, times(1)).addError("Test error");
    }
    
    @Test
    public void testSessionEnd() {
        // 开始assistant消息
        messageFlowManager.handleAssistantMessage("Hello");
        assertEquals(MessageFlowManager.MessageState.ASSISTANT_MESSAGE, messageFlowManager.getCurrentState());
        
        // 结束会话
        messageFlowManager.endSession("session-1", true);
        assertEquals(MessageFlowManager.MessageState.SESSION_ENDING, messageFlowManager.getCurrentState());
        
        // 验证assistant消息被正确结束
        verify(mockRenderer, times(1)).finishAssistantMessage();
    }
    
    @Test
    public void testReset() {
        // 设置一些状态
        messageFlowManager.handleAssistantMessage("Hello");
        assertEquals(MessageFlowManager.MessageState.ASSISTANT_MESSAGE, messageFlowManager.getCurrentState());
        
        // 重置
        messageFlowManager.reset();
        assertEquals(MessageFlowManager.MessageState.IDLE, messageFlowManager.getCurrentState());
    }
}