<idea-plugin>
    <id>com.sentinel.JetDev</id>

    <name>JetDev</name>

    <vendor email="<EMAIL>" url="https://www.quwangroup.com/">趣丸</vendor>

    <description><![CDATA[
        <p><a href="https://q9jvw0u5f5.feishu.cn/wiki/TrM6whxMGi8PttkQUT3c3L65npA">JetDev使用手册</a></p>
        <p>Goland支持最低版本：2023.2</p>
        <p>sinceBuild=232.8660.185</p>
        <p>untilBuild=242.*</p>
      ]]>
    </description>

    <depends>com.intellij.modules.platform</depends>
    <depends>org.jetbrains.plugins.terminal</depends>

    <depends optional="true" config-file="python-only.xml">com.intellij.modules.python</depends>
    <depends optional="true" config-file="java-only.xml">com.intellij.java</depends>
    <depends optional="true" config-file="go-only.xml">org.jetbrains.plugins.go</depends>

    <extensions defaultExtensionNs="com.intellij">

        <applicationService serviceImplementation="com.sentinel.nocalhost.configuration.go.dlv.JetDevSettings"/>

        <applicationService serviceImplementation="com.sentinel.nocalhost.api.IdeHubAPI"/>
        <applicationService serviceImplementation="com.sentinel.nocalhost.commands.NhctlCommand"/>
        <applicationService serviceImplementation="com.sentinel.nocalhost.service.ProgressProcessManager"/>
        <applicationService serviceImplementation="com.sentinel.nocalhost.service.NocalhostIdeHubApIService"/>
        <applicationService serviceImplementation="com.sentinel.nocalhost.api.CodeReviewAPI"/>
        <applicationService serviceImplementation="com.sentinel.nocalhost.intellicode.api.IntellicodeAPI"/>

        <projectService serviceImplementation="com.sentinel.nocalhost.service.NocalhostContextManager"/>
        <projectService serviceImplementation="com.sentinel.nocalhost.exception.NocalhostNotifier"/>
        <projectService serviceImplementation="com.sentinel.nocalhost.service.NocalhostAppQueryManager"/>
        <projectService serviceImplementation="com.sentinel.nocalhost.service.NocalhostAppListManager"/>
        <projectService serviceImplementation="com.sentinel.nocalhost.service.JetDevExecutionManager"/>
        <projectService serviceImplementation="com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier"/>
        <projectService serviceImplementation="com.sentinel.nocalhost.service.CodeReviewSuggestionListManager"/>
        <projectService serviceImplementation="com.sentinel.nocalhost.service.CodeReviewTaskManager"/>

        <postStartupActivity implementation="com.sentinel.nocalhost.startup.JetDevAutoRefreshAppListActivity" id="JetDevAutoRefreshAppListActivity" />

        <statusBarWidgetFactory
                implementation="com.sentinel.nocalhost.ui.widget.SyncStatusWidgetFactory"
                order="first" id="File Sync Status"/>

        <toolWindow id="JetDev" anchor="right" icon="NocalhostIcons.ConfigurationLogoRight"
                    factoryClass="com.sentinel.nocalhost.ui.toolwindow.NocalhostWindowFactory"/>

        <toolWindow id="JetDev Console" anchor="bottom" icon="NocalhostIcons.ConfigurationLogoRight"
                    factoryClass="com.sentinel.nocalhost.ui.console.NocalhostConsoleWindowFactory"
                    canCloseContents="true"/>


        <notificationGroup id="Login Notifications" displayType="BALLOON" />
        <notificationGroup id="Nhctl Notifications" displayType="BALLOON" />
        <notificationGroup id="Auth Notifications" displayType="BALLOON" />
        <notificationGroup id="API Notifications" displayType="BALLOON" />
        <notificationGroup id="Review Notifications" displayType="BALLOON" />
        <notificationGroup id="Intellicode Notifications" displayType="BALLOON" />

        <checkinHandlerFactory implementation="com.sentinel.nocalhost.vcs.GitCommitNotificationListener" />

        <programRunner implementation="com.sentinel.nocalhost.configuration.NocalhostRunner" order="first"/>

    </extensions>
</idea-plugin>