package com.sentinel.nocalhost.commands.data;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class NhctlSyncOptions extends NhctlGlobalOptions {
    private String deployment;
    private String controllerType;
    private String container;
    private boolean doubleSide;
    private boolean overwrite;
    private boolean resume;
    private boolean stop;
    private List<String> ignoredPatterns;
    private List<String> syncedPatterns;

    public NhctlSyncOptions(String appId, String cluster, String namespace) {
        super(appId, cluster, namespace);
    }
}
