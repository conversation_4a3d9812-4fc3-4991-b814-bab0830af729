package com.sentinel.nocalhost.commands.data;

import com.intellij.openapi.progress.Task;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NhctlGlobalOptions {
    private boolean debug;
    private String kubeconfig;
    private String namespace;
    private Task task;
    private String appId;
    private String cluster;

    public NhctlGlobalOptions() {}

    protected NhctlGlobalOptions(String appId, String cluster, String namespace) {
        this.appId = appId;
        this.cluster = cluster;
        this.namespace = namespace;
    }

}
