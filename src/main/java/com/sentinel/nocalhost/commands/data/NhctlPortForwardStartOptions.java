package com.sentinel.nocalhost.commands.data;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class NhctlPortForwardStartOptions extends NhctlGlobalOptions {
    private boolean daemon;
    private String deployment;
    private List<String> devPorts;
    private String pod;
    private String type;
    private String port;

    public NhctlPortForwardStartOptions(String appId, String cluster, String namespace) {
        super(appId, cluster, namespace);
    }
}
