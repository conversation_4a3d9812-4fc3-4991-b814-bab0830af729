package com.sentinel.nocalhost.commands.data;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NhctlDevStartOptions extends NhctlGlobalOptions {
    private String mode;
    private String deployment;
    private String image;
    private String header;
    private String localSync;
    private String sidecarImage;
    private String storageClass;
    private String syncthingVersion;
    private String workDir;
    private String container;
    private String controllerType;
    private boolean withoutTerminal;
    private boolean authCheck;


    public NhctlDevStartOptions(String appId, String cluster, String namespace) {
        super(appId, cluster, namespace);
    }

}
