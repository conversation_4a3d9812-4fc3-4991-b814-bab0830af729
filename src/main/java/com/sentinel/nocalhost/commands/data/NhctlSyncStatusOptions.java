package com.sentinel.nocalhost.commands.data;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NhctlSyncStatusOptions extends NhctlGlobalOptions {
    private String deployment;
    private String controllerType;
    private boolean override;
    private boolean wait;
    private int timeout;

    public NhctlSyncStatusOptions(String appId, String cluster, String namespace) {
        super(appId, cluster, namespace);
    }
}
