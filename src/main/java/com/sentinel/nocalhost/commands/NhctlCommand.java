package com.sentinel.nocalhost.commands;

import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.google.common.io.CharStreams;
import com.intellij.execution.ExecutionException;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.util.SystemInfo;
import com.intellij.util.Alarm;
import com.intellij.util.EnvironmentUtil;
import com.sentinel.nocalhost.Config;
import com.sentinel.nocalhost.commands.data.*;
import com.sentinel.nocalhost.exception.NhctlCommandException;
import com.sentinel.nocalhost.exception.NocalhostExecuteCmdException;
import com.sentinel.nocalhost.service.ProgressProcessManager;
import com.sentinel.nocalhost.utils.NhctlUtil;
import com.sentinel.nocalhost.utils.SudoUtil;
import com.sentinel.nocalhost.utils.Tools;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

public class NhctlCommand {

    public void reviewStart() throws IOException, InterruptedException, NocalhostExecuteCmdException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "intellicode", "start");
        execute(args, new NhctlGlobalOptions());
    }

    public void reviewStop() throws IOException, InterruptedException, NocalhostExecuteCmdException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "intellicode", "stop");
        execute(args, new NhctlGlobalOptions());
    }

    public void reviewInit() throws IOException, InterruptedException, NocalhostExecuteCmdException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "intellicode", "init");
        execute(args, new NhctlGlobalOptions());
    }

    public String devStart(
            NhctlDevStartOptions opts
    ) throws IOException, InterruptedException, NocalhostExecuteCmdException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "cicd", "start");
        return execute(args, opts);
    }

    public void devEnd(NhctlDevEndOptions opts) throws IOException, InterruptedException, NocalhostExecuteCmdException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "cicd", "end");
        execute(args, opts);
    }

    public String sync(NhctlSyncOptions opts) throws IOException, InterruptedException, NocalhostExecuteCmdException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "cicd", "sync");
        if (opts.isStop()) {
            args.add("--stop");
        }
        if (opts.isResume()) {
            args.add("--resume");
        }
        return execute(args, opts);
    }

    public String syncStatus(NhctlSyncStatusOptions opts) throws InterruptedException, NocalhostExecuteCmdException, IOException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "cicd", "sync-status");
        if (opts.isOverride()) {
            args.add("--override");
        }
        if (opts.isWait()) {
            args.add("--wait");
        }
        if (opts.getTimeout() > 0) {
            args.add("--timeout");
            args.add(String.valueOf(opts.getTimeout()));
        }
        return execute(args, opts);
    }

    public void startPortForward(NhctlPortForwardStartOptions opts) throws IOException, InterruptedException, NocalhostExecuteCmdException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "cicd", "debug-port-forward", "--start", "--local-port", opts.getPort());
        String result = execute(args, opts);
        System.out.println(result);
    }

    public void endPortForward(NhctlPortForwardEndOptions opts) throws InterruptedException, NocalhostExecuteCmdException, IOException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "cicd", "debug-port-forward", "--end", "--local-port", opts.getPort());
        execute(args, opts);
    }

    public String version() throws InterruptedException, NocalhostExecuteCmdException, IOException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "version");
        return execute(args, null);
    }

    public void setToken(String token) throws InterruptedException, NocalhostExecuteCmdException, IOException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "token", "set", token);
        execute(args, null);
    }

    public String getTrafficMark() throws NocalhostExecuteCmdException, IOException, InterruptedException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "x", "traffic-mark", "get");
        return execute(args, null);
    }

    public String setTrafficMark(String trafficMark) throws NocalhostExecuteCmdException, IOException, InterruptedException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "x", "traffic-mark", "set", "-m", trafficMark);
        return execute(args, null);
    }

    protected String execute(List<String> args, NhctlGlobalOptions opts) throws IOException, InterruptedException, NocalhostExecuteCmdException {
        return execute(args, opts, null);
    }

    protected String execute(List<String> args, NhctlGlobalOptions opts, String sudoPassword) throws IOException, InterruptedException, NocalhostExecuteCmdException {
        addGlobalOptions(args, opts);

        if (sudoPassword != null) {
            args = SudoUtil.toSudoCommand(args);
        }

        GeneralCommandLine commandLine = getCommandline(args);
        String cmd = commandLine.getCommandLineString();

        Process process;
        try {
            process = commandLine.createProcess();
            if (opts != null && opts.getTask() != null) {
                ApplicationManager.getApplication().getService(ProgressProcessManager.class)
                        .add(opts.getTask(), process);
            }
            if (sudoPassword != null) {
                SudoUtil.inputPassword(process, sudoPassword);
            }
        } catch (ExecutionException e) {
            throw new NocalhostExecuteCmdException(cmd, -1, e.getMessage());
        }

        if (!args.isEmpty() && StringUtils.equals(args.get(1), "get")) {
            new Alarm(Alarm.ThreadToUse.POOLED_THREAD, ApplicationManager.getApplication())
                    .addRequest(process::destroy, 10 * 1000);
        }

        final AtomicReference<String> errorOutput = new AtomicReference<>();
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            InputStreamReader reader = new InputStreamReader(process.getErrorStream(), Charsets.UTF_8);
            try {
                errorOutput.set(CharStreams.toString(reader));
            } catch (Exception ignore) {
            }
        });

        try (InputStreamReader reader = new InputStreamReader(process.getInputStream(), Charsets.UTF_8)) {
            String output = CharStreams.toString(reader);
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                output += errorOutput.get();
                throw new NhctlCommandException(cmd, exitCode, output, errorOutput.get());
            }
            return output;
        }
    }

    protected void addGlobalOptions(List<String> args, NhctlGlobalOptions opts) {
        if (opts == null) {
            return;
        }
        if (opts.isDebug()) {
            args.add("--debug");
        }
        if (StringUtils.isNotEmpty(opts.getAppId())) {
            args.add("--app");
            args.add(opts.getAppId());
        }
        if (StringUtils.isNotEmpty(opts.getCluster())) {
            args.add("--cluster");
            args.add(opts.getCluster());
        }
        if (StringUtils.isNotEmpty(opts.getNamespace())) {
            args.add("--namespace");
            args.add(opts.getNamespace());
        }

        args.add("--client=jetDev");
        args.add("--ide=" + Tools.getIdeaName());
        args.add("--pluginVersion=" + Config.getProperty("report_version"));
    }

    protected String getNhctlCmd() {
        return NhctlUtil.binaryPath();
    }

    protected GeneralCommandLine getCommandline(List<String> args) {
        final Map<String, String> environment = new HashMap<>(EnvironmentUtil.getEnvironmentMap());
        environment.put("DISABLE_SPINNER", "true");
        if (SystemInfo.isMac || SystemInfo.isLinux) {
            String path = environment.get("PATH");
            String nhctlCmd = getNhctlCmd();
            if (StringUtils.contains(nhctlCmd, "/")) {
                path = nhctlCmd.substring(0, nhctlCmd.lastIndexOf("/")) + ":" + path;
                environment.put("PATH", path);
            }
        }
        return new GeneralCommandLine(args).withEnvironment(environment);
    }

    public String getConfig(NhctlConfigOptions opts) throws IOException, InterruptedException, NocalhostExecuteCmdException {
        List<String> args = Lists.newArrayList(getNhctlCmd(), "cicd", "data", "--action", "get", "--type", "config");
        return execute(args, opts);
    }

}
