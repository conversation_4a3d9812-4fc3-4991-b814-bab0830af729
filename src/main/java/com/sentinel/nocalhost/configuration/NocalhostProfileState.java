package com.sentinel.nocalhost.configuration;

import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.intellij.execution.ExecutionException;
import com.intellij.execution.Executor;
import com.intellij.execution.configurations.CommandLineState;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.execution.executors.DefaultDebugExecutor;
import com.intellij.execution.process.ProcessHandler;
import com.intellij.execution.runners.ExecutionEnvironment;
import com.intellij.execution.ui.ConsoleView;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.terminal.TerminalExecutionConsole;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.api.data.UserProject;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.commands.data.*;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.JetDevExecutionManager;
import com.sentinel.nocalhost.service.NocalhostAppListManager;
import com.sentinel.nocalhost.service.NocalhostContextManager;
import com.sentinel.nocalhost.service.NocalhostIdeHubApIService;
import com.sentinel.nocalhost.utils.DataUtils;
import com.sentinel.nocalhost.utils.NhctlUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public class NocalhostProfileState extends CommandLineState {
    private static final Logger LOG = Logger.getInstance(NocalhostProfileState.class);
    private final List<Disposable> disposables = Lists.newArrayList();
    private final AtomicReference<NocalhostRunnerContext> refContext = new AtomicReference<>(null);
    private final NocalhostIdeHubApIService hubApIService = ApplicationManager.getApplication().getService(NocalhostIdeHubApIService.class);
    public NocalhostProfileState(ExecutionEnvironment environment) {
        super(environment);
    }

    @Override
    protected @NotNull ProcessHandler startProcess() throws ExecutionException {
        NocalhostRunnerContext dev = refContext.get();
        if (dev == null) {
            throw new ExecutionException("Call prepare() before this method");
        }
        UserApp context = dev.getContext();

        List<String> commandLine = hubApIService.getCommandLine(context, getEnvironment());

        NocalhostDevProcessHandler processHandler = new NocalhostDevProcessHandler(
                new GeneralCommandLine(commandLine).withCharset(Charsets.UTF_8),
                getEnvironment(),
                this,
                context.getId()
        );

        JetDevExecutionManager.getInstance(getEnvironment().getProject()).add(processHandler);

        return processHandler;
    }

    public String getDebugPort() {
        NocalhostRunnerContext dev = refContext.get();
        return dev.getDebug().getLocalPort();
    }

    @Override
    protected @NotNull ConsoleView createConsole(@NotNull Executor executor) {
        return new TerminalExecutionConsole(getEnvironment().getProject(), null);
    }

    public void prepare() throws ExecutionException {
        try {
            var project = getEnvironment().getProject();

            setContext(project);
            var context = NocalhostContextManager.getInstance(project).getContext();
            if (context == null) {
                throw new ExecutionException(Constants.USER_APP_NOT_SUCCESSFUL);
            }

            removeRepeatService(project, context);

            hubApIService.isUserAppStarted(project, context);

            hubApIService.isProjectPathMatched(project, context);

            if (!isSyncStatusIdle()) {
                throw new ExecutionException("File sync has not yet completed.");
            }

            NhctlRawConfig devConfig = NhctlUtil.getDevConfig(context);
            List<ServiceContainer> containers = devConfig.getContainers();
            ServiceContainer container = containers.isEmpty() ? null : containers.get(0);
            for (ServiceContainer c : containers) {
                if (StringUtils.equals(context.getName(), c.getName())) {
                    container = c;
                    break;
                }
            }
            if (container == null) {
                throw new ExecutionException("Service container config not found.");
            }

            NocalhostRunnerContext.Debug debug = null;
            NocalhostRunnerContext.Command command = new NocalhostRunnerContext.Command(resolveRunCommand(container), resolveDebugCommand(container));
            if (isDebugExecutor()) {
                if (!StringUtils.isNotEmpty(command.getDebug())) {
                    throw new ExecutionException("Debug command not configured");
                }
                String remotePort = resolveDebugPort(container);
                if (!StringUtils.isNotEmpty(remotePort)) {
                    throw new ExecutionException("Remote debug port not configured.");
                }
                String localPort = startDebugPortForward(context);
                debug = new NocalhostRunnerContext.Debug(remotePort, localPort);
            } else {
                if (!StringUtils.isNotEmpty(command.getRun())) {
                    throw new ExecutionException("Run command not configured");
                }
            }

            refContext.set(new NocalhostRunnerContext(
                    debug,
                    container.getDev().getShell(),
                    command,
                    context,
                    container
            ));
        } catch (Exception e) {
            throw new ExecutionException(e);
        }
    }

    private static void removeRepeatService(Project project, UserApp context) {

        List<NocalhostDevProcessHandler> processHandlers = JetDevExecutionManager.getInstance(project).get();
        for (NocalhostDevProcessHandler processHandler : processHandlers) {
            if (context.getId() == processHandler.appId) {
                if (!processHandler.isProcessTerminating()
                        && !processHandler.isProcessTerminated()) {
                    processHandler.destroyProcess();
                }
            }
        }
    }

    private void setContext(Project project) {
        List<UserProject> userProjectList = NocalhostAppListManager.getInstance(project).get();
        UserApp userApp = null;
        if (null != userProjectList) {
            for (UserProject userProject : userProjectList) {
                for (UserApp app : userProject.getApps()) {
                    if (app.getName().equals(getEnvironment().getRunProfile().getName())) {
                        if (Constants.DEBUG_STATUS_SUCCESSFUL.equals(app.getStatus())) {
                            userApp = app;
                            break;
                        }
                    }
                }
            }
        }
        NocalhostContextManager.getInstance(project).setContext(userApp);
    }

    private boolean isDebugExecutor() {
        return StringUtils.equals(DefaultDebugExecutor.EXECUTOR_ID, getEnvironment().getExecutor().getId());
    }

    private String startDebugPortForward(UserApp context) throws ExecutionException {
        NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);
        try {
            NhctlPortForwardStartOptions portForwardStartOptions =
                    new NhctlPortForwardStartOptions(String.valueOf(context.getId()),
                            context.getCluster(), context.getNamespace());
            portForwardStartOptions.setPort(String.valueOf(NhctlUtil.findAvailablePort()));
            nhctlCommand.startPortForward(portForwardStartOptions);
            return portForwardStartOptions.getPort();
        } catch (Exception e) {
            throw new ExecutionException(e);
        }
    }

    public void stopDebugPortForward() {
        NocalhostRunnerContext dev = refContext.get();
        NocalhostRunnerContext.Debug debug = dev.getDebug();
        if (debug == null) {
            return;
        }
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);
                UserApp userApp = dev.getContext();
                NhctlPortForwardEndOptions nhctlPortForwardEndOptions =
                        new NhctlPortForwardEndOptions(String.valueOf(userApp.getId()), userApp.getCluster(), userApp.getNamespace());
                nhctlPortForwardEndOptions.setPort(debug.getLocalPort());
                nhctlCommand.endPortForward(nhctlPortForwardEndOptions);
            } catch (Exception e) {
                LOG.error(e);
            }
        });
    }

    private boolean isSyncStatusIdle() throws Exception {

        UserApp userApp = NocalhostContextManager.getInstance(getEnvironment().getProject()).getContext();
        final NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);
        NhctlSyncStatusOptions nhctlSyncStatusOptions =
                new NhctlSyncStatusOptions(String.valueOf(userApp.getId()), userApp.getCluster(), userApp.getNamespace());
        String status = nhctlCommand.syncStatus(nhctlSyncStatusOptions);
        NhctlSyncStatus nhctlSyncStatus = DataUtils.GSON.fromJson(status, NhctlSyncStatus.class);

        if (StringUtils.equals(nhctlSyncStatus.getStatus(), "idle")) {

            ProgressManager.getInstance().run(new Task.Backgroundable(getEnvironment().getProject(), userApp.getName() + " Running", false) {
                @Override
                public void run(@NotNull ProgressIndicator progressIndicator) {
                    try {
                        nhctlSyncStatusOptions.setOverride(true);
                        nhctlCommand.syncStatus(nhctlSyncStatusOptions);
                        nhctlSyncStatusOptions.setWait(true);
                        nhctlSyncStatusOptions.setOverride(false);
                        nhctlCommand.syncStatus(nhctlSyncStatusOptions);
                    } catch (Exception ignored) {
                    }
                }
            });
        }

        return StringUtils.equals(nhctlSyncStatus.getStatus(), "idle");
    }

    private static String resolveRunCommand(ServiceContainer serviceContainer) {
        if (serviceContainer == null
                || serviceContainer.getDev() == null
                || serviceContainer.getDev().getCommand() == null
                || serviceContainer.getDev().getCommand().getRun() == null) {
            return null;
        }
        return String.join(" ", serviceContainer.getDev().getCommand().getRun());
    }

    private static String resolveDebugCommand(ServiceContainer serviceContainer) {
        if (serviceContainer == null
                || serviceContainer.getDev() == null
                || serviceContainer.getDev().getCommand() == null
                || serviceContainer.getDev().getCommand().getDebug() == null) {
            return null;
        }
        return String.join(" ", serviceContainer.getDev().getCommand().getDebug());
    }

    private static String resolveDebugPort(ServiceContainer serviceContainer) {
        if (serviceContainer == null
                || serviceContainer.getDev() == null
                || serviceContainer.getDev().getDebug() == null) {
            return null;
        }
        return serviceContainer.getDev().getDebug().getRemoteDebugPort();
    }

    public void startup() throws ExecutionException {
        var dev = refContext.get();
        if (dev == null) {
            throw new ExecutionException("Call prepare() before this method");
        }
        if (dev.getContainer().getDev().isHotReload()) {
            disposables.add(new HotReload(getEnvironment()).withExec());
        }
    }

    public void destroy() {
        disposables.forEach(Disposable::dispose);
        disposables.clear();
        stopDebugPortForward();
    }
}
