package com.sentinel.nocalhost.configuration;

import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.commands.data.ServiceContainer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@AllArgsConstructor
@Getter
@Setter
public class NocalhostRunnerContext {
    private Debug debug;
    private String shell;
    private Command command;
    private UserApp context;
    private ServiceContainer container;

    @AllArgsConstructor
    @Getter
    @Setter
    public static class Command {
        private String run;
        private String debug;
    }

    @AllArgsConstructor
    @Getter
    @Setter
    public static class Debug {
        private String remotePort;
        private String localPort;
    }
}

