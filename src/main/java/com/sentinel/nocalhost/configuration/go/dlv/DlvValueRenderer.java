package com.sentinel.nocalhost.configuration.go.dlv;

import com.intellij.xdebugger.frame.XValueNode;
import com.intellij.xdebugger.frame.presentation.XValuePresentation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.concurrency.Promise;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: DlvValueRenderer.class */
public interface DlvValueRenderer {
    @NotNull
    Promise<XValuePresentation> getPresentation(@NotNull XValueNode xValueNode, @NotNull DlvXValue dlvXValue);
}
