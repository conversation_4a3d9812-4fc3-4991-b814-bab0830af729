package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.dlv.DlvVm;
import com.goide.i18n.GoBundle;
import com.goide.psi.GoNamedElement;
import com.goide.psi.GoParamDefinition;
import com.goide.psi.GoReferenceExpression;
import com.goide.psi.GoStatement;
import com.goide.psi.GoTypeSpec;
import com.goide.psi.GoVarDeclaration;
import com.goide.psi.GoVarDefinition;
import com.goide.psi.GoVarSpec;
import com.goide.psi.impl.GoElementFactory;
import com.goide.psi.impl.GoPsiImplUtil;
import com.goide.psi.impl.GoReference;
import com.goide.refactor.GoNamesValidator;
import com.goide.stubs.index.GoTypesIndex;
import com.goide.utils.GoStringUtil;
import com.intellij.icons.AllIcons;
import com.intellij.lang.ASTNode;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.project.DumbService;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Ref;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.psi.ResolveState;
import com.intellij.psi.formatter.FormatterUtil;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.util.ObjectUtils;
import com.intellij.util.ThreeState;
import com.intellij.util.concurrency.NonUrgentExecutor;
import com.intellij.util.containers.ContainerUtil;
import com.intellij.xdebugger.XDebugSession;
import com.intellij.xdebugger.XDebuggerUtil;
import com.intellij.xdebugger.XExpression;
import com.intellij.xdebugger.XSourcePosition;
import com.intellij.xdebugger.evaluation.XInstanceEvaluator;
import com.intellij.xdebugger.frame.*;
import com.intellij.xdebugger.frame.presentation.XErrorValuePresentation;
import com.intellij.xdebugger.frame.presentation.XValuePresentation;
import com.intellij.xdebugger.impl.ui.XDebuggerUIConstants;
import com.intellij.xdebugger.impl.ui.tree.nodes.XValueNodeImpl;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.swing.Icon;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import one.util.streamex.StreamEx;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.annotations.TestOnly;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;

public class DlvXValue extends XNamedValue {
    private final @Nullable DlvXValue myParent;
    @NotNull
    private volatile DlvApi.@NotNull Variable myVariable;
    final long myGoroutineId;
    private final Icon myIcon;
    private final DlvDebugProcess myProcess;
    final int myFrameId;
    @TestOnly
    private final AtomicInteger myLoadedChildren;
    private final @Nullable DlvXValue myFollowedPointer;
    private volatile int myCallResultCount;

    private DlvXValue(@NotNull String name, @NotNull DlvDebugProcess process, @Nullable DlvXValue parent, @NotNull DlvApi.@NotNull Variable variable, int frameId, long goroutineId, @Nullable Icon icon, @Nullable DlvXValue followedPointer) {


        super(name);
        this.myLoadedChildren = new AtomicInteger(0);
        this.myCallResultCount = -1;
        this.myProcess = process;
        this.myParent = parent;
        this.myVariable = variable;
        this.myGoroutineId = goroutineId;
        this.myIcon = icon;
        this.myFrameId = frameId;
        this.myFollowedPointer = followedPointer;
    }

    public static @NotNull DlvXValue create(@NotNull DlvDebugProcess process, @Nullable DlvXValue parent, @NotNull DlvApi.@NotNull Variable variable, int frameId, long goroutineId, @Nullable Icon icon) {

        return create(variable.name, process, parent, variable, frameId, goroutineId, icon);
    }

    public static @NotNull DlvXValue create(@NotNull String name, @NotNull DlvDebugProcess process, @Nullable DlvXValue parent, @NotNull DlvApi.@NotNull Variable variable, int frameId, long goroutineId, @Nullable Icon icon) {
   

        return createInner(name, process, parent, variable, frameId, goroutineId, icon);
    }

    public static @NotNull XValue createCallResult(@NotNull String expression, @NotNull List<DlvApi.Variable> callResults, @NotNull DlvDebugProcess process, int frameId, long goroutineId) {
 
        DlvXValue val;
        switch (callResults.size()) {
            case 0:
                val = createVoidCallResult(expression, process, frameId, goroutineId);
                val.myCallResultCount = 0;
                break;
            case 1:
                val = create(expression, process, null, callResults.get(0), frameId, goroutineId, AllIcons.Debugger.Watch);
                val.myCallResultCount = 1;
                break;
            default:
                DlvApi.Variable resultVar = new DlvApi.Variable();
                resultVar.type = "";
                resultVar.value = "";
                resultVar.name = "result";
                resultVar.type = callResults.stream().map((it) -> it.type).collect(Collectors.joining(", "));
                resultVar.children = callResults.toArray(new DlvApi.Variable[0]);
                val = create(expression, process, null, resultVar, frameId, goroutineId, AllIcons.Debugger.Watch);
                val.myCallResultCount = resultVar.children.length;
        }

        return val;
    }

    private static @NotNull DlvXValue createVoidCallResult(final @NotNull String expression, @NotNull DlvDebugProcess process, int frameId, long goroutineId) {


        final DlvApi.Variable resultVar = new DlvApi.Variable();
        resultVar.type = "";
        resultVar.name = "result";
        resultVar.value = "undefined";
        return new DlvXValue(expression, process, null, resultVar, frameId, goroutineId, null, null) {
            public String getEvaluationExpression() {
                return expression;
            }

            public void computePresentation(@NotNull XValueNode node, @NotNull XValuePlace place) {


                XValuePresentation p = new XValuePresentation() {
                    public void renderValue(@NotNull XValuePresentation.@NotNull XValueTextRenderer renderer) {


                        renderer.renderValue(resultVar.value);
                    }
                };
                node.setPresentation(null, p, false);
            }
        };
    }

    private static @NotNull DlvXValue createInner(@NotNull String name, @NotNull DlvDebugProcess process, @Nullable DlvXValue parent, @NotNull DlvApi.@NotNull Variable variable, int frameId, long goroutineId, @Nullable Icon icon) {

        DlvXValue followedPointer = null;

        DlvApi.Variable v;
        for(v = variable; (v.isPtr() || v.isInterface()) && v.children.length == 1; v = v.children[0]) {
            followedPointer = new DlvXValue(name, process, parent, v, frameId, goroutineId, icon, followedPointer);
        }

        if (isTruncatedString(v) && followedPointer != null) {

            return followedPointer;
        } else {
            return new DlvXValue(name, process, parent, v, frameId, goroutineId, icon, followedPointer);
        }
    }

    private static boolean isTruncatedString(@NotNull DlvApi.@NotNull Variable v) {


        return v.isString() && v.len != 0L && v.value.isEmpty();
    }

    public void computePresentation(@NotNull XValueNode node, @NotNull XValuePlace place) {


        this.computePresentation(node).onSuccess((presentation) -> {
            int maxStringLen = JetDevSettings.getInstance().maxStringLen;
            boolean isPartiallyLoadedString = this.myVariable.isString() && this.myVariable.len > (long)maxStringLen && this.myVariable.len < 10485760L;
            if (!this.isPartOfCallResult() && (isPartiallyLoadedString || isNonEmptyByteSliceOrArray(this.myVariable) && presentation instanceof DlvValueRenderers.DefaultSliceOrArrayPresentation)) {
                node.setFullValueEvaluator(new DlvXValue.MyXFullValueEvaluator());
            }

            node.setPresentation(this.getIcon(), presentation, this.hasChildren());
        }).onError((error) -> {
            DlvVm.LOG.debug("Failed to compute debug node presentation", error);
            String errorMessage = GoBundle.message("go.debugger.failed.to.compute.node.presentation");
            node.setPresentation(this.getIcon(), new XErrorValuePresentation(errorMessage), false);
        });
    }

    private static boolean isNonEmptyByteSliceOrArray(@NotNull DlvApi.@NotNull Variable v) {


        if (v.len != 0L && (v.isArray() || v.isSlice())) {
            DlvApi.Variable child = v.children != null && v.children.length > 0 ? v.children[0] : null;
            return child != null && child.isUint8();
        } else {
            return false;
        }
    }

    private boolean isPartOfCallResult() {
        DlvXValue value = this;

        while(!value.isCallResult()) {
            value = value.myParent;
            if (value == null) {
                return false;
            }
        }

        return true;
    }

    private boolean isCallResult() {
        return this.myCallResultCount > -1;
    }

    private @NotNull Icon getIcon() {
        Icon var10000;
        if (this.myIcon != null) {
            var10000 = this.myIcon;

            return var10000;
        } else if (!this.myVariable.isNumber() && !this.myVariable.isString() && !this.myVariable.isBool()) {
            if (!this.myVariable.isArray() && !this.myVariable.isSlice()) {
                var10000 = AllIcons.Debugger.Value;


                return var10000;
            } else {
                var10000 = AllIcons.Debugger.Db_array;


                return var10000;
            }
        } else {
            var10000 = AllIcons.Debugger.Db_primitive;

            return var10000;
        }
    }

    private boolean hasChildren() {
        return this.myVariable.children != null && this.myVariable.children.length > 0 || isCompleteVariable(this.myVariable);
    }

    private static byte parseUint8(@NotNull String value) {


        int n = Integer.parseInt(value);
        if (n >= 0 && n <= 255) {
            return (byte)(n & 255);
        } else {
            throw new RuntimeException("Uint8 value out of range: " + value);
        }
    }

    public @Nullable String getEvaluationExpression() {
        return this.getEvaluationExpression(true);
    }

    private @NotNull String getEvaluationExpression(boolean traverseParents) {
        String name = this.getTopLevelVariableName(traverseParents);
        if (StringUtil.isNotEmpty(name)) {


            return name;
        } else {
            return "*(*\"" + this.myVariable.realType + "\")(" + this.myVariable.addr + ")";
        }
    }

    private @Nullable String getTopLevelVariableName(boolean traverseParents) {
        if (!this.isFollowedPointer() && this.myParent == null) {
            return StringUtil.nullize(this.myVariable.name);
        } else if (!traverseParents) {
            return null;
        } else {
            DlvXValue child = this;

            for(DlvXValue parent = this.myFollowedPointer; parent != null; parent = parent.myFollowedPointer) {
                DlvApi.Variable parentVar = parent.myVariable;
                if (!parentVar.isInterface() && !parentVar.isPtr() || parentVar.children.length != 1 || child.myVariable != parentVar.children[0]) {
                    break;
                }

                if (!parent.isFollowedPointer() && parent.myParent == null) {
                    return StringUtil.nullize(parentVar.name);
                }

                child = parent;
            }

            return null;
        }
    }

    public void computeChildren(@NotNull XCompositeNode node) {


        this.computeChildren(node, 0);
    }

    public void computeChildren(@NotNull XCompositeNode node, int startFrom) {

        DlvApi.Variable variable = this.myVariable;
        if (this.isCallResult()) {
            if (startFrom < variable.children.length) {
                this.computeChildrenInner(node, variable, variable.children, 0);
            } else if (isCompleteVariable(this.myVariable)) {
                XValueChildrenList list = getxValueChildrenList();
                node.addChildren(list, true);
            }

        } else {
            String expression;
            if (isCompleteVariable(variable)) {
                if ((variable.isArray() || variable.isMap() || variable.isSlice() || variable.isStructure()) && variable.children != null && variable.children.length <= startFrom) {
                    int startOffset = variable.isMap() ? startFrom / 2 : startFrom;
                    expression = variable.isStructure() ? evaluationExpression(variable) : "(" + evaluationExpression(variable) + ")[" + startOffset + ":]";
                    this.myProcess.send(new DlvRequest.Eval(expression, this.myFrameId, this.myGoroutineId)).onSuccess((v) -> this.computeChildrenInner(node, variable, v.children, startFrom)).onError((t) -> {
                        DlvVm.LOG.warn("Cannot load rest children: " + expression, t);
                        super.computeChildren(node);
                    });
                    return;
                }

                if ((variable.isPtr() || variable.isInterface()) && !isZero(variable.addr) && variable.children.length == 0 && variable.onlyAddr) {
                    expression = evaluationExpression(variable);
                    this.myProcess.send(new DlvRequest.Eval(expression, this.myFrameId, this.myGoroutineId)).onSuccess((v) -> {
                        if (v.children.length == 1 && isZero(v.children[0].addr) && StringUtil.isEmpty(v.children[0].value)) {
                            v.name = "";
                            v.type = "";
                            this.computeChildrenInner(node, variable, new DlvApi.Variable[]{v}, startFrom);
                        } else {
                            this.computeChildrenInner(node, variable, v.children, startFrom);
                        }

                    }).onError((t) -> {
                        DlvVm.LOG.warn("Cannot load rest children: " + expression, t);
                        super.computeChildren(node);
                    });
                    return;
                }
            } else if (BigInteger.ZERO.equals(variable.addr) && variable.children != null && variable.children.length > 0 && (variable.isSlice() || variable.isArray())) {
                this.computeChildrenInner(node, variable, variable.children, 0);
                return;
            }

            expression = evaluationExpression(variable);
            this.myProcess.send(new DlvRequest.Eval(expression, this.myFrameId, this.myGoroutineId)).onSuccess((v) -> {
                this.myVariable = v;
                this.computeChildrenInner(node, v, v.children, 0);
            }).onError((t) -> {
                DlvVm.LOG.warn("Cannot evaluate nested variable: " + expression, t);
                super.computeChildren(node);
            });
        }
    }

    @NotNull
    private static XValueChildrenList getxValueChildrenList() {
        XValueChildrenList list = new XValueChildrenList(1);
        list.add(new XNamedValue("error") {
            public void computePresentation(@NotNull XValueNode node, @NotNull XValuePlace place) {


                node.setPresentation(XDebuggerUIConstants.ERROR_MESSAGE_ICON, new XValuePresentation() {
                    public void renderValue(@NotNull XValuePresentation.@NotNull XValueTextRenderer renderer) {

                        renderer.renderError(GoBundle.message("go.debugger.cannot.load.remaining.call.result.children"));
                    }
                }, false);
            }
        });
        return list;
    }

    static @NotNull String evaluationExpression(@NotNull DlvApi.@NotNull Variable variable) {


        StringBuilder expression = new StringBuilder("*(*\"");
        GoStringUtil.escapeString(variable.realType, expression);


        return expression.append("\")(").append(variable.addr).append(")").toString();
    }

    private static boolean isCompleteVariable(@NotNull DlvApi.@NotNull Variable variable) {
        if (isUnreadable(variable)) {
            return false;
        } else if (!variable.isPtr() && !variable.isInterface()) {
            if (!variable.isStructure() && !variable.isArray() && !variable.isSlice()) {
                if (!variable.isMap()) {
                    return false;
                } else {
                    return variable.onlyAddr || (long) (variable.children.length / 2) != variable.len;
                }
            } else {
                return variable.onlyAddr || (variable.len != 0L && (long) variable.children.length != variable.len);
            }
        } else {
            return variable.children.length == 0 || (variable.children.length == 1 && variable.children[0].onlyAddr && !isZero(variable.addr));
        }
    }

    private void computeChildrenInner(@NotNull XCompositeNode node, @NotNull DlvApi.@NotNull Variable variable, DlvApi.Variable @Nullable [] childrenToAdd, int startIndexInParent) {


        if (childrenToAdd != null && childrenToAdd.length != 0) {
            int nextBatchStart = startIndexInParent + childrenToAdd.length;
            Icon icon = variable.isStructure() ? AllIcons.Nodes.Field : null;
            XValueChildrenList list;
            int remaining;
            if (variable.isMap()) {
                list = new XValueChildrenList(childrenToAdd.length / 2);

                int remainingEntriesCount;
                for(remaining = 0; remaining < childrenToAdd.length; remaining += 2) {
                    remainingEntriesCount = startIndexInParent + remaining;
                    String name = String.valueOf(remainingEntriesCount / 2);
                    DlvXValue k = create(name, this.myProcess, this, childrenToAdd[remaining], this.myFrameId, this.myGoroutineId, icon);
                    DlvXValue v = create(name, this.myProcess, this, childrenToAdd[remaining + 1], this.myFrameId, this.myGoroutineId, icon);
                    list.add(new DlvMapEntryXValue(name, k, v));
                    this.myLoadedChildren.addAndGet(2);
                }

                node.addChildren(list, true);
                remaining = (int)(this.myVariable.len * 2L) - nextBatchStart;
                if (remaining > 0) {
                    remainingEntriesCount = remaining / 2;
                    node.tooManyChildren(remainingEntriesCount, () -> this.computeChildren(node, nextBatchStart));
                }

                return;
            }

            list = new XValueChildrenList(childrenToAdd.length);

            for(remaining = 0; remaining < childrenToAdd.length; ++remaining) {
                DlvXValue value = !variable.isSlice() && !variable.isArray() ? create(this.myProcess, this, childrenToAdd[remaining], this.myFrameId, this.myGoroutineId, icon) : create(String.valueOf(startIndexInParent + remaining), this.myProcess, this, childrenToAdd[remaining], this.myFrameId, this.myGoroutineId, icon);
                list.add(value.getName(), value);
                this.myLoadedChildren.incrementAndGet();
            }

            node.addChildren(list, true);
            if (variable.isArray() || variable.isSlice()) {
                remaining = (int)this.myVariable.len - nextBatchStart;
                if (remaining > 0) {
                    node.tooManyChildren(remaining, () -> this.computeChildren(node, nextBatchStart));
                }
            }
        } else {
            super.computeChildren(node);
        }

    }

    public @Nullable XValueModifier getModifier() {
        if (this.myProcess.isRecorded()) {
            return null;
        } else {
            final String evalExpression = this.getEvaluationExpression(false);
            return new XValueModifier() {
                public void setValue(@NotNull XExpression expression, @NotNull XValueModifier.@NotNull XModificationCallback callback) {


                    DlvRequest<?> var10000;
                    if (DlvXValue.this.myVariable.isString()) {
                        var10000 = new DlvRequest.Call(evalExpression + "=" + DlvXValue.quote(expression.getExpression()));
                    } else {
                        var10000 = new DlvRequest.Set(evalExpression, expression.getExpression(), DlvXValue.this.myGoroutineId, DlvXValue.this.myFrameId);
                    }

                    DlvRequest<?> command = var10000;
                    DlvXValue.this.myProcess.send(command).onProcessed((o) -> {
                        if (o != null) {
                            callback.valueModified();
                        }
                    }).onError((throwable) -> callback.errorOccurred(throwable.toString()));
                }
            };
        }
    }

    private static @NotNull String quote(@NotNull String s) {


        if (s.startsWith("\"") && s.endsWith("\"")) {
            s = StringUtil.unquoteString(s);
        }


        return "\"" + StringUtil.escapeQuotes(s) + "\"";
    }

    private static boolean isUnreadable(@NotNull DlvApi.@NotNull Variable variable) {


        return StringUtil.isNotEmpty(StringUtil.trim(variable.unreadable));
    }

    boolean isUnreadable() {
        return isUnreadable(this.myVariable);
    }

    boolean isNil() {
        return this.myVariable.isChan() && this.myVariable.children.length == 0 || this.myVariable.isInterface() && isZero(this.myVariable.addr) || this.myVariable.isFunction() && StringUtil.isEmpty(this.myVariable.value) || (this.myVariable.isSlice() || this.myVariable.isMap()) && isZero(this.myVariable.base) && !this.hasChildren() || this.isFollowedPointer() && isZero(this.myVariable.addr) && StringUtil.isEmpty(this.myVariable.value);
    }

    @NotNull Promise<@NotNull XValuePresentation> computePresentation(@NotNull XValueNode node) {


        DlvStackFrame frame = ObjectUtils.tryCast(this.myProcess.getSession().getCurrentStackFrame(), DlvStackFrame.class);
        DlvValueRenderer renderer = this.myProcess.getRenderers().getRenderer(frame, node, this);


        return renderer.getPresentation(node, this).thenAsync((presentation) -> presentation != null ? Promises.resolvedPromise(presentation) : DlvValueRenderers.DEFAULT.getPresentation(node, this));
    }

    @NotNull Promise<@Nullable String> getTypePresentationAsync(@NotNull XValueNode node) {


        if (node.isObsolete()) {


            return Promises.rejectedPromise();
        } else {


            return ReadAction.nonBlocking(() -> node.isObsolete() ? null : this.getTypePresentation()).submit(NonUrgentExecutor.getInstance());
        }
    }

    private @Nullable String getTypePresentation() {
        JetDevSettings settings = JetDevSettings.getInstance();
        StringBuilder result = new StringBuilder();
        if (settings.showTypes) {
            StringUtil.join(this.getTypeParts(), " | ", result);
        }

        if (settings.showPointerAddresses) {
            BigInteger address = this.getPointerAddress();
            if (address != null) {
                if (!result.isEmpty()) {
                    result.append(" | ");
                }

                result.append("0x");
                result.append(address.toString(16));
            }
        }

        return StringUtil.nullize(result.toString());
    }

    private @Nullable BigInteger getPointerAddress() {
        DlvXValue pointer = this.getTopFollowedPointer();
        DlvApi.Variable pointerVar = pointer != null ? pointer.getVariable() : null;
        return pointerVar != null && pointerVar.isPtr() && pointerVar.children.length == 1 ? pointerVar.children[0].addr : null;
    }

    @NotNull Promise<@NotNull List<String>> getTypePartsAsync(@NotNull XValueNode node) {


        if (node.isObsolete()) {


            return Promises.rejectedPromise();
        } else {


            return ReadAction.nonBlocking(() -> node.isObsolete() ? null : this.getTypeParts()).submit(NonUrgentExecutor.getInstance());
        }
    }

    private @NotNull List<String> getTypeParts() {
        List<String> typesReversed = new ArrayList<>();
        DlvXValue value = this;

        for(DlvSymbolPrettier prettier = this.myProcess.getSymbolPrettier(); value != null; value = value.myFollowedPointer) {
            DlvApi.Variable var = value.getVariable();
            if (var.isPtr()) {
                if (typesReversed.isEmpty()) {
                    typesReversed.add(prettier.prettifyType(var.type));
                } else {
                    typesReversed.set(typesReversed.size() - 1, prettier.prettifyType(var.type));
                }
            } else {
                typesReversed.add(prettier.prettifyType(var.type));
            }
        }

        Collections.reverse(typesReversed);
        if ("void".equals(ContainerUtil.getLastItem(typesReversed))) {
            typesReversed.remove(typesReversed.size() - 1);
        }

        return typesReversed;
    }

    @NotNull
    DlvApi.@NotNull Variable getVariable() {

        return this.myVariable;
    }

    static boolean isZero(@Nullable BigInteger o) {
        return BigInteger.ZERO.equals(o);
    }

    private @Nullable PsiElement findTargetElement(@NotNull Project project, @NotNull XSourcePosition position, @NotNull String name) {

        PsiFile file = PsiManager.getInstance(project).findFile(position.getFile());
        if (file == null) {
            return null;
        } else if (this.myParent != null && this.myParent.myVariable.isStructure()) {
            return this.findTargetField(project, this.myParent.myVariable.type, this.myName, file);
        } else {
            Document document = PsiDocumentManager.getInstance(project).getDocument(file);
            Ref<PsiElement> result = Ref.create();
            DlvXValue topmostParent = this.getTopmostParent();
            BigInteger declLine = (topmostParent != null ? topmostParent.myVariable : this.myVariable).declLine;
            int varLine = declLine != null ? declLine.intValue() - 1 : 0;
            DlvStackFrame.processIdentifiers(file, document, varLine, (identifier) -> {
                PsiElement parent = identifier.getParent();
                if ((parent instanceof GoVarDefinition || parent instanceof GoParamDefinition) && name.equals(((GoNamedElement)parent).getName())) {
                    result.set(parent);
                    return false;
                } else {
                    return true;
                }
            });
            if (!result.isNull()) {
                return result.get();
            } else if (DumbService.isDumb(project)) {
                return null;
            } else {
                int offset = varLine > 0 && document != null && varLine < document.getLineCount() ? document.getLineStartOffset(varLine) : position.getOffset();
                ASTNode leafElement = offset > 0 ? FormatterUtil.getNextNonWhitespaceLeaf(file.getNode().findLeafElementAt(offset)) : null;
                PsiElement visibleElement = leafElement != null ? leafElement.getPsi() : null;
                GoStatement statement = PsiTreeUtil.getNonStrictParentOfType(visibleElement, GoStatement.class);
                PsiElement context = ObjectUtils.chooseNotNull(statement != null ? statement.getNextSibling() : null, visibleElement);
                return context != null && GoNamesValidator.INSTANCE.isIdentifier(name, project) ? GoReference.create(name, context).resolve() : null;
            }
        }
    }

    private @Nullable DlvXValue getTopmostParent() {
        DlvXValue result = null;

        for(DlvXValue parent = this.myParent; parent != null; parent = parent.myParent) {
            result = parent;
        }

        return result;
    }

    @Nullable
    private PsiElement findTargetField(@NotNull Project project, @NotNull String structType, @NotNull String fieldName, @NotNull PsiFile file) {


        if (DumbService.isDumb(project)) {
            return null;
        } else {
            int index = structType.lastIndexOf(46);
            if (index != -1 && !structType.startsWith("struct {")) {
                String importPath = structType.substring(0, index);
                String typeName = structType.substring(index + 1);
                GoVarDeclaration var;
                ResolveState state;
                if ("main".equals(importPath)) {
                    VirtualFile mainFile = this.myProcess.getMainFile();
                    Document document = mainFile != null ? FileDocumentManager.getInstance().getDocument(mainFile) : null;
                    PsiFile mainPsiFile = document != null ? PsiDocumentManager.getInstance(project).getPsiFile(document) : null;
                    String text = "package main; var a=" + typeName + "{}." + fieldName;
                    var = mainPsiFile != null ? GoElementFactory.createElement(project, text, GoVarDeclaration.class, mainPsiFile) : null;
                    state = GoPsiImplUtil.createContextOnElement(mainPsiFile);
                } else {
                    String text = "package main; import pkg \"" + importPath + "\"; var a=pkg." + typeName + "{}." + fieldName;
                    var = GoElementFactory.createElement(project, text, GoVarDeclaration.class);
                    state = GoPsiImplUtil.createContextOnElement(file);
                }

                List<GoVarSpec> varSpecs = var != null ? var.getVarSpecList() : null;
                GoVarSpec spec = varSpecs != null && !varSpecs.isEmpty() ? varSpecs.get(0) : null;
                GoReferenceExpression expression = ObjectUtils.tryCast(ContainerUtil.getFirstItem(spec != null ? spec.getExpressionList() : null), GoReferenceExpression.class);
                return expression != null ? expression.resolve(state) : null;
            } else {
                return null;
            }
        }
    }

    public void computeSourcePosition(final @NotNull XNavigatable navigatable) {

        readActionInPooledThread(new Runnable() {
            public void run() {
                navigatable.setSourcePosition(this.findPosition());
            }

            private @Nullable XSourcePosition findPosition() {
                XDebugSession debugSession = DlvXValue.this.getSession();
                if (debugSession == null) {
                    return null;
                } else {
                    XStackFrame stackFrame = debugSession.getCurrentStackFrame();
                    if (stackFrame == null) {
                        return null;
                    } else {
                        Project project = debugSession.getProject();
                        XSourcePosition position = debugSession.getCurrentPosition();
                        if (position == null) {
                            return null;
                        } else {
                            String name = !DlvXValue.this.myName.startsWith("*") && !DlvXValue.this.myName.startsWith("&") ? DlvXValue.this.myName : DlvXValue.this.myName.substring(1);
                            PsiElement resolved = DlvXValue.this.findTargetElement(project, position, name);
                            if (resolved == null) {
                                return null;
                            } else {
                                PsiFile containingFile = resolved.getContainingFile();
                                if (containingFile == null) {
                                    return null;
                                } else {
                                    VirtualFile virtualFile = containingFile.getViewProvider().getVirtualFile();
                                    return XDebuggerUtil.getInstance().createPositionByOffset(virtualFile, resolved.getTextOffset());
                                }
                            }
                        }
                    }
                }
            }
        });
    }

    private static void readActionInPooledThread(@NotNull Runnable runnable) {


        ApplicationManager.getApplication().executeOnPooledThread(() -> ApplicationManager.getApplication().runReadAction(runnable));
    }

    @Nullable Project getProject() {
        XDebugSession session = this.getSession();
        return session != null ? session.getProject() : null;
    }

    @Nullable XDebugSession getSession() {
        return this.myProcess.getSession();
    }

    @NotNull DlvDebugProcess getProcess() {

        return this.myProcess;
    }

    public @NotNull ThreeState computeInlineDebuggerData(@NotNull XInlineDebuggerDataCallback callback) {

        ThreeState threeState;
        if (this.myParent != null && this.myParent.myVariable.isStructure()) {
            threeState = ThreeState.NO;


        } else {
            Objects.requireNonNull(callback);
            this.computeSourcePosition(callback::computed);
            threeState = ThreeState.YES;


        }
        return threeState;
    }

    public boolean canNavigateToSource() {
        return true;
    }

    public boolean canNavigateToTypeSource() {
        return (this.myVariable.isStructure() || this.myVariable.isPtr() || this.myVariable.isInterface() || this.myVariable.isFunction()) && this.getProject() != null;
    }

    public void computeTypeSourcePosition(@NotNull XNavigatable navigatable) {

        readActionInPooledThread(() -> {
            boolean isStructure = this.myVariable.isStructure();
            boolean isInterface = this.myVariable.isInterface();
            boolean isFunction = this.myVariable.isFunction();
            boolean isPtr = this.myVariable.isPtr();
            if (isStructure || isPtr || isInterface || isFunction) {
                Project project = this.getProject();
                if (project != null) {
                    String typeName = this.myVariable.type;
                    String fullName = (isPtr && typeName.startsWith("*") ? typeName.substring(1) : typeName).replaceFirst(isPtr ? "\\*struct " : "{2}struct ", "");
                    DlvNamedType dlvNamedType = DlvNamedType.parse(fullName);
                    String name = dlvNamedType != null ? dlvNamedType.getTypeName() : null;
                    if (StringUtil.isNotEmpty(name)) {
                        GlobalSearchScope scope = dlvNamedType.isFromMainPackage() ? this.myProcess.getMainPackageScope() : GlobalSearchScope.allScope(project);
                        if (scope == null) {
                            return;
                        }

                        GoTypesIndex.process(name, project, scope, null, (type) -> {
                            if (inPackagePath(type, dlvNamedType.getPackagePath())) {
                                navigatable.setSourcePosition(XDebuggerUtil.getInstance().createPositionByOffset(type.getContainingFile().getVirtualFile(), type.getTextOffset()));
                                return false;
                            } else {
                                return true;
                            }
                        });
                    }

                }
            }
        });
    }

    private static boolean inPackagePath(@NotNull GoTypeSpec type, @NotNull String packagePath) {


        return StringUtil.isEmpty(packagePath) || "main".equals(packagePath) || Objects.equals(type.getContainingFile().getImportPath(false), packagePath);
    }

    static @Nullable DlvXValue getNodeValue(@NotNull XValueNode node) {


        DlvXValue var10000;
        if (node instanceof XValueNodeImpl valueNode) {
            XValueContainer var3 = valueNode.getValueContainer();
            if (var3 instanceof DlvXValue value) {
                var10000 = value;
                return var10000;
            }
        }

        return null;
    }

    int getCallResultCount() {
        return this.myCallResultCount;
    }

    public @Nullable XInstanceEvaluator getInstanceEvaluator() {
        return (callback, frame) -> {


            DlvStackFrame dlvStackFrame = ObjectUtils.tryCast(frame, DlvStackFrame.class);
            if (dlvStackFrame != null) {
                callback.evaluated(new DlvXValue(DlvXValue.this.myName, DlvXValue.this.myProcess, DlvXValue.this.myParent, DlvXValue.this.myVariable, dlvStackFrame.getFrameId(), dlvStackFrame.getGoroutineId(), DlvXValue.this.myIcon, DlvXValue.this.myFollowedPointer));
            } else {
                callback.evaluated(DlvXValue.this);
            }

        };
    }

    private boolean isFollowedPointer() {
        return this.myFollowedPointer != null;
    }

    @Nullable DlvXValue getTopFollowedPointer() {
        DlvXValue result;
        DlvXValue p;
        for(result = this.myFollowedPointer; result != null; result = p) {
            p = result.myFollowedPointer;
            if (p == null) {
                break;
            }
        }

        return result;
    }

    private class MyXFullValueEvaluator extends XFullValueEvaluator {
        private MyXFullValueEvaluator() {
        }

        public void startEvaluation(@NotNull XFullValueEvaluator.@NotNull XFullValueEvaluationCallback callback) {

            if (!callback.isObsolete()) {
                String evaluationExpression = DlvXValue.this.getEvaluationExpression();
                if (evaluationExpression == null) {
                    callback.errorOccurred(GoBundle.message("go.debugger.cannot.calculate.value"));
                } else {
                    DlvXValue.this.myProcess.send(new DlvRequest.Eval(evaluationExpression, DlvXValue.this.myFrameId, DlvXValue.this.myGoroutineId, DlvXValue.this.myVariable.len)).onSuccess((v) -> callback.evaluated(callback.isObsolete() ? GoBundle.message("go.debugger.obsolete.value") : this.calcString(v))).onError((e) -> callback.errorOccurred(e.getMessage()));
                }
            }
        }

        @NotNull String calcString(@NotNull DlvApi.@NotNull Variable v) {

            String var10000;
            if (!DlvXValue.this.myVariable.isString()) {
                if (DlvXValue.isNonEmptyByteSliceOrArray(v)) {
                    try {
                        return new String(StreamEx.of(v.children).mapToInt((child) -> DlvXValue.parseUint8(child.value)).toByteArray(), StandardCharsets.UTF_8);
                    } catch (Exception var3) {
                        var10000 = GoBundle.message("go.debugger.cannot.calculate.string.presentation", var3.getMessage());


                        return var10000;
                    }
                } else {
                    String msg = GoBundle.message("go.debugger.unsupported.type", DlvXValue.this.myVariable.realType);

                    assert false : msg;


                    return msg;
                }
            } else if (DlvXValue.this.isFollowedPointer() && !v.isString()) {
                while((v.isInterface() || v.isPtr()) && v.children.length == 1) {
                    DlvApi.Variable firstChild = v.children[0];
                    if (firstChild.isString()) {
                        var10000 = firstChild.value;


                        return var10000;
                    }

                    v = firstChild;
                }

                return "";
            } else {
                var10000 = v.value;


                return var10000;
            }
        }
    }
}
