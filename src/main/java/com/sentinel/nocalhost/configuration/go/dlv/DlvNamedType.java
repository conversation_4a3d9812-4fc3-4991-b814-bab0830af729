package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.dlv.DlvPackagePath;
import com.intellij.openapi.util.text.StringUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: DlvNamedType.class */
public final class DlvNamedType {
    private final DlvPackagePath myDlvPackagePath;
    private final String myTypeName;


    private DlvNamedType(@NotNull DlvPackagePath dlvPackagePath, @NotNull String typeName) {

        this.myDlvPackagePath = dlvPackagePath;
        this.myTypeName = typeName;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public DlvPackagePath getDlvPackagePath() {

        return this.myDlvPackagePath;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public String getPackagePath() {

        return this.myDlvPackagePath.getPackagePath();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public String getTypeName() {

        return this.myTypeName;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public boolean isFromMainPackage() {
        return "main".equals(getPackagePath()) && !isFromTestPackage();
    }

    boolean isFromTestPackage() {
        return this.myDlvPackagePath.isTestPackage();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public static DlvNamedType parse(@NotNull String dlvTypeString) {

        String dlvTypeString2 = StringUtil.trimStart(dlvTypeString, "*");
        if (StringUtil.containsAnyChar(dlvTypeString2, "{}[]() ,;<")) {
            return null;
        }
        int lastDot = dlvTypeString2.lastIndexOf(46);
        if (lastDot == -1) {
            return new DlvNamedType(DlvPackagePath.getEmptyPackagePath(), dlvTypeString2);
        }
        DlvPackagePath prefix = DlvPackagePath.parse(dlvTypeString2.substring(0, lastDot));
        if (prefix != null) {
            return new DlvNamedType(prefix, dlvTypeString2.substring(lastDot + 1));
        }
        return null;
    }
}
