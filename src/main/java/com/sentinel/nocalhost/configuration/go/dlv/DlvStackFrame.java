package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.GoIcons;
import com.goide.GoTypes;
import com.goide.dlv.DlvStackFuncName;
import com.goide.i18n.GoBundle;
import com.goide.psi.GoForClause;
import com.goide.psi.GoVarDefinition;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.util.NotNullLazyValue;
import com.intellij.openapi.util.Pair;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.ui.ColoredTextContainer;
import com.intellij.ui.SimpleTextAttributes;
import com.intellij.util.BitUtil;
import com.intellij.util.ObjectUtils;
import com.intellij.util.Processor;
import com.intellij.xdebugger.XDebuggerBundle;
import com.intellij.xdebugger.XDebuggerUtil;
import com.intellij.xdebugger.XSourcePosition;
import com.intellij.xdebugger.evaluation.XDebuggerEvaluator;
import com.intellij.xdebugger.frame.XCompositeNode;
import com.intellij.xdebugger.frame.XStackFrame;
import com.intellij.xdebugger.frame.XValueChildrenList;
import com.intellij.xdebugger.impl.frame.XDebuggerFramesList;
import com.intellij.xdebugger.settings.XDebuggerSettingsManager;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: DlvStackFrame.class */
public class DlvStackFrame extends XStackFrame implements XDebuggerFramesList.ItemWithSeparatorAbove {
    private final DlvDebugProcess myProcess;
    private final DlvApi.Location myLocation;
    private final DlvStackFuncName myDlvStackFuncName;
    private final long myGoroutineId;
    private final int myFrameId;

    @Nullable
    private volatile String myErr;
    private final boolean myAsync;
    private final XSourcePosition mySourcePosition;



    /* JADX INFO: Access modifiers changed from: package-private */
    public DlvStackFrame(@NotNull DlvDebugProcess process, @NotNull DlvApi.Location location, @NotNull DlvStackFuncName stackFuncName, long goroutineId, int frameId, @Nullable String err, boolean async) {

        this.myProcess = process;
        this.myLocation = location;
        this.myDlvStackFuncName = stackFuncName;
        this.myGoroutineId = goroutineId;
        this.myFrameId = frameId;
        this.myErr = err;
        this.myAsync = async;
        this.mySourcePosition = computeSourcePosition();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public boolean hasErr() {
        return this.myErr != null;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public void setErr(@Nullable String err) {
        this.myErr = err;
    }

    public boolean hasSeparatorAbove() {
        return this.myAsync;
    }

    public String getCaptionAboveOf() {
        return GoBundle.message("go.debugger.async.stacktrace.caption", new Object[0]);
    }

    @Nullable
    public XDebuggerEvaluator getEvaluator() {
        if (this.myAsync) {
            return null;
        }
        return new DlvExpressionEvaluator(this.myProcess, this.myLocation, this.myFrameId, this.myGoroutineId);
    }

    @Nullable
    public Object getEqualityObject() {
        return this.myLocation.name();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public long getGoroutineId() {
        return this.myGoroutineId;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public int getFrameId() {
        return this.myFrameId;
    }

    @Nullable
    public XSourcePosition getSourcePosition() {
        return this.mySourcePosition;
    }

    @Nullable
    public XSourcePosition computeSourcePosition() {
        VirtualFile file = this.myProcess.findFile(this.myLocation);
        if (file == null) {
            return null;
        }
        return XDebuggerUtil.getInstance().createPosition(file, this.myLocation.line - 1);
    }

    public void customizePresentation(@NotNull ColoredTextContainer component) {

        XSourcePosition position = getSourcePosition();
        if (position != null) {
            component.append(this.myDlvStackFuncName.getFuncName(), SimpleTextAttributes.REGULAR_ATTRIBUTES);
            component.append(" (" + position.getFile().getName() + ":" + (position.getLine() + 1) + ")", SimpleTextAttributes.GRAY_ATTRIBUTES);
            if (StringUtil.isNotEmpty(this.myDlvStackFuncName.getPackageImportPath())) {
                component.append(" " + this.myDlvStackFuncName.getPackageImportPath(), SimpleTextAttributes.GRAY_ITALIC_ATTRIBUTES);
                return;
            }
            return;
        }
        String file = this.myLocation.file;
        if (file != null) {
            component.append(file + ":" + (this.myLocation.line + 1), SimpleTextAttributes.GRAYED_ATTRIBUTES);
        } else {
            component.append(XDebuggerBundle.message("invalid.frame"), SimpleTextAttributes.ERROR_ATTRIBUTES);
        }
    }

    public void computeChildren(@NotNull XCompositeNode node) {

        String err = this.myErr;
        if (err != null) {
            node.setErrorMessage(err);
        }
        if (this.myAsync) {
            node.addChildren(XValueChildrenList.EMPTY, true);
            return;
        }
        VirtualFile file = this.myProcess.findFile(this.myLocation);
        Pair<Document, PsiFile> docAndFile = getDocumentAndFile(file);
        Document document = docAndFile.first;
        PsiFile psiFile = docAndFile.second;
        List<Pair<String, DlvXValue>> vars = new ArrayList<>();
        NotNullLazyValue<List<String>> loopVars = NotNullLazyValue.createValue(() -> getVarNames(psiFile, document, this.myLocation.line - 1, var -> PsiTreeUtil.getParentOfType(var, GoForClause.class) != null));
        this.myProcess.send(new DlvRequest.ListLocalVars(this.myGoroutineId, this.myFrameId)).thenAsync(variables -> {
            for (DlvApi.Variable v : variables) {
                DlvXValue xValue = DlvXValue.create(this.myProcess, null, v, this.myFrameId, this.myGoroutineId, null);
                if (!xValue.isUnreadable()) {
                    BigInteger varLine = ObjectUtils.notNull(v.declLine, BigInteger.ZERO);
                    if (varLine.compareTo(BigInteger.valueOf(this.myLocation.line)) < 0 || psiFile == null || loopVars.getValue().contains(v.name)) {
                        vars.add(Pair.create(getVariableName(v, xValue), xValue));
                    }
                }
            }
            return this.myProcess.send(new DlvRequest.ListFunctionArgs(this.myGoroutineId, this.myFrameId));
        }).onSuccess(args -> {
            XValueChildrenList xVars = new XValueChildrenList(vars.size() + args.size());
            for (DlvApi.Variable v : args) {
                if (v.name == null || !v.name.startsWith("~r")) {
                    DlvXValue xValue = DlvXValue.create(this.myProcess, null, v, this.myFrameId, this.myGoroutineId, GoIcons.PARAMETER);
                    if (!xValue.isUnreadable()) {
                        xVars.add(getVariableName(v, xValue), xValue);
                    }
                }
            }
            if (!XDebuggerSettingsManager.getInstance().getDataViewSettings().isSortValues()) {
                vars.sort(createLocalVarComparator(psiFile, document));
            }
            for (Pair<String, DlvXValue> var : vars) {
                xVars.add(var.first, var.second);
            }
            node.addChildren(xVars, true);
        }).onError(th -> node.setMessage(th.getMessage(), AllIcons.General.Warning, SimpleTextAttributes.REGULAR_ATTRIBUTES, null));
    }

    private Pair<Document, PsiFile> getDocumentAndFile(@Nullable VirtualFile file) {
        return ReadAction.compute(() -> {
            Document document = file != null ? FileDocumentManager.getInstance().getDocument(file) : null;
            PsiFile psiFile = document != null ? PsiDocumentManager.getInstance(this.myProcess.getSession().getProject()).getPsiFile(document) : null;
            return Pair.create(document, psiFile);
        });
    }

    @NotNull
    private static String getVariableName(@NotNull DlvApi.Variable v, @NotNull DlvXValue xValue) {
        String varName = xValue.getName();
        if (BitUtil.isSet(v.flags, 2)) {
            varName = varName + " (shadowed)";
        }
        return varName;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public DlvApi.Location getLocation() {
        return this.myLocation;
    }

    @NotNull
    private static Comparator<Pair<String, DlvXValue>> createLocalVarComparator(@Nullable PsiFile file, @Nullable Document document) {
        Map<Integer, List<String>> varNamesPerLine = new HashMap<>();
        return (o1, o2) -> {
            DlvApi.Variable var1 = o1.second.getVariable();
            DlvApi.Variable var2 = o2.second.getVariable();
            BigInteger line1 = ObjectUtils.notNull(var1.declLine, BigInteger.ZERO);
            BigInteger line2 = ObjectUtils.notNull(var2.declLine, BigInteger.ZERO);
            int byDeclLine = line1.compareTo(line2);
            if (byDeclLine != 0) {
                return byDeclLine;
            }
            try {
                int line = line1.intValueExact() - 1;
                List<String> varNames =  varNamesPerLine.computeIfAbsent(line, it -> getVarNames(file, document, it, __ -> true));
                return Integer.compare(varNames.indexOf(o1.first), varNames.indexOf(o2.first));
            } catch (ArithmeticException e) {
                return 0;
            }
        };
    }

    /* JADX INFO: Access modifiers changed from: private */
    @NotNull
    public static List<String> getVarNames(@Nullable PsiFile file, @Nullable Document document, int line, @NotNull Predicate<? super GoVarDefinition> predicate) {
        List<String> result = new ArrayList<>();
        processIdentifiers(file, document, line, identifier -> {
            GoVarDefinition definition = ObjectUtils.tryCast(identifier.getParent(), GoVarDefinition.class);
            if (definition != null && predicate.test(definition)) {
                result.add(definition.getName());
                return true;
            }
            return true;
        });
        return result;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public static void processIdentifiers(@Nullable PsiFile file, @Nullable Document document, int line, @NotNull Processor<? super PsiElement> processor) {
        if (file == null || document == null) {
            return;
        }
        ReadAction.run(() -> {
            try {
                int lineStart = document.getLineStartOffset(line);
                int lineEnd = document.getLineEndOffset(line);
                for (PsiElement at = file.findElementAt(lineStart); at != null; at = PsiTreeUtil.nextVisibleLeaf(at)) {
                    if (at.getTextOffset() >= lineEnd) {
                        break;
                    }
                    if (at.getNode().getElementType() == GoTypes.IDENTIFIER && !processor.process(at)) {
                        return;
                    }
                }
            } catch (IndexOutOfBoundsException ignored) {
            }
        });
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DlvStackFrame frame)) {
            return false;
        }
        if (this.myGoroutineId == frame.myGoroutineId && this.myFrameId == frame.myFrameId && this.myProcess.equals(frame.myProcess)) {
            return this.myLocation.equals(frame.myLocation);
        }
        return false;
    }

    public int hashCode() {
        int result = this.myProcess.hashCode();
        return (31 * ((31 * ((31 * result) + this.myLocation.hashCode())) + Long.hashCode(this.myGoroutineId))) + this.myFrameId;
    }
}
