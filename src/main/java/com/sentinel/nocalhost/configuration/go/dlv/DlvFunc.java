package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.dlv.DlvPackagePath;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: DlvFunc.class */
public class DlvFunc {
    private final DlvPackagePath myDlvPackagePath;
    private final String myName;

    private DlvFunc(@NotNull DlvPackagePath dlvPackagePath, @NotNull String name) {
        this.myDlvPackagePath = dlvPackagePath;
        this.myName = name;
    }

    @NotNull
    public DlvPackagePath getDlvPackagePath() {
        return this.myDlvPackagePath;
    }

    @NotNull
    public String getName() {

        return this.myName;
    }

    @Nullable
    private static String nameWithoutInst(@NotNull String func) {

        int start = func.indexOf(91);
        if (start == -1) {
            return func;
        }
        int end = func.lastIndexOf(93);
        if (end == -1 || end < start) {
            return null;
        }
        return func.substring(0, start) + func.substring(end + 1);
    }

    @Nullable
    public static DlvFunc parse(@NotNull String func) {

        String name = nameWithoutInst(func);
        if (name == null) {
            return null;
        }
        int functionStart = name.lastIndexOf(46);
        if (functionStart == -1) {
            return null;
        }
        int lastSlash = name.lastIndexOf(47);
        if (lastSlash == -1) {
            lastSlash = 0;
        }
        int packageEnd = name.indexOf(46, lastSlash);
        DlvPackagePath packagePath = packageEnd != -1 ? DlvPackagePath.parse(name.substring(0, packageEnd)) : null;
        if (packagePath != null) {
            return new DlvFunc(packagePath, name.substring(packageEnd + 1));
        }
        return null;
    }
}
