package com.sentinel.nocalhost.configuration.go.dlv.protocol;

import com.goide.dlv.JsonReaderEx;
import com.google.gson.stream.JsonToken;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.jsonProtocol.JsonType;
import org.jetbrains.jsonProtocol.Optional;

@JsonType
/* loaded from: DlvResponse.class */
public interface DlvResponse {

    @JsonType
    /* loaded from: DlvResponse$ErrorInfo.class */
    public interface ErrorInfo {
        @Nullable
        String message();

        @Optional
        @NotNull
        List<String> data();

        int code();
    }

    int id();

    @Optional
    @Nullable
    JsonReaderEx result();

    @Optional
    @Nullable
    ErrorInfo error();

    /* loaded from: DlvResponse$CommandResponseImpl.class */
    public static final class CommandResponseImpl implements DlvResponse {

        @NonNls
        private static final Set<String> INLINE_RESULTS = Set.of("Recorded", "DelveVersion", "Goroutines");

        @NonNls
        private static final String ERROR_ATTRIBUTE_NAME = "error";

        @NonNls
        private static final String ID_ATTRIBUTE_NAME = "id";

        @NonNls
        private static final String RESULT_ATTRIBUTE_NAME = "result";

        @Nullable
        private ErrorInfo _error;
        private int _id;

        @Nullable
        private JsonReaderEx _result;


        public CommandResponseImpl(@NotNull JsonReaderEx reader, @Nullable String name) {
            String nextNameOrNull;
            this._id = -1;
            if (name == null) {
                if (reader.hasNext() && reader.beginObject().hasNext()) {
                    name = reader.nextName();
                } else {
                    return;
                }
            }
            do {
                if (ERROR_ATTRIBUTE_NAME.equals(name)) {
                    this._error = new M5m(reader);
                } else if (ID_ATTRIBUTE_NAME.equals(name)) {
                    this._id = reader.nextInt();
                } else {
                    if (RESULT_ATTRIBUTE_NAME.equals(name)) {
                        if (reader.nextIsEmptyObject()) {
                            this._result = reader.subReader();
                        } else if (reader.peek() == JsonToken.BEGIN_OBJECT && reader.hasNext()) {
                            JsonReaderEx resultObject = reader.subReader();
                            if (reader.beginObject().hasNext()) {
                                if (reader.peek() == JsonToken.NAME) {
                                    String firstNameInResult = reader.nextName();
                                    this._result = INLINE_RESULTS.contains(firstNameInResult) ? resultObject : reader.subReader();
                                }
                                if (reader.peek() == JsonToken.END_OBJECT) {
                                    reader.endObject();
                                }
                            }
                        }
                    }
                    reader.skipValue();
                }
                nextNameOrNull = reader.nextNameOrNull();
                name = nextNameOrNull;
            } while (nextNameOrNull != null);
            reader.endObject();
        }

        @Override // com.goide.dlv.protocol.DlvResponse
        @Nullable
        public ErrorInfo error() {
            return this._error;
        }

        @Override // com.goide.dlv.protocol.DlvResponse
        public int id() {
            return this._id;
        }

        @Override // com.goide.dlv.protocol.DlvResponse
        @Nullable
        public JsonReaderEx result() {
            return this._result;
        }
    }

    /* loaded from: DlvResponse$M5m.class */
    final class M5m implements ErrorInfo {

        @Nullable
        private final String _message;

        M5m(@NotNull JsonReaderEx reader) {

            this._message = nextNullableString(reader);
        }

        @Override // com.goide.dlv.protocol.DlvResponse.ErrorInfo
        public int code() {
            return -1;
        }

        @Override // com.goide.dlv.protocol.DlvResponse.ErrorInfo
        @NotNull
        public List<String> data() {

            return Collections.emptyList();
        }

        @Override // com.goide.dlv.protocol.DlvResponse.ErrorInfo
        @Nullable
        public String message() {
            return this._message;
        }

        private static String nextNullableString(@NotNull JsonReaderEx reader) {

            if (reader.peek() == JsonToken.NULL) {
                reader.nextNull();
                return null;
            }
            return reader.nextString();
        }
    }
}
