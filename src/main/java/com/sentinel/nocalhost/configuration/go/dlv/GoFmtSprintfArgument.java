package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.util.Value;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.concurrency.Promise;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: GoFmtSprintfArgument.class */
public interface GoFmtSprintfArgument {
    @NotNull
    Promise<Value> getValue(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.Variable variable);
}
