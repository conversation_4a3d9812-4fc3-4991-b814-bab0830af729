package com.sentinel.nocalhost.configuration.go.dlv.protocol;

import com.goide.i18n.GoBundle;
import com.google.gson.annotations.SerializedName;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.util.NlsSafe;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: DlvApi.class */
public final class DlvApi {
    private static final Logger LOG = Logger.getInstance(DlvApi.class);

    @NonNls
    public static final String CONTINUE = "continue";

    @NonNls
    public static final String STEP = "step";

    @NonNls
    public static final String STEP_OUT = "stepOut";

    @NonNls
    public static final String STEP_INSTRUCTION = "stepInstruction";

    @NonNls
    public static final String NEXT = "next";

    @NonNls
    public static final String SWITCH_THREAD = "switchThread";

    @NonNls
    public static final String HALT = "halt";

    @NonNls
    public static final String SWITCH_GOROUTINE = "switchGoroutine";

    @NonNls
    public static final String REWIND = "rewind";

    /* loaded from: DlvApi$Breakpoint.class */
    public static class Breakpoint {
        public int id;

        @Nullable
        public String name;
        public BigInteger addr;
        public String file;
        public int line;

        @Nullable
        public String functionName;
        public String Cond;

        @SerializedName(DlvApi.CONTINUE)
        @NonNls
        public boolean tracepoint;
        public int stacktrace;
        public boolean goroutine;

        @Nullable
        public List<String> variables;
        public Object hitCount;
        public long totalHitCount;
    }

    /* loaded from: DlvApi$BreakpointInfo.class */
    public static class BreakpointInfo {
        public List<Location> stacktrace;

        @Nullable
        public Goroutine goroutine;
        public List<Variable> variables;
        public List<Variable> arguments;
        public List<Variable> locals;
    }

    /* loaded from: DlvApi$DebuggerCommand.class */
    public static class DebuggerCommand {
        public String name;
        public int threadID;
        public long goroutineID;
    }

    /* loaded from: DlvApi$DebuggerState.class */
    public static class DebuggerState {

        @SerializedName("Pid")
        @NonNls
        public int pid;
        public Breakpoint breakPoint;

        @Nullable
        public Thread currentThread;

        @SerializedName("Threads")
        @NonNls
        public List<Thread> threads;

        @SerializedName("NextInProgress")
        @NonNls
        public boolean nextInProgress;

        @Nullable
        public Goroutine currentGoroutine;
        public BreakpointInfo breakPointInfo;
        public boolean exited;
        public long exitStatus;
        public String err;

        @SerializedName("Running")
        @NonNls
        public boolean running;

        @SerializedName("Recording")
        @NonNls
        public boolean recording;
    }

    /* loaded from: DlvApi$EvalScope.class */
    public static class EvalScope {
        public long GoroutineID;
        public int Frame;
    }

    /* loaded from: DlvApi$Function.class */
    public static class Function {
        public String name;
        public BigInteger value;
        public byte type;
        public int goclass;
        public List<Variable> args;
        public List<Variable> locals;
    }

    /* loaded from: DlvApi$GetVersion.class */
    public static class GetVersion {
        public String DelveVersion;
        public int APIVersion;
        public String Backend;
        public String TargetGoVersion;
        public String MinSupportedVersionOfGo;
        public String MaxSupportedVersionOfGo;
    }

    /* loaded from: DlvApi$Goroutine.class */
    public static class Goroutine {
        public long id;
        public BigInteger pc;
        public String file;
        public int line;
        public Location currentLoc;
        public Location userCurrentLoc;
        public Location goStatementLoc;
        public int threadID;
        public Map<String, String> labels;
    }

    /* loaded from: DlvApi$ListGoroutinesOut.class */
    public static class ListGoroutinesOut {
        public List<Goroutine> Goroutines;
        public int Nextg;
    }

    /* loaded from: DlvApi$Recorded.class */
    public static class Recorded {
        public boolean Recorded;
        public String TraceDirectory;
    }

    /* loaded from: DlvApi$Thread.class */
    public static class Thread {
        public long id;
        public BigInteger pc;
        public String file;
        public int line;

        @Nullable
        public Function function;
        public long goroutineID;

        @Nullable
        public Breakpoint breakPoint;

        @Nullable
        public BreakpointInfo breakPointInfo;

        @SerializedName("ReturnValues")
        @NonNls
        public List<Variable> returnValues;
    }

    /* loaded from: DlvApi$Location.class */
    public static class Location {
        public BigInteger pc;

        @NlsSafe
        @Nullable
        public String file;
        public int line;

        @Nullable
        public Function function;

        public Location(BigInteger pc, @NlsSafe @Nullable String file, int line, @Nullable Function function) {
            this.pc = pc;
            this.file = file;
            this.line = line;
            this.function = function;
        }

        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof Location)) {
                return false;
            }
            Location location = (Location) o;
            if (this.line == location.line && Objects.equals(this.pc, location.pc)) {
                return Objects.equals(this.file, location.file);
            }
            return false;
        }

        public int hashCode() {
            int result = this.pc != null ? this.pc.hashCode() : 0;
            return (31 * ((31 * result) + (this.file != null ? this.file.hashCode() : 0))) + this.line;
        }

        @NlsSafe
        public String name() {
            return this.function != null ? this.function.name : GoBundle.message("go.debugger.missing.function.name", new Object[0]);
        }
    }

    /* loaded from: DlvApi$Variable.class */
    public static class Variable {
        public static final int SHADOWED_FLAG_MASK = 2;

        @NonNls
        public String name;

        @NonNls
        public String type;
        public BigInteger addr;
        public boolean onlyAddr;

        @NonNls
        public String realType;
        public int flags;
        public int kind;

        @NonNls
        public String value;
        public long len;
        public long cap;
        public Variable[] children;
        public BigInteger base;
        public String unreadable;
        public String locationExpression;

        @SerializedName("DeclLine")
        @Nullable
        @NonNls
        public BigInteger declLine;

        @NotNull
        private Kind getKind() {
            try {
                return Kind.values()[this.kind];
            } catch (Exception e) {
                DlvApi.LOG.warn("Unknown kind '" + this.kind + "' of variable '" + this.name + "'");
                return Kind.Invalid;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        /* loaded from: DlvApi$Variable$Kind.class */
        public enum Kind {
            Invalid,
            Bool,
            Int,
            Int8,
            Int16,
            Int32,
            Int64,
            Uint,
            Uint8,
            Uint16,
            Uint32,
            Uint64,
            Uintptr,
            Float32,
            Float64,
            Complex64,
            Complex128,
            Array,
            Chan,
            Func,
            Interface,
            Map,
            Ptr,
            Slice,
            String,
            Struct,
            UnsafePointer;

            private boolean isNumber() {
                return compareTo(Int) >= 0 && compareTo(Complex128) <= 0;
            }

            private boolean isInteger() {
                return compareTo(Int) >= 0 && compareTo(Uintptr) <= 0;
            }
        }

        public boolean isSlice() {
            return getKind() == Kind.Slice;
        }

        public boolean isArray() {
            return getKind() == Kind.Array;
        }

        public boolean isNumber() {
            return getKind().isNumber();
        }

        public boolean isInteger() {
            return getKind().isInteger();
        }

        public boolean isString() {
            return getKind() == Kind.String;
        }

        public boolean isBool() {
            return getKind() == Kind.Bool;
        }

        public boolean isStructure() {
            return getKind() == Kind.Struct;
        }

        public boolean isInterface() {
            return getKind() == Kind.Interface;
        }

        public boolean isChan() {
            return getKind() == Kind.Chan;
        }

        public boolean isFunction() {
            return getKind() == Kind.Func;
        }

        public boolean isPtr() {
            return getKind() == Kind.Ptr;
        }

        public boolean isMap() {
            return getKind() == Kind.Map;
        }

        public boolean isUint8() {
            return this.realType.equals("uint8");
        }
    }
}
