package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.dlv.JsonReaderEx;
import com.intellij.openapi.diagnostic.Logger;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.json.JsonObjectDecoder;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.debugger.*;
import org.jetbrains.io.NettyUtil;
import org.jetbrains.io.SimpleChannelInboundHandlerAdapter;
import org.jetbrains.jsonProtocol.Request;

import java.nio.charset.StandardCharsets;

public class DlvVm extends VmBase implements StandaloneVmHelper.VmEx {
    public static final Logger LOG = Logger.getInstance(DlvVm.class);
    private final @NotNull DlvCommandProcessor commandProcessor;
    private final @NotNull StandaloneVmHelper vmHelper;
    private final @NotNull DummyBreakpointManager breakpointManager;
    private final DlvDisconnectOption myDisconnectOption;

    public DlvVm(@NotNull DebugEventListener tabListener, @NotNull Channel channel, @NotNull DlvDisconnectOption disconnectOption) {


        super(tabListener);
        this.breakpointManager = new DummyBreakpointManager();
        this.myDisconnectOption = disconnectOption;
        this.commandProcessor = new DlvCommandProcessor() {
            public boolean write(@NotNull Request<?> message) {


                ByteBuf content = message.getBuffer();
                DlvVm.LOG.debug("OUT: " + content.toString(StandardCharsets.UTF_8));
                return DlvVm.this.vmHelper.write(content);
            }
        };
        this.vmHelper = new StandaloneVmHelper(this, this.commandProcessor, channel);
        channel.pipeline().addLast(new JsonObjectDecoder(NettyUtil.MAX_CONTENT_LENGTH), new SimpleChannelInboundHandlerAdapter<>() {
            public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
                ctx.close();
            }

            protected void messageReceived(ChannelHandlerContext context, Object message) {
                if (message instanceof ByteBuf) {
                    CharSequence string = ((ByteBuf)message).toString(StandardCharsets.UTF_8);
                    DlvVm.LOG.debug("IN: " + string);
                    JsonReaderEx ex = new JsonReaderEx(string);

                    try {
                        DlvVm.this.getCommandProcessor().processIncomingJson(ex);
                    } catch (Throwable var8) {
                        try {
                            ex.close();
                        } catch (Throwable var7) {
                            var8.addSuppressed(var7);
                        }

                        throw var8;
                    }

                    ex.close();
                }

            }
        });
    }

    public @NotNull DlvDisconnectOption getDisconnectOption() {


        return this.myDisconnectOption;
    }

    public @Nullable Request<?> createDisconnectRequest() {
        return null;
    }

    public @NotNull AttachStateManager getAttachStateManager() {


        return this.vmHelper;
    }

    public final @NotNull DlvCommandProcessor getCommandProcessor() {


        return this.commandProcessor;
    }

    public @NotNull ScriptManagerBase<ScriptBase> getScriptManager() {
        throw new UnsupportedOperationException();
    }

    public @NotNull BreakpointManager getBreakpointManager() {


        return this.breakpointManager;
    }

    public @NotNull SuspendContextManager<CallFrame> getSuspendContextManager() {
        return new SuspendContextManagerBase<SuspendContextBase<CallFrame>, CallFrame>() {
            public @NotNull Promise<?> continueVm(@NotNull StepAction stepAction, int stepCount) {


                return DlvVm.this.commandProcessor.send(new DlvRequest.Command("continue"));
            }

            protected @NotNull DebugEventListener getDebugListener() {

                return DlvVm.this.getDebugListener();
            }

            protected @NotNull Promise<?> doSuspend() {


                return DlvVm.this.commandProcessor.send(new DlvRequest.Command("halt"));
            }
        };
    }
}