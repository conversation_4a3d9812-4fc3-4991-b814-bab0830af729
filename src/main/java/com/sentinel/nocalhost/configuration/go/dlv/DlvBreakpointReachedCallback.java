package com.sentinel.nocalhost.configuration.go.dlv;

import com.intellij.xdebugger.breakpoints.XBreakpoint;
import com.intellij.xdebugger.breakpoints.XBreakpointProperties;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: DlvBreakpointReachedCallback.class */
public class DlvBreakpointReachedCallback {
    /* JADX INFO: Access modifiers changed from: package-private */
    public void breakpointReached(@NotNull XBreakpoint<? extends XBreakpointProperties> breakpoint, @Nullable String evaluatedLogExpression, @NotNull DlvSuspendContext context) {

    }
}
