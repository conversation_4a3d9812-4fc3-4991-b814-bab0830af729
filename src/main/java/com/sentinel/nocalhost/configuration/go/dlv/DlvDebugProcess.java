package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.GoOsManager;
import com.goide.dlv.DlvEditorsProvider;
import com.goide.dlv.DlvGoVersion;
import com.goide.dlv.DlvStackFuncName;
import com.goide.dlv.attach.GoLocalAttachDebuggerRunner;
import com.goide.dlv.breakpoint.DlvBreakpointProperties;
import com.goide.dlv.breakpoint.DlvBreakpointType;
import com.goide.dlv.breakpoint.DlvErrorBreakpointType;
import com.goide.dlv.replay.DlvRecordAndReplayRunner;
import com.goide.execution.DlvRemoteDebugDisconnectOption;
import com.goide.execution.GoRemoteDebugConfigurationType;
import com.goide.execution.GoRunConfigurationBase;
import com.goide.execution.GoRunUtil;
import com.goide.i18n.GoBundle;
import com.goide.psi.GoFile;
import com.goide.sdk.GoPackageUtil;
import com.goide.sdk.GoSdk;
import com.goide.sdk.GoSdkService;
import com.goide.statistics.GoDebuggerUsageCollector;
import com.intellij.execution.ExecutionResult;
import com.intellij.execution.configurations.RunProfile;
import com.intellij.execution.process.OSProcessUtil;
import com.intellij.execution.process.ProcessHandler;
import com.intellij.execution.runners.ExecutionEnvironment;
import com.intellij.execution.ui.ConsoleViewContentType;
import com.intellij.execution.ui.ExecutionConsole;
import com.intellij.execution.ui.UIExperiment;
import com.intellij.notification.Notification;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.actionSystem.*;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.progress.ProcessCanceledException;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DoNotAskOption;
import com.intellij.openapi.ui.MessageDialogBuilder;
import com.intellij.openapi.util.*;
import com.intellij.openapi.util.registry.Registry;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.psi.PsiManager;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.util.EnvironmentUtil;
import com.intellij.util.ObjectUtils;
import com.intellij.util.PathUtil;
import com.intellij.util.containers.ContainerUtil;
import com.intellij.util.io.socketConnection.ConnectionStatus;
import com.intellij.xdebugger.*;
import com.intellij.xdebugger.breakpoints.XBreakpoint;
import com.intellij.xdebugger.breakpoints.XBreakpointHandler;
import com.intellij.xdebugger.breakpoints.XBreakpointProperties;
import com.intellij.xdebugger.breakpoints.XLineBreakpoint;
import com.intellij.xdebugger.evaluation.XDebuggerEditorsProvider;
import com.intellij.xdebugger.evaluation.XDebuggerEvaluator;
import com.intellij.xdebugger.frame.XExecutionStack;
import com.intellij.xdebugger.frame.XStackFrame;
import com.intellij.xdebugger.frame.XSuspendContext;
import com.intellij.xdebugger.frame.XValue;
import com.intellij.xdebugger.impl.XDebugSessionImpl;
import com.intellij.xdebugger.impl.XDebuggerManagerImpl;
import com.intellij.xdebugger.impl.ui.DebuggerUIUtil;
import com.intellij.xdebugger.stepping.XSmartStepIntoHandler;
import com.sentinel.nocalhost.configuration.go.dlv.dump.DlvThreadDump;
import com.sentinel.nocalhost.configuration.go.dlv.location.DefaultDlvPositionConverter;
import com.sentinel.nocalhost.configuration.go.dlv.location.DlvPositionConverter;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import it.unimi.dsi.fastutil.ints.IntSet;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.annotations.TestOnly;
import org.jetbrains.concurrency.AsyncPromise;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;
import org.jetbrains.debugger.*;
import org.jetbrains.debugger.connection.VmConnection;

import javax.swing.event.HyperlinkEvent;
import javax.swing.event.HyperlinkListener;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

public final class DlvDebugProcess extends XDebugProcess implements Disposable {
    public static final String MIN_SUPPORTED_GO_VERSION = "1.18";
    public static final Key<String> CUSTOM_DELVE_PATH_KEY = Key.create("go.debugger.custom.delve.path");
    private static final DlvGoVersion MIN_SUPPORTED_GO_VERSION_GOLAND = Objects.requireNonNull(DlvGoVersion.parse("1.18"));
    private static final @NonNls String STOP_RECORDING_HREF = "#stoprecording";
    private static final long KILL_DELAY_MS = 3000L;
    private static final String NIL_DEREFERENCE_ERROR = "bad access: nil dereference";
    private static final int GOROUTINES_WITH_STACK_PACK_SIZE = 1000;
    private final DlvEditorsProvider myEditorsProvider;
    private final DlvRemoteVmConnection myConnection;
    private final @Nullable ExecutionResult myExecutionResult;
    private final DlvSmartStepIntoHandler mySmartStepIntoHandler;
    private final XBreakpointHandler<?>[] myBreakpointHandlers;
    private final AtomicBoolean myBreakpointsInitiated;
    private final AtomicBoolean myBreakpointsInitFinished;
    private final AtomicBoolean myComplexCommandIsInProcess;
    private final AtomicBoolean myConsumingState;
    private final AtomicReference<Promise<?>> myCommandInProgress;
    private final Map<XBreakpoint<DlvBreakpointProperties>, Integer> myBreakpoints;
    private final AtomicBoolean myShutdownStarted;
    private final boolean myRemote;
    private final Semaphore myThreadDumpSemaphore;
    private final AtomicReference<DlvThreadDump> myLastSelectedDump;
    private volatile boolean myRecorded;
    private volatile boolean mySupportReverseExecution;
    private volatile boolean myMultiClient;
    private volatile boolean myAttachedToExistingProcess;
    private volatile DlvPositionConverter myPositionConverter;
    private volatile DlvApi.Location myMainFileLocation;
    private final DlvValueRenderers myRenderers;
    private volatile boolean myShowThreads;
    private volatile boolean myProcessExited;
    private volatile DlvBreakpointReachedCallback myBreakpointReachedCallback;
    private volatile boolean myRecordingInProgress;
    private volatile InetSocketAddress myAddress;
    private volatile int myGoroutineWithStackPackSize;
    private final DlvSymbolPrettier mySymbolPrettier;
    private volatile DlvSuspendContext.DlvExecutionStack myStackSelectedInUi;
    private volatile int myPid;
    private final @NotNull Function<DlvApi.DebuggerState, Promise<Void>> myStateConsumer;

    public DlvDebugProcess(@NotNull XDebugSession session, @NotNull DlvRemoteVmConnection connection, @Nullable ExecutionResult er, boolean remote) {

        super(session);
        this.myEditorsProvider = new DlvEditorsProvider();
        this.myBreakpointHandlers = new XBreakpointHandler[]{new DlvDebugProcess.MyBreakpointHandler()};
        this.myBreakpointsInitiated = new AtomicBoolean();
        this.myBreakpointsInitFinished = new AtomicBoolean();
        this.myComplexCommandIsInProcess = new AtomicBoolean();
        this.myConsumingState = new AtomicBoolean();
        this.myCommandInProgress = new AtomicReference<>(Promises.resolvedPromise());
        this.myBreakpoints = new ConcurrentHashMap<>();
        this.myShutdownStarted = new AtomicBoolean();
        this.myThreadDumpSemaphore = new Semaphore(1);
        this.myLastSelectedDump = new AtomicReference<>();
        this.myRenderers = new DlvValueRenderers();
        this.myGoroutineWithStackPackSize = -1;
        this.myStackSelectedInUi = null;
        this.myPid = 0;
        this.myStateConsumer = new Function<DlvApi.DebuggerState, Promise<Void>>() {
            public Promise<Void> apply(DlvApi.DebuggerState o) {
                if (DlvDebugProcess.this.myComplexCommandIsInProcess.get()) {
                    return Promises.resolvedPromise();
                } else {
                    try {
                        DlvDebugProcess.this.myConsumingState.set(true);
                        if (DlvDebugProcess.this.myRecordingInProgress) {
                            DlvDebugProcess.this.myRecordingInProgress = false;
                        }

                        if (o.exited) {
                            DlvDebugProcess.this.myProcessExited = true;
                            DlvDebugProcess.this.getSession().stop();
                            return Promises.resolvedPromise();
                        } else if (o.nextInProgress && o.err == null) {
                            DlvDebugProcess.this.myConsumingState.set(false);
                            return DlvDebugProcess.this.command("continue").thenAsync((__) -> Promises.resolvedPromise());
                        } else {
                            DlvApi.Thread currentThread = o.currentThread;
                            if (currentThread == null) {
                                DlvVm.LOG.error("currentThread is null");
                                DlvDebugProcess.this.myConsumingState.set(false);
                                return Promises.resolvedPromise();
                            } else {
                                Promise var10000 = this.createSuspendContext(o).thenAsync((context) -> {
                                    XBreakpoint<? extends XBreakpointProperties<?>> breakpoint = this.findBreak(currentThread.breakPoint, o.err);
                                    XDebugSession session = DlvDebugProcess.this.getSession();
                                    if (breakpoint == null) {
                                        session.positionReached(context);
                                    } else {
                                        XExpression logExpressionObject = breakpoint.getLogExpressionObject();
                                        String logExpression = logExpressionObject != null ? logExpressionObject.getExpression() : null;
                                        if (logExpression != null) {
                                            DlvApi.Goroutine goroutine = o.currentGoroutine;
                                            if (goroutine != null) {
                                                this.evaluateExpression(context, breakpoint, logExpression, goroutine);
                                            } else {
                                                String message = GoBundle.message("go.debugger.cannot.evaluate.expression", logExpression, GoBundle.message("go.debugger.cannot.get.current.goroutine", new Object[0]));
                                                this.breakpointReached(breakpoint, message, context);
                                            }
                                        } else {
                                            this.breakpointReached(breakpoint, null, context);
                                        }
                                    }
                                    return Promises.resolvedPromise();
                                }).onProcessed((__) -> DlvDebugProcess.this.myConsumingState.set(false));

                                return var10000.onError((error) -> {
                                    Logger logger = DlvVm.LOG;
                                    logger.error(error);
                                });
                            }
                        }
                    } catch (Throwable var3) {
                        DlvVm.LOG.error("Error while processing debugger state", var3);
                        DlvDebugProcess.this.myConsumingState.set(false);
                        return Promises.resolvedPromise();
                    }
                }
            }

            private @NotNull Promise<DlvSuspendContext> createSuspendContext(@NotNull DlvApi.@NotNull DebuggerState state) {


                DlvApi.Thread currentThread = state.currentThread;

                assert currentThread != null;

                Promise<DlvSuspendContext> var10000;
                if (DlvDebugProcess.this.myShowThreads) {
                    var10000 = DlvSuspendContext.create(DlvDebugProcess.this, currentThread, state.err);

                    return var10000;
                } else if (state.currentGoroutine != null) {
                    if (SystemInfo.isMac && "bad access: nil dereference".equals(state.err)) {
                        var10000 = DlvDebugProcess.this.findGoroutine(state.currentGoroutine.id).thenAsync((g) -> DlvSuspendContext.create(DlvDebugProcess.this, g, state.err));


                        return var10000;
                    } else {
                        var10000 = DlvSuspendContext.create(DlvDebugProcess.this, state.currentGoroutine, state.err);


                        return var10000;
                    }
                } else if (currentThread.goroutineID == 0L) {
                    var10000 = DlvDebugProcess.this.findAnyGoroutine().thenAsync((g) -> DlvSuspendContext.create(DlvDebugProcess.this, g, state.err));


                    return var10000;
                } else {
                    var10000 = DlvDebugProcess.this.findGoroutine(currentThread.goroutineID).thenAsync((g) -> DlvSuspendContext.create(DlvDebugProcess.this, g, state.err));


                    return var10000;
                }
            }

            private void evaluateExpression(final DlvSuspendContext context, final XBreakpoint<? extends XBreakpointProperties<?>> breakpoint, final String logExpression, DlvApi.Goroutine goroutine) {
                (new DlvExpressionEvaluator(DlvDebugProcess.this, goroutine.currentLoc, 0, goroutine.id)).evaluate(logExpression, new XDebuggerEvaluator.XEvaluationCallback() {
                    public void evaluated(@NotNull XValue result) {


                        String evaluatedExpression = result instanceof DlvXValue ? getEvaluatedExpression((DlvXValue)result) : null;
                        breakpointReached(breakpoint, evaluatedExpression, context);
                    }

                    public void errorOccurred(@NotNull String errorMessage) {


                        String message = GoBundle.message("go.debugger.cannot.evaluate.expression", logExpression, errorMessage);
                        breakpointReached(breakpoint, message, context);
                    }
                }, null);
            }

            private @Nullable String getEvaluatedExpression(@NotNull DlvXValue result) {


                int count = result.getCallResultCount();
                return switch (count) {
                    case -1, 0, 1 -> result.getVariable().value;
                    default ->
                            Arrays.stream(result.getVariable().children).map((it) -> it.value).collect(Collectors.joining(", ", "(", ")"));
                };
            }

            void breakpointReached(@NotNull XBreakpoint<? extends XBreakpointProperties<?>> breakpoint, @Nullable String evaluatedLogExpression, @NotNull DlvSuspendContext context) {


                if (DlvDebugProcess.this.myBreakpointReachedCallback != null) {
                    DlvDebugProcess.this.myBreakpointReachedCallback.breakpointReached(breakpoint, evaluatedLogExpression, context);
                }

                if (breakpoint.getType() instanceof DlvErrorBreakpointType) {
                    if (SystemInfo.isMac) {
                        DlvDebugProcess.this.getSession().breakpointReached(breakpoint, evaluatedLogExpression, context);
                    } else if (!breakpoint.isEnabled() || !DlvDebugProcess.this.getSession().breakpointReached(breakpoint, evaluatedLogExpression, context)) {
                        DlvDebugProcess.this.resume(context);
                    }
                } else if (!DlvDebugProcess.this.getSession().breakpointReached(breakpoint, evaluatedLogExpression, context)) {
                    DlvDebugProcess.this.resume(context);
                }

            }

            private @Nullable XBreakpoint<? extends XBreakpointProperties<?>> findBreak(@Nullable DlvApi.@Nullable Breakpoint point, @Nullable String error) {
                if (point != null) {
                    for (Map.Entry<XBreakpoint<DlvBreakpointProperties>, Integer> xBreakpointIntegerEntry : DlvDebugProcess.this.myBreakpoints.entrySet()) {
                        if (xBreakpointIntegerEntry.getValue() == point.id) {
                            return xBreakpointIntegerEntry.getKey();
                        }
                    }
                }

                return DlvDebugProcess.isErrorBreakpoint(point, error) ? DlvErrorBreakpointType.findDefaultErrorBreakpoint(DlvDebugProcess.this.getSession().getProject()) : null;
            }
        };
        this.myConnection = connection;
        this.myExecutionResult = er;
        this.mySmartStepIntoHandler = new DlvSmartStepIntoHandler(session);
        this.myRemote = remote;
        session.addSessionListener(new XDebugSessionListener() {
            public void beforeSessionResume() {
                DlvDebugProcess.this.saveStackSelectedInUi();
            }
        });
        if (session instanceof XDebugSessionImpl) {
            ExecutionEnvironment env = ((XDebugSessionImpl)session).getExecutionEnvironment();
            if (env != null && env.getRunner() instanceof DlvRecordAndReplayRunner) {
                this.myRecordingInProgress = true;
            }
        }

        String myUnknownFunctionName = GoBundle.message("go.debugger.unknown.function.name", new Object[0]);
        this.mySymbolPrettier = new DlvSymbolPrettier(session);
        session.setPauseActionSupported(true);
    }

    public boolean isShowThreads() {
        return this.myShowThreads;
    }

    public void setShowThreads(boolean showThreads) {
        this.myShowThreads = showThreads;
        this.consumeDebuggerState();
    }

    private void consumeDebuggerState() {
        Promise<Void> var10000 = this.send(new DlvRequest.State()).thenAsync(this::consumeDebuggerState);
        Logger var10001 = DlvVm.LOG;
        Objects.requireNonNull(var10001);
        var10000.onError(var10001::info);

    }

    @NotNull Promise<Void> consumeDebuggerState(@NotNull DlvApi.@NotNull DebuggerState state) {
        return this.myStateConsumer.apply(state);
    }

    @NotNull Promise<Void> consumeStateWithError(@NotNull String message) {
        return this.send(new DlvRequest.State()).thenAsync((s) -> {
            s.err = message;
            return this.consumeDebuggerState(s);
        });
    }

    public @Nullable VirtualFile findFile(@Nullable DlvApi.@Nullable Location location) {
        return this.findFile(location != null ? location.file : null);
    }

    public @Nullable VirtualFile findFile(@Nullable String remotePath) {
        if (StringUtil.isEmpty(remotePath)) {
            return null;
        } else if (this.myPositionConverter != null) {
            return this.myPositionConverter.toLocalFile(remotePath);
        } else {
            VirtualFile file = LocalFileSystem.getInstance().findFileByPath(remotePath);
            if (file != null) {
                return file;
            } else {
                if (GoOsManager.isWindows()) {
                    Project project = this.getSession().getProject();
                    GoSdk sdk = !project.isDisposed() ? GoSdkService.getInstance(project).getSdk(DlvUtil.getModule(this.getSession())) : GoSdk.NULL;
                    if (sdk.isValid()) {
                        String newUrl = StringUtil.replaceIgnoreCase(remotePath, "c:/go", sdk.getHomeUrl());
                        return VirtualFileManager.getInstance().findFileByUrl(newUrl);
                    }
                }

                return null;
            }
        }
    }

    public @Nullable VirtualFile getMainFile() {
        return this.findFile(this.myMainFileLocation);
    }

    public void connect(@NotNull InetSocketAddress address) {
        if (this.myConnection == null) {
            throw new IllegalStateException(GoBundle.message("go.debugger.cannot.connect.non.remote", new Object[0]));
        } else if (this.myConnection.getState().getStatus() != ConnectionStatus.NOT_CONNECTED) {
            throw new IllegalStateException(GoBundle.message("go.debugger.cannot.connect.twice", new Object[0]));
        } else {
            this.myAddress = address;
            DlvVm.LOG.debug("Open connection to " + address);
            this.myConnection.open(address).onError((err) -> {
                String reason = err.toString();
                DlvVm.LOG.debug("Connection to " + address + " failed", err);
                if (!this.myShutdownStarted.get()) {
                    showNotification(this.getSession().getProject(), NotificationType.WARNING, StringUtil.isEmpty(reason) ? GoBundle.message("go.debugger.connection.failed.generic.message", new Object[]{address}) : reason);
                }

                this.getSession().stop();
            });
        }
    }

    public boolean supportsReverseExecution() {
        return this.mySupportReverseExecution;
    }

    protected @NotNull ProcessHandler doGetProcessHandler() {
        ProcessHandler handler = this.myExecutionResult != null ? this.myExecutionResult.getProcessHandler() : null;
        ProcessHandler processHandler;
        if (handler == null) {
            processHandler = new DefaultDebugProcessHandler() {
                public boolean isSilentlyDestroyOnClose() {
                    return true;
                }
            };
        } else {
            processHandler = new ProcessHandlerWrapper(this, handler);
        }
        return processHandler;
    }

    private static boolean isErrorBreakpoint(@Nullable DlvApi.@Nullable Breakpoint point, @Nullable String error) {
        return point != null && (point.id == -1 || point.id == -2) || SystemInfo.isMac && "bad access: nil dereference".equals(error);
    }

    private @Nullable DlvCommandProcessor getProcessor() {
        DlvVm dlvVm = this.myConnection.getVm();
        return dlvVm != null ? dlvVm.getCommandProcessor() : null;
    }

    <T> @NotNull Promise<T> send(@NotNull DlvRequest<T> request) {

        DlvCommandProcessor processor = this.getProcessor();
        Promise<T> var10000;
        if (processor == null) {
            request.getBuffer().release();
            var10000 = Promises.rejectedPromise();
            return var10000;
        } else {
            var10000 = processor.send(request).onError((t) -> {
                String message = t.getMessage();
                if (isConditionEvaluationFailed(message)) {
                    this.consumeStateWithError(message);
                } else if (SystemInfo.isMac && "bad access".equals(message)) {
                    this.consumeStateWithError("bad access: nil dereference");
                } else {
                    DlvVm.LOG.info(t);
                }
            });
            return var10000;
        }
    }

    private static boolean isConditionEvaluationFailed(@NonNls @Nullable String s) {
        return s != null && (s.startsWith("error evaluating expression:") || s.startsWith("condition expression unreadable:") || s.startsWith("condition expression not boolean"));
    }

    public XBreakpointHandler<?> @NotNull [] getBreakpointHandlers() {
        ConnectionStatus status = this.myConnection.getState().getStatus();
        return switch (status) {
            case DISCONNECTED, DETACHED, CONNECTION_FAILED -> XBreakpointHandler.EMPTY_ARRAY;
            default -> this.myBreakpointHandlers;
        };
    }

    public @NotNull ExecutionConsole createConsole() {
        ExecutionResult executionResult = this.myExecutionResult;

        return executionResult == null ? super.createConsole() : executionResult.getExecutionConsole();
    }

    public void dispose() {
        this.myMainFileLocation = null;
    }

    public boolean checkCanInitBreakpoints() {
        this.myConnection.stateChanged((state) -> {
            String statusText = state.getStatus().getStatusText();
            String msg = state.getMessage();
            String details = msg.equals(statusText) ? "" : " (" + msg + ")";
            DlvVm.LOG.debug("Connection state changed: status=" + statusText + details);
            if (state.getStatus() == ConnectionStatus.CONNECTED) {
                this.initBreakpointHandlersAndSetBreakpoints();
            } else if (this.myConnection.getState().getStatus() == ConnectionStatus.DISCONNECTED && !this.myShutdownStarted.get()) {
                showNotification(this.getSession().getProject(), NotificationType.WARNING, GoBundle.message("go.debugger.disconnected.unexpectedly", new Object[0]));
                if (!this.getSession().isStopped()) {
                    this.getSession().stop();
                }
            }

            this.getSession().rebuildViews();
            return null;
        });
        if (this.connected()) {
            this.initBreakpointHandlersAndSetBreakpoints();
        }

        return false;
    }

    private void initBreakpointHandlersAndSetBreakpoints() {
        if (this.myBreakpointsInitiated.compareAndSet(false, true)) {
            DlvVm.LOG.debug("Init debug session");
            Vm vm = this.myConnection.getVm();

            assert vm != null : "Vm should be initialized";

            runInBackground(() -> this.prepareDelveForInit().thenAsync((__) -> this.checkGoVersion()).thenAsync((__) -> this.retrieveRemoteProperties()).thenAsync((__) -> this.initPositionConverter()).thenAsync((__) -> this.initBreakpoints()));
        }
    }

    private @NotNull Promise<?> initBreakpoints() {
        if (this.isCoreDump()) {
            this.myBreakpointsInitFinished.set(true);
            this.consumeDebuggerState();
        } else {
            this.setAndResume();
        }


        return Promises.resolvedPromise();
    }

    private boolean isCoreDump() {
        return this.myRecorded && !this.mySupportReverseExecution;
    }

    private @NotNull Promise<?> initPositionConverter() {
        Promise<?> var10000;
        Logger var10001;
        if (!this.myRemote && !this.isUseTrimPaths()) {
            if (DlvVm.LOG.isDebugEnabled()) {
                var10000 = this.send(new DlvRequest.ListSources());
                var10001 = DlvVm.LOG;
                Objects.requireNonNull(var10001);
                var10000 = var10000.onError(var10001::info);;
            } else {
                var10000 = Promises.resolvedPromise();
            }



            return var10000;
        } else {
            var10000 = this.send(new DlvRequest.ListSources()).onSuccess(this::setPositionConverter);
            var10001 = DlvVm.LOG;
            Objects.requireNonNull(var10001);
            var10000 = var10000.onError(var10001::info);;


            return var10000;
        }
    }

    private boolean isUseTrimPaths() {
        RunProfile profile = this.getSession().getRunProfile();
        if (!(profile instanceof GoRunConfigurationBase)) {
            return false;
        } else if (hasTrimPath(((GoRunConfigurationBase<?>)profile).getGoToolParams())) {
            return true;
        } else if (hasTrimPath((String)((GoRunConfigurationBase<?>)profile).getCustomEnvironment().get("GOFLAGS"))) {
            return true;
        } else {
            String systemGoFlags = ((GoRunConfigurationBase<?>)profile).isPassParentEnvironment() ? (String)EnvironmentUtil.getEnvironmentMap().get("GOFLAGS") : null;
            return hasTrimPath(systemGoFlags);
        }
    }

    private static boolean hasTrimPath(@Nullable String params) {
        return params != null && GoRunUtil.findParam(params, (p) -> p.startsWith("-trimpath")) != null;
    }

    private @NotNull Promise<?> retrieveRemoteProperties() {

        return this.send(new DlvRequest.Recorded()).onSuccess((recordedInfo) -> {
            this.myRecorded = recordedInfo.Recorded;
            this.mySupportReverseExecution = this.myRecorded && StringUtil.isNotEmpty(recordedInfo.TraceDirectory);
        }).thenAsync((__) -> this.myRemote ? this.send(new DlvRequest.IsMulticlient()).onSuccess((result) -> {
            this.myMultiClient = result;
        }) : Promises.resolvedPromise()).thenAsync((__) -> this.myRemote ? this.send(new DlvRequest.AttachedToExistingProcess()).onSuccess((result) -> {
            this.myAttachedToExistingProcess = result;
        }) : Promises.resolvedPromise()).thenAsync((__) -> this.send(new DlvRequest.FindLocation("main.main"))).onSuccess((locations) -> {
            this.myMainFileLocation = ContainerUtil.getFirstItem(locations);
        });
    }

    private @NotNull Promise<?> prepareDelveForInit() {
        this.showCustomDelveNotification();
        this.myComplexCommandIsInProcess.set(true);

        return this.send(new DlvRequest.State(true)).thenAsync((state) -> {
            this.myPid = state.pid;
            if (state.recording) {
                return this.send(new DlvRequest.State(false));
            } else {
                return state.running ? this.command("halt") : Promises.resolvedPromise(state);
            }
        }).thenAsync((__) -> {
            this.myComplexCommandIsInProcess.set(false);
            return Promises.resolvedPromise();
        });
    }

    private void showCustomDelveNotification() {
        final XDebugSessionImpl session = ObjectUtils.tryCast(this.getSession(), XDebugSessionImpl.class);
        ExecutionEnvironment env = session != null ? session.getExecutionEnvironment() : null;
        String customDelvePath = env != null ? env.getUserData(CUSTOM_DELVE_PATH_KEY) : null;
        if (customDelvePath != null) {
            final AnAction editCustomPropertiesAction = getEditCustomPropertiesAction(session);
            Notification notification;
            if (editCustomPropertiesAction != null) {
                notification = XDebuggerManagerImpl.getNotificationGroup().createNotification(GoBundle.message("go.debugger.custom.delve.extended.notification", customDelvePath), NotificationType.WARNING);
                notification.setListener((notification1, event) -> {
                    if (event.getEventType() == HyperlinkEvent.EventType.ACTIVATED) {
                        editCustomPropertiesAction.actionPerformed(DlvDebugProcess.createProjectActionEvent(session));
                    }

                });
            } else {
                notification = XDebuggerManagerImpl.getNotificationGroup().createNotification(GoBundle.message("go.debugger.custom.delve.notification", customDelvePath), NotificationType.WARNING);
            }

            notification.notify(session.getProject());
        }

    }

    private static @Nullable AnAction getEditCustomPropertiesAction(@NotNull XDebugSessionImpl session) {


        AnAction result = ActionManager.getInstance().getAction("EditCustomProperties");
        if (result == null) {
            return null;
        } else {
            AnActionEvent e = createProjectActionEvent(session);
            e.getPresentation().setEnabled(false);
            result.update(e);
            return e.getPresentation().isEnabled() ? result : null;
        }
    }

    private static @NotNull AnActionEvent createProjectActionEvent(@NotNull XDebugSessionImpl session) {


        Project project = session.getProject();
        DataContext context = (dataId) -> CommonDataKeys.PROJECT.is(dataId) ? project : null;


        return AnActionEvent.createFromDataContext("Notification", new Presentation(), context);
    }

    private @NotNull Promise<?> checkGoVersion() {

        return this.send(new DlvRequest.GetVersion()).thenAsync((versionInfo) -> {
            DlvGoVersion minDelveVersion = DlvGoVersion.parse(versionInfo.MinSupportedVersionOfGo);
            if (minDelveVersion == null) {
                DlvVm.LOG.warn("Cannot parse minimum supported go version '" + versionInfo.MinSupportedVersionOfGo + "', continue without version check");
                return Promises.resolvedPromise();
            } else {
                XDebugSessionImpl session = ObjectUtils.tryCast(this.getSession(), XDebugSessionImpl.class);
                ExecutionEnvironment env = session != null ? session.getExecutionEnvironment() : null;
                String customDelvePath = env != null ? env.getUserData(CUSTOM_DELVE_PATH_KEY) : null;
                String expectedBundledDelveCommit;
                String actualDelveCommit;
                if (customDelvePath == null) {
                    try {
                        expectedBundledDelveCommit = getBundledDelveArtifactVersionAndCommit().second;
                        actualDelveCommit = getDelveCommit(versionInfo.DelveVersion);
                        if (actualDelveCommit == null) {
                            DlvVm.LOG.warn("Failed to extract delve commit from delve version");
                        } else if (!expectedBundledDelveCommit.equals(actualDelveCommit)) {
                            DlvVm.LOG.warn("Unexpected bundled delve version. Expected commit '" + expectedBundledDelveCommit + "', actual commit '" + actualDelveCommit + "'.");
                        }
                    } catch (Exception var12) {
                        DlvVm.LOG.warn("Failed to get expected bundled delve version", var12);
                    }
                }

                expectedBundledDelveCommit = "Go cmd/compile ";
                actualDelveCommit = StringUtil.trimStart(versionInfo.TargetGoVersion, expectedBundledDelveCommit);
                DlvGoVersion actualGoVersion = DlvGoVersion.parse(actualDelveCommit);
                if (actualGoVersion == null) {
                    DlvVm.LOG.warn("Cannot parse go version '" + versionInfo.TargetGoVersion + "', continue without version check");
                    return Promises.resolvedPromise();
                } else {
                    GoDebuggerUsageCollector.logGoTargetVersion(actualGoVersion);
                    Project project = this.getSession().getProject();
                    if (!actualGoVersion.isAfterOrEqual(minDelveVersion)) {
                        String version = StringUtil.trimStart(actualDelveCommit, "go");
                        String msg;
                        if (actualGoVersion.isAfterOrEqual(MIN_SUPPORTED_GO_VERSION_GOLAND)) {
                            msg = GoBundle.message("go.debugger.go.version.is.not.officially.supported", version, versionInfo.MinSupportedVersionOfGo);
                            showNotification(project, NotificationType.WARNING, msg);
                        } else {
                            msg = GoBundle.message("go.debugger.go.version.is.not.supported", version, versionInfo.MinSupportedVersionOfGo);
                            showNotification(project, NotificationType.ERROR, msg);
                            this.getSession().getConsoleView().print(msg, ConsoleViewContentType.LOG_ERROR_OUTPUT);
                            if (!Registry.is("go.debugger.allow.unsupported.go.versions")) {
                                this.getSession().stop();
                                return Promises.rejectedPromise();
                            }
                        }
                    }

                    return Promises.resolvedPromise();
                }
            }
        });
    }

    public static void showNotification(@NotNull Project project, @NotNull NotificationType type, @NotNull @NlsContexts.NotificationContent String msg) {


        XDebuggerManagerImpl.getNotificationGroup().createNotification(msg, type).notify(project);
    }

    private void setAndResume() {
        ReadAction.run(() -> this.getSession().initBreakpoints());
        this.pauseIfNeededAndProcess(() -> {
            this.myBreakpointsInitFinished.set(true);
            this.resumeIfSuspended();
            return Promises.resolvedPromise();
        });
    }

    private void resumeIfSuspended() {
        this.myComplexCommandIsInProcess.set(true);
        this.send(new DlvRequest.State(true)).onProcessed((state) -> {
            this.myComplexCommandIsInProcess.set(false);
            if (state != null && !state.running) {
                this.continueVm(StepAction.CONTINUE);
            }

        });
    }

    private void setPositionConverter(@NotNull List<String> sourcesList) {

        Set<String> remotePaths = ContainerUtil.map2SetNotNull(sourcesList, PathUtil::toSystemIndependentName);
        this.myPositionConverter = new DefaultDlvPositionConverter(this.getSession().getProject(), remotePaths);
    }

    public void rewind() {
        this.command("rewind");
    }

    private @NotNull Promise<DlvApi.DebuggerState> command(@NotNull String name) {


        return this.command(new DlvRequest.Command(name));
    }

    @NotNull Promise<DlvApi.DebuggerState> command(@NotNull DlvRequest<? extends DlvApi.DebuggerState> request) {


        AsyncPromise<DlvApi.DebuggerState> result = new AsyncPromise<>();
        runInBackground(() -> this.send(request).thenAsync((state) -> this.consumeDebuggerState(state).onProcessed((__) -> {
            result.setResult(state);
        })));


        return result;
    }

    @NotNull DlvSmartStepInto getSmartStepInto() {
        return new DlvSmartStepInto(this);
    }

    private void saveStackSelectedInUi() {
        XDebugSessionImpl session = ObjectUtils.tryCast(this.getSession(), XDebugSessionImpl.class);
        this.myStackSelectedInUi = session != null ? ObjectUtils.tryCast(session.getCurrentExecutionStack(), DlvSuspendContext.DlvExecutionStack.class) : null;
    }

    private void continueVmInBackground(@NotNull StepAction stepAction) {

        runInBackground(() -> {
            this.pauseIfNeededAndProcess(() -> {
                this.continueVm(stepAction);
                return Promises.resolvedPromise();
            });
        });
    }

    private @NotNull Promise<?> switchToStackSelectedInUi() {
        DlvSuspendContext.DlvExecutionStack stack = this.myStackSelectedInUi;
        Promise<?> var10000;
        if (stack == null) {
            var10000 = Promises.resolvedPromise();


            return var10000;
        } else {
            this.myStackSelectedInUi = null;
            if (stack.isCurrent()) {
                var10000 = Promises.resolvedPromise();


                return var10000;
            } else {
                var10000 = this.send(stack.createSwitchCommand());


                return var10000;
            }
        }
    }

    private void continueVm(@NotNull StepAction stepAction) {


        this.switchToStackSelectedInUi().then((__) -> {
            switch (stepAction) {
                case CONTINUE:
                    this.command("continue");
                    break;
                case IN:
                    this.stepAndConsumeState();
                    break;
                case OVER:
                    this.command("next");
                    break;
                case OUT:
                    this.command("stepOut");
            }

            return null;
        });
    }

    void stepAndConsumeState() {
        runInBackground(() -> {
            this.step().thenAsync(this::consumeDebuggerState);
        });
    }

    @NotNull Promise<DlvApi.DebuggerState> step() {


        return Registry.is("go.debugger.skip.autogenerated.frames") ? this.stepSkippingAutogeneratedFrames() : this.send(new DlvRequest.Command("step"));
    }

    private @NotNull Promise<DlvApi.DebuggerState> stepSkippingAutogeneratedFrames() {


        return this.send(new DlvRequest.Command("step")).thenAsync((state) -> {
            DlvApi.Location loc = DlvUtil.getCurrentLocation(state);
            return DlvUtil.isAutogenerated(loc) ? this.stepSkippingAutogeneratedFrames() : Promises.resolvedPromise(state);
        });
    }

    public void runToPosition(@NotNull XSourcePosition position, @Nullable XSuspendContext context) {


        VirtualFile file = position.getFile();
        int line = position.getLine();
        String debuggerPath = this.getDebuggerPath(file);
        Project project = this.getSession().getProject();
        if (debuggerPath == null) {
            showNotification(project, NotificationType.WARNING, GoBundle.message("go.debugger.cannot.find.debugger.path", file.getPath()));
            this.command("continue");
        } else {
            runInBackground(() -> this.pauseIfNeededAndProcess(() -> {
                this.send(new DlvRequest.CreateBreakpoint(debuggerPath, line + 1, null)).onError((t) -> {
                    showNotification(project, NotificationType.WARNING, GoBundle.message("go.debugger.failed.to.create.breakpoint"));
                    this.command("continue");
                }).onSuccess(this::continueAndClearBreakpoint);
                return Promises.resolvedPromise();
            }));
        }

    }

    private void continueAndClearBreakpoint(@NotNull DlvApi.@NotNull Breakpoint breakpoint) {


        this.send(new DlvRequest.Command("continue")).onSuccess((state) -> {
            this.send(new DlvRequest.ClearBreakpoint(breakpoint.id)).onProcessed((__) -> this.consumeDebuggerState(state));
        }).onError((error) -> {
            showNotification(this.getSession().getProject(), NotificationType.WARNING, GoBundle.message("go.debugger.failed.to.run.to.position"));
        });
    }

    public @NotNull Promise<Object> stopAsync() {
        Promise<Object> var10000;
        if (!Disposer.isDisposed(this) && this.myShutdownStarted.compareAndSet(false, true)) {
            if (!this.connected()) {
                this.myConnection.detachAndClose();
                var10000 = Promises.resolvedPromise();

                return var10000;
            } else if (this.myRecordingInProgress) {
                this.myConnection.detachAndClose();
                var10000 = Promises.resolvedPromise();


                return var10000;
            } else {
                DlvDisconnectOption disconnectOption = this.getDisconnectOption();
                if (disconnectOption == DlvDisconnectOption.LEAVE_RUNNING) {
                    this.myComplexCommandIsInProcess.set(true);
                    Promise<Object> clearBreakpointsIfRunning = this.myProcessExited ? Promises.resolvedPromise() : this.send(new DlvRequest.Command("halt")).thenAsync((__) -> this.clearBreakpoints()).thenAsync((__) -> {
                        this.send(new DlvRequest.Command("continue"));
                        return Promises.resolvedPromise();
                    });
                    var10000 = clearBreakpointsIfRunning.onProcessed((__) -> {
                        this.myComplexCommandIsInProcess.set(false);
                        Disposer.dispose(this);
                        this.myConnection.detachAndClose();
                    });

                    return var10000;
                } else {
                    var10000 = this.interruptProcessOrHaltDebugger(disconnectOption).thenAsync((__) -> this.connected() ? this.send(new DlvRequest.Detach(disconnectOption == DlvDisconnectOption.KILL)) : Promises.resolvedPromise()).onProcessed((__) -> {
                        DlvVm.LOG.debug("Closing connection");
                        Disposer.dispose(this);
                        this.myConnection.detachAndClose();
                        if (this.myExecutionResult != null) {
                            this.myExecutionResult.getProcessHandler().waitFor(3000L);
                            DlvVm.LOG.debug("Delve terminated");
                        }

                    });


                    return var10000;
                }
            }
        } else {
            var10000 = Promises.resolvedPromise();


            return var10000;
        }
    }

    private @NotNull Promise<?> interruptProcessOrHaltDebugger(@Nullable DlvDisconnectOption disconnectOption) {
        int pid = this.myPid;
        this.myComplexCommandIsInProcess.set(true);
        GoRunConfigurationBase<?> profile = ObjectUtils.tryCast(this.getSession().getRunProfile(), GoRunConfigurationBase.class);
        Promise<?> var10000;
        if (this.myRecorded || this.myRemote || this.myProcessExited || pid == 0 || disconnectOption != DlvDisconnectOption.KILL || profile != null && profile.isRunWithSudo()) {
            var10000 = this.myProcessExited ? Promises.resolvedPromise() : this.send(new DlvRequest.Command("halt"));


            return var10000;
        } else {
            var10000 = this.send(new DlvRequest.Command("halt")).thenAsync((state) -> {
                if (state.exited) {
                    this.myProcessExited = true;
                    return Promises.resolvedPromise();
                } else {
                    DlvVm.LOG.debug("Clear breakpoints before interrupting a process");
                    return this.clearBreakpoints();
                }
            }).thenAsync((__) -> {
                if (this.myProcessExited) {
                    DlvVm.LOG.debug("Process exited, skip graceful shutdown");
                    return Promises.resolvedPromise(false);
                } else {
                    return Promises.runAsync(() -> {
                        DlvVm.LOG.debug("Resume before interrupting the process");
                        AtomicBoolean running = new AtomicBoolean(false);
                        this.send(new DlvRequest.Command("continue")).thenAsync((statex) -> {
                            if (statex != null) {
                                if (statex.exited) {
                                    this.myProcessExited = true;
                                }

                                if (statex.running) {
                                    running.set(true);
                                }
                            }

                            return Promises.resolvedPromise();
                        });
                        long t0 = System.currentTimeMillis();

                        while(!running.get()) {
                            try {
                                DlvApi.DebuggerState state = this.send(new DlvRequest.State(true)).blockingGet(1, TimeUnit.SECONDS);
                                if (state != null) {
                                    if (state.exited) {
                                        this.myProcessExited = true;
                                        break;
                                    }

                                    if (state.running) {
                                        running.set(true);
                                        break;
                                    }

                                    long waitTime = System.currentTimeMillis() - t0;
                                    if (waitTime > 5000L) {
                                        DlvVm.LOG.debug("Process didn't resume after " + waitTime + "ms, stop waiting");
                                        break;
                                    }
                                }
                            } catch (Exception var7) {
                                running.set(false);
                                break;
                            }
                        }

                        return running.get();
                    });
                }
            }).then((programRunning) -> {
                if (this.myProcessExited) {
                    DlvVm.LOG.debug("Process exited, skip graceful shutdown");
                    return false;
                } else if (!programRunning) {
                    DlvVm.LOG.debug("Process didn't resume, skip graceful shutdown");
                    return false;
                } else {
                    try {
                        DlvVm.LOG.debug("Interrupt process " + pid);
                        OSProcessUtil.terminateProcessGracefully(pid);
                        return true;
                    } catch (Exception var4) {
                        DlvVm.LOG.debug("Failed to interrupt process", var4);
                        return false;
                    }
                }
            }).thenAsync((shouldWaitForExit) -> {
                if (shouldWaitForExit) {
                    long t0 = System.currentTimeMillis();

                    while(!this.myProcessExited) {
                        ConnectionStatus status = this.myConnection.getState().getStatus();
                        if (status == ConnectionStatus.DISCONNECTED || status == ConnectionStatus.DETACHED) {
                            DlvVm.LOG.debug("Delve disconnected");
                            break;
                        }

                        long waitTime = System.currentTimeMillis() - t0;
                        if (waitTime > 30000L) {
                            DlvVm.LOG.debug("Process didn't exit after " + waitTime + "ms, stop waiting");
                            break;
                        }

                        try {
                            Thread.sleep(100L);
                        } catch (InterruptedException var9) {
                            break;
                        }
                    }

                    if (this.myProcessExited) {
                        DlvVm.LOG.debug("Process terminated, pid=" + pid);
                    }
                }

                return this.connected() ? this.send(new DlvRequest.Command("halt")) : Promises.resolvedPromise(null);
            });


            return var10000;
        }
    }

    private @Nullable DlvDisconnectOption getDisconnectOption() {
        Vm var3 = this.myConnection.getVm();
        DlvDisconnectOption var10000;
        if (var3 instanceof DlvVm vm) {
            var10000 = vm.getDisconnectOption();
        } else {
            var10000 = null;
        }

        DlvDisconnectOption defaultDisconnectOption = var10000;
        if (!this.myRemote && !this.myAttachedToExistingProcess) {
            return defaultDisconnectOption;
        } else {
            AtomicReference<DlvDisconnectOption> resultRef = new AtomicReference<>();
            ApplicationManager.getApplication().invokeAndWait(() -> {
                resultRef.set(this.askForDisconnectOption(defaultDisconnectOption));
            });
            return resultRef.get();
        }
    }

    private @Nullable DlvDisconnectOption askForDisconnectOption(@Nullable DlvDisconnectOption disconnectOption) {
        Project project = this.getSession().getProject();
        DlvDisconnectOption result = disconnectOption;
        GoRemoteDebugConfigurationType.DlvRemoteDebugConfiguration config = ObjectUtils.tryCast(this.getSession().getRunProfile(), GoRemoteDebugConfigurationType.DlvRemoteDebugConfiguration.class);
        boolean shouldAsk = config != null && config.getDisconnectOption() == DlvRemoteDebugDisconnectOption.ASK;
        if (this.myRemote && shouldAsk && disconnectOption == DlvDisconnectOption.LEAVE_RUNNING) {
            if (this.myMultiClient) {
                String message = this.myProcessExited ? GoBundle.message("go.debugger.process.terminated.stop.remote.delve.message") : GoBundle.message("go.debugger.stop.remote.delve.message", new Object[0]);
                MessageDialogBuilder.YesNo dialog = MessageDialogBuilder.yesNo(GoBundle.message("go.debugger.stop.remote.delve.title", new Object[0]), message).doNotAsk(getDoNotAskOption(config));
                if (dialog.ask(project)) {
                    result = DlvDisconnectOption.DETACH;
                }
            } else {
                result = DlvDisconnectOption.DETACH;
            }
        }

        boolean isAttachedToLocalProcess = GoLocalAttachDebuggerRunner.isAttachedToLocalProcess(this.myExecutionResult);
        if (!isAttachedToLocalProcess && this.myAttachedToExistingProcess && !this.myProcessExited && result == DlvDisconnectOption.DETACH) {
            String message = GoBundle.message("go.debugger.kill.existing.process.message");
            MessageDialogBuilder.YesNo dialog = MessageDialogBuilder.yesNo(GoBundle.message("go.debugger.kill.existing.process.title"), message);
            if (dialog.ask(project)) {
                result = DlvDisconnectOption.KILL;
            }
        }

        return result;
    }

    @NotNull
    private static DoNotAskOption.@NotNull Adapter getDoNotAskOption(@NotNull final GoRemoteDebugConfigurationType.@NotNull DlvRemoteDebugConfiguration runConfig) {


        return new DoNotAskOption.Adapter() {
            public void rememberChoice(boolean isSelected, int exitCode) {
                if (isSelected) {
                    runConfig.setDisconnectOption(exitCode == 0 ? DlvRemoteDebugDisconnectOption.STOP : DlvRemoteDebugDisconnectOption.LEAVE);
                }

            }

            public boolean shouldSaveOptionsOnCancel() {
                return true;
            }
        };
    }

    private Promise<?> clearBreakpoints() {
        return this.send(new DlvRequest.ListBreakpoints()).thenAsync((processBreakpoints) -> {
            List<Promise<DlvApi.Breakpoint>> promises = new ArrayList<>();
            IntSet processed = new IntOpenHashSet();

            for (DlvApi.Breakpoint breakpoint : processBreakpoints) {
                processed.add(breakpoint.id);
                promises.add(this.send(new DlvRequest.ClearBreakpoint(breakpoint.id)));
            }

            this.myBreakpoints.values().forEach((id) -> {
                if (id != null && processed.add(id)) {
                    promises.add(this.send(new DlvRequest.ClearBreakpoint(id)));
                }

            });
            return Promises.collectResults(promises, true);
        });
    }

    private void pauseIfNeededAndProcess(@NotNull Computable<Promise<?>> c) {


        AsyncPromise<Void> newPromise = new AsyncPromise<>();
        Promise<?> oldPromise = Objects.requireNonNull((Promise<?>)this.myCommandInProgress.getAndSet(newPromise));
        oldPromise.onProcessed((__) -> {
            this.pauseIfNeededAndProcess(c, newPromise);
        });
    }

    private void pauseIfNeededAndProcess(@NotNull Computable<Promise<?>> c, @NotNull AsyncPromise<?> callback) {


        if (!this.needToHalt()) {
            c.compute().onProcessed((__) -> callback.setResult(null));
        } else {
            this.myComplexCommandIsInProcess.set(true);
            this.send(new DlvRequest.State(true)).onSuccess((state) -> {
                if (state.running) {
                    this.send(new DlvRequest.Command("halt")).thenAsync((__) -> c.compute()).onProcessed((__) -> {
                        this.myComplexCommandIsInProcess.set(false);
                        if (this.connected()) {
                            this.command("continue");
                        }
                        callback.setResult(null);
                    });
                } else {
                    c.compute().onProcessed((__) -> {
                        this.myComplexCommandIsInProcess.set(false);
                        callback.setResult(null);
                    });
                }

            }).onError((error) -> {
                this.myComplexCommandIsInProcess.set(false);
                callback.setError(error);
            });
        }
    }

    private boolean needToHalt() {
        return this.myBreakpointsInitFinished.get() && this.connected() && !this.getSession().isPaused() && !this.myConsumingState.get();
    }

    public boolean connected() {
        return this.myConnection.getState().getStatus() == ConnectionStatus.CONNECTED;
    }

    private @Nullable String getDebuggerPath(@NotNull VirtualFile file) {


        return this.myPositionConverter != null ? this.myPositionConverter.toRemotePath(file) : file.getPath();
    }

    public @NotNull DlvValueRenderers getRenderers() {


        return this.myRenderers;
    }

    public static @Nullable DlvDebugProcess getCurrentProcess(@NotNull AnActionEvent e) {

        XDebugSession session = DebuggerUIUtil.getSession(e);
        return session != null ? ObjectUtils.tryCast(session.getDebugProcess(), DlvDebugProcess.class) : null;
    }

    @NotNull Promise<List<XStackFrame>> getStacktrace(long goroutineId) {
        AsyncPromise<List<XStackFrame>> promise = new AsyncPromise<>();
        runInBackground(() -> this.send(new DlvRequest.Stacktrace(goroutineId)).then((locations) -> {
            if (locations == null) {
                promise.setResult(Collections.emptyList());
            } else {
                ArrayList<XStackFrame> result = new ArrayList<>(locations.size());

                for(int i = 0; i < locations.size(); ++i) {
                    DlvStackFuncName stackFuncName = DlvSuspendContext.getStackFuncName(this, locations.get(i));
                    result.add(new DlvStackFrame(this, locations.get(i), stackFuncName, goroutineId, i, null, false));
                }

                promise.setResult(result);
            }

            return null;
        }));


        return promise;
    }

    public boolean isLibraryFrameFilterSupported() {
        return super.isLibraryFrameFilterSupported();
    }

    public @Nullable GlobalSearchScope getMainPackageScope() {
        VirtualFile mainFile = this.getMainFile();
        GoFile psiFile = mainFile != null ? ObjectUtils.tryCast(PsiManager.getInstance(this.getSession().getProject()).findFile(mainFile), GoFile.class) : null;
        return psiFile != null ? GoPackageUtil.packageScope(psiFile) : null;
    }

    @TestOnly
    public void setBreakpointReachedCallback(DlvBreakpointReachedCallback breakpointReachedCallback) {
        this.myBreakpointReachedCallback = breakpointReachedCallback;
    }

    public @NotNull XDebuggerEditorsProvider getEditorsProvider() {


        return this.myEditorsProvider;
    }

    public @Nullable XSmartStepIntoHandler<?> getSmartStepIntoHandler() {
        return this.mySmartStepIntoHandler;
    }

    public void startStepOver(@Nullable XSuspendContext context) {
        this.continueVmInBackground(StepAction.OVER);
    }

    public void startStepOut(@Nullable XSuspendContext context) {
        this.continueVmInBackground(StepAction.OUT);
    }

    public void resume(@Nullable XSuspendContext context) {
        this.continueVmInBackground(StepAction.CONTINUE);
    }

    public void startStepInto(@Nullable XSuspendContext context) {
        this.continueVmInBackground(StepAction.IN);
    }

    public void startPausing() {
        Vm vm = this.getActiveOrMainVm();
        if (vm != null) {
            vm.getSuspendContextManager().suspend().onError(new RejectErrorReporter(this.getSession(), GoBundle.message("go.debugger.cannot.pause")));
        } else {
            showNotification(this.getSession().getProject(), NotificationType.WARNING, GoBundle.message("go.debugger.cannot.pause"));
        }

    }

    private @Nullable Vm getActiveOrMainVm() {
        XDebugSession session = this.getSession();
        XSuspendContext context = session.getSuspendContext();
        if (context != null) {
            XExecutionStack stack = context.getActiveExecutionStack();
            if (stack instanceof ExecutionStackView) {
                return ((ExecutionStackView)stack).getSuspendContext().getVm();
            }
        }

        return this.myConnection.getVm();
    }

    public String getCurrentStateMessage() {
        ConnectionStatus status = this.myConnection.getState().getStatus();
        if (status == ConnectionStatus.WAITING_FOR_CONNECTION) {
            InetSocketAddress address = this.myAddress;
            return address != null ? GoBundle.message("go.debugger.connecting.to.address.message", address.toString()) : GoBundle.message("go.debugger.connecting.to.unknown.address.message");
        } else {
            return status == ConnectionStatus.CONNECTED && this.myRecordingInProgress ? GoBundle.message("go.debugger.recording.in.progress", "#stoprecording") : super.getCurrentStateMessage();
        }
    }

    public @Nullable HyperlinkListener getCurrentStateHyperlinkListener() {
        ConnectionStatus status = this.myConnection.getState().getStatus();
        return status == ConnectionStatus.CONNECTED && this.myRecordingInProgress ? e -> {
            if ("#stoprecording".equals(e.getDescription())) {
                DlvDebugProcess.this.stopRecording();
            }

        } : super.getCurrentStateHyperlinkListener();
    }

    public void stopRecording() {
        DlvVm.LOG.debug("Stop mozilla rr recording");
        this.send(new DlvRequest.StopRecording()).onError((exception) -> {
            DlvVm.LOG.warn("Failed to stop mozilla rr recording", exception);
            String details = exception != null ? exception.getMessage() : null;
            String message = StringUtil.isEmpty(details) ? GoBundle.message("go.debugger.failed.to.stop.mozilla.rr.recording") : GoBundle.message("go.debugger.failed.to.stop.mozilla.rr.recording.details", details);
            showNotification(this.getSession().getProject(), NotificationType.WARNING, message);
        });
    }

    boolean isRecorded() {
        return this.myRecorded;
    }

    private @NotNull Promise<DlvApi.Goroutine> findAnyGoroutine() {

        return this.send(new DlvRequest.ListGoroutines(0, 1)).then((goroutinesPack) -> ContainerUtil.getFirstItem(goroutinesPack.Goroutines));
    }

    private @NotNull Promise<DlvApi.Goroutine> findGoroutine(long goroutineId) {


        return this.send(new DlvRequest.ListGoroutines(0, 10000)).thenAsync((goroutinesPack) -> this.findGoroutineInner(goroutineId, goroutinesPack));
    }

    private @NotNull Promise<DlvApi.Goroutine> findGoroutineInner(long goroutineId, @NotNull DlvApi.@NotNull ListGoroutinesOut goroutinesPack) {


        Iterator<DlvApi.Goroutine> var4 = goroutinesPack.Goroutines.iterator();

        Promise<DlvApi.Goroutine> var10000;
        DlvApi.Goroutine goroutine;
        do {
            if (!var4.hasNext()) {
                if (goroutinesPack.Nextg == -1) {
                    var10000 = Promises.resolvedPromise(null);


                    return var10000;
                }

                var10000 = this.send(new DlvRequest.ListGoroutines(goroutinesPack.Nextg, 10000)).thenAsync((nextPack) -> this.findGoroutineInner(goroutineId, nextPack));

                return var10000;
            }

            goroutine = var4.next();
        } while(goroutine.id != goroutineId);

        var10000 = Promises.resolvedPromise(goroutine);


        return var10000;
    }

    private int getGoroutineWithStackPackSize() {
        return this.myGoroutineWithStackPackSize > 0 ? this.myGoroutineWithStackPackSize : 1000;
    }

    public void setGoroutineWithStackPackSize(int goroutineWithStackPackSize) {
        this.myGoroutineWithStackPackSize = goroutineWithStackPackSize;
    }

    private @NotNull Promise<DlvThreadDump> fillDump(@NotNull ProgressIndicator progress, @NotNull DlvThreadDump dump) {


        return Promises.runAsync(() -> {
            AtomicInteger count = new AtomicInteger(0);

            DlvApi.ListGoroutinesOut pack;
            try {
                for(int start = 0; start != -1; start = pack.Nextg) {
                    progress.checkCanceled();
                    pack = waitForPromise(progress, this.send(new DlvRequest.ListGoroutines(start, this.getGoroutineWithStackPackSize())));
                    if (pack == null) {
                        throw new ProcessCanceledException();
                    }

                    progress.checkCanceled();
                    CountDownLatch latch = new CountDownLatch(pack.Goroutines.size());
                    List<Promise<Pair<DlvApi.Goroutine, List<DlvApi.Location>>>> stacks = new ArrayList<>(pack.Goroutines.size());

                    for (DlvApi.Goroutine goroutine : pack.Goroutines) {
                        Promise<Pair<DlvApi.Goroutine, List<DlvApi.Location>>> stackPromisex = this.send(new DlvRequest.Stacktrace(goroutine.id)).then((stackx) -> {
                            progress.setText(GoBundle.message("go.debugger.create.dump.background.task.progress.text", count.incrementAndGet()));
                            return Pair.create(goroutine, stackx);
                        });
                        stacks.add(stackPromisex);
                        stackPromisex.onProcessed((it) -> latch.countDown());
                    }

                    waitForLatch(progress, latch);

                    for (Promise<Pair<DlvApi.Goroutine, List<DlvApi.Location>>> stackPromise : stacks) {
                        if (stackPromise.isSucceeded()) {
                            try {
                                Pair<DlvApi.Goroutine, List<DlvApi.Location>> stack = stackPromise.blockingGet(0);
                                if (stack != null) {
                                    dump.addThread(stack.first, stack.second);
                                }
                            } catch (ExecutionException | TimeoutException ignored) {
                            }
                        }
                    }
                }
            } catch (ProcessCanceledException var12) {
                dump.setIncomplete();
                if (dump.getThreadCount() == 0) {
                    throw var12;
                }
            }

            dump.sort();
            return dump;
        });
    }

    private static void waitForLatch(@NotNull ProgressIndicator progress, @NotNull CountDownLatch latch) {


        while(!progress.isCanceled()) {
            try {
                if (latch.await(1L, TimeUnit.SECONDS)) {
                    break;
                }
            } catch (InterruptedException var3) {
                break;
            }
        }

    }

    private static <T> @Nullable T waitForPromise(@NotNull ProgressIndicator progress, @NotNull Promise<T> promise) {


        while(true) {
            progress.checkCanceled();

            try {
                return promise.blockingGet(1, TimeUnit.SECONDS);
            } catch (TimeoutException ignored) {
            } catch (ExecutionException var4) {
                throw new RuntimeException(var4);
            }
        }
    }

    public void registerAdditionalActions(@NotNull DefaultActionGroup leftToolbar, @NotNull DefaultActionGroup topToolbar, @NotNull DefaultActionGroup settings) {


        if (!UIExperiment.isNewDebuggerUIEnabled()) {
            Constraints afterMute = new Constraints(Anchor.AFTER, "XDebugger.MuteBreakpoints");
            leftToolbar.add(Separator.getInstance(), afterMute);
            leftToolbar.addAction(ActionManager.getInstance().getAction("DlvDumpAction"), afterMute);
            leftToolbar.add(Separator.getInstance(), afterMute);
        }

    }

    public boolean isThreadDumpInProgress() {
        return this.myThreadDumpSemaphore.availablePermits() == 0;
    }

    public boolean checkCanPerformCommands() {
        XDebugSession session = this.getSession();
        if (this.isThreadDumpInProgress()) {
            showNotification(session.getProject(), NotificationType.WARNING, GoBundle.message("go.debugger.thread.dump.is.in.progress", new Object[0]));
            return false;
        } else if (this.myComplexCommandIsInProcess.get()) {
            showNotification(session.getProject(), NotificationType.WARNING, GoBundle.message("go.debugger.another.command.is.in.progress", new Object[0]));
            return false;
        } else {
            return true;
        }
    }

    public void setLastSelectedDump(@NotNull DlvThreadDump dump) {


        this.myLastSelectedDump.set(dump);
    }

    public @Nullable DlvThreadDump getLastSelectedDump() {
        return (DlvThreadDump)this.myLastSelectedDump.get();
    }

    public @NotNull DlvSymbolPrettier getSymbolPrettier() {


        return this.mySymbolPrettier;
    }

    public @NotNull VmConnection<?> getConnection() {


        return this.myConnection;
    }

    static void runInBackground(@NotNull Runnable r) {


        if (ApplicationManager.getApplication().isDispatchThread()) {
            ApplicationManager.getApplication().executeOnPooledThread(r);
        } else {
            r.run();
        }

    }

    public static @NotNull Pair<@NotNull String, @NotNull String> getBundledDelveArtifactVersionAndCommit() throws IOException {
        InputStream stream = DlvDebugProcess.class.getClassLoader().getResourceAsStream("delve.properties");

        Pair<String, String> var4;
        try {
            if (stream == null) {
                throw new IllegalStateException("Bundled delve properties not found");
            }

            PropertyResourceBundle bundle = new PropertyResourceBundle(stream);
            String delveArtifactVersion = bundle.getString("delveVersion");
            if (StringUtil.isEmptyOrSpaces(delveArtifactVersion)) {
                throw new IllegalStateException("Delve version is missing");
            }

            String delveCommit = bundle.getString("delveCommit");
            if (StringUtil.isEmptyOrSpaces(delveCommit)) {
                throw new IllegalStateException("Delve commit is missing");
            }

            var4 = Pair.create(delveArtifactVersion, delveCommit);
        } catch (Throwable var6) {
            if (stream != null) {
                try {
                    stream.close();
                } catch (Throwable var5) {
                    var6.addSuppressed(var5);
                }
            }

            throw var6;
        }

        stream.close();

        return var4;
    }

    public static @Nullable String getDelveCommit(@Nullable String delveVersion) {
        if (delveVersion == null) {
            return null;
        } else {
            String commitPrefix = "Build: ";
            String[] var2 = StringUtil.splitByLines(delveVersion);

            for (String line : var2) {
                if (line.startsWith(commitPrefix)) {
                    return StringUtil.trimStart(line, commitPrefix).trim();
                }
            }

            return null;
        }
    }

    private final class MyBreakpointHandler extends XBreakpointHandler<XLineBreakpoint<DlvBreakpointProperties>> {
        private MyBreakpointHandler() {
            super(DlvBreakpointType.class);
        }

        public void registerBreakpoint(@NotNull XLineBreakpoint<DlvBreakpointProperties> breakpoint) {
            XSourcePosition breakpointPosition = breakpoint.getSourcePosition();
            if (breakpointPosition != null) {
                VirtualFile file = breakpointPosition.getFile();
                int line = breakpointPosition.getLine();
                XExpression expression = breakpoint.getConditionExpression();
                String condition = expression != null ? expression.getExpression() : null;
                String debuggerPath = DlvDebugProcess.this.getDebuggerPath(file);
                if (debuggerPath == null) {
                    DlvDebugProcess.this.getSession().setBreakpointInvalid(breakpoint, GoBundle.message("go.debugger.cannot.find.debugger.path", file.getPath()));
                } else {
                    DlvDebugProcess.this.pauseIfNeededAndProcess(() -> this.sendSetBreakpoint(breakpoint, line, condition, debuggerPath));
                }

            }
        }

        public void unregisterBreakpoint(@NotNull XLineBreakpoint<DlvBreakpointProperties> breakpoint, boolean temporary) {

            XSourcePosition breakpointPosition = breakpoint.getSourcePosition();
            if (breakpointPosition != null) {
                DlvDebugProcess.this.pauseIfNeededAndProcess(() -> {
                    Integer id = DlvDebugProcess.this.myBreakpoints.remove(breakpoint);
                    return id != null ? DlvDebugProcess.this.send(new DlvRequest.ClearBreakpoint(id)) : Promises.resolvedPromise();
                });
            }
        }

        private Promise<DlvApi.Breakpoint> sendSetBreakpoint(@NotNull XLineBreakpoint<DlvBreakpointProperties> breakpoint, int line, @Nullable String condition, @NotNull String path) {

            return DlvDebugProcess.this.send(new DlvRequest.CreateBreakpoint(path, line + 1, condition)).onSuccess((b) -> {
                DlvDebugProcess.this.myBreakpoints.put(breakpoint, b.id);
                DlvDebugProcess.this.getSession().setBreakpointVerified(breakpoint);
            }).onError((t) -> {
                String message = t == null ? null : t.getMessage();
                if (message != null && message.equals("could not find file " + path)) {
                    message = GoBundle.message("go.debugger.no.debug.information.for.file", path);
                }

                DlvDebugProcess.this.getSession().setBreakpointInvalid(breakpoint, message);
            });
        }
    }
}