package com.sentinel.nocalhost.configuration.go;

import com.intellij.execution.Executor;
import com.intellij.execution.configurations.ConfigurationFactory;
import com.intellij.execution.configurations.LocatableConfigurationBase;
import com.intellij.execution.configurations.RunConfiguration;
import com.intellij.execution.configurations.RunProfileState;
import com.intellij.execution.runners.ExecutionEnvironment;
import com.intellij.openapi.options.SettingsEditor;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.configuration.NocalhostConfiguration;
import com.sentinel.nocalhost.configuration.NocalhostProfileState;
import com.sentinel.nocalhost.configuration.NocalhostSettingsEditor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class NocalhostGoConfiguration
        extends LocatableConfigurationBase<NocalhostGoConfiguration>
        implements NocalhostConfiguration {
    protected NocalhostGoConfiguration(@NotNull Project project,
                                       @NotNull ConfigurationFactory factory) {
        super(project, factory);
    }

    @Override
    public @NotNull SettingsEditor<? extends RunConfiguration> getConfigurationEditor() {
        return new NocalhostSettingsEditor();
    }

    @Override
    public @Nullable RunProfileState getState(
            @NotNull Executor executor,
            @NotNull ExecutionEnvironment environment) {
        return new NocalhostProfileState(environment);
    }
}
