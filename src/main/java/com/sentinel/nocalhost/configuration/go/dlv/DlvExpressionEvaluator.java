package com.sentinel.nocalhost.configuration.go.dlv;


import com.goide.i18n.GoBundle;
import com.goide.psi.GoBuiltinCallExpr;
import com.goide.psi.GoCallExpr;
import com.goide.psi.GoConstDefinition;
import com.goide.psi.GoExpression;
import com.goide.psi.GoFile;
import com.goide.psi.GoParamDefinition;
import com.goide.psi.GoStatement;
import com.goide.psi.GoTypeOwner;
import com.goide.psi.GoVarDefinition;
import com.goide.psi.impl.GoElementFactory;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.project.IndexNotReadyException;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Computable;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.util.concurrency.AppExecutorUtil;
import com.intellij.xdebugger.XSourcePosition;
import com.intellij.xdebugger.evaluation.ExpressionInfo;
import com.intellij.xdebugger.evaluation.XDebuggerEvaluator;
import com.intellij.xdebugger.frame.XStackFrame;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;


class DlvExpressionEvaluator extends XDebuggerEvaluator {
    private final DlvDebugProcess myProcess;
    private final DlvApi.Location myLocation;
    private final int myFrameId;
    private final long myGoroutineId;

    DlvExpressionEvaluator(@NotNull DlvDebugProcess process, @NotNull DlvApi.@NotNull Location location, int frameId, long goroutineId) {


        super();
        this.myProcess = process;
        this.myLocation = location;
        this.myFrameId = frameId;
        this.myGoroutineId = goroutineId;
    }

    public void evaluate(@NotNull String expression, @NotNull XDebuggerEvaluator.@NotNull XEvaluationCallback callback, @Nullable XSourcePosition expressionPosition) {


        if (this.hasCalls(expression)) {
            if (this.myFrameId != 0) {
                callback.errorOccurred(GoBundle.message("go.debugger.cannot.evaluate.call", new Object[]{GoBundle.message("go.debugger.non.top.frame.selected", new Object[0])}));
            } else if (this.myLocation.function == null) {
                callback.errorOccurred(GoBundle.message("go.debugger.cannot.evaluate.call", new Object[]{GoBundle.message("go.debugger.missing.current.function", new Object[0])}));
            } else {
                this.myProcess.getStacktrace(this.myGoroutineId).thenAsync((stack) -> {
                    return this.myProcess.send(new DlvRequest.Call(expression)).thenAsync((state) -> {
                        return this.handleCallResult(stack, expression, callback, state);
                    }).onError((throwable) -> {
                        String message = throwable.getMessage();
                        if (message.contains("could not find symbol value for")) {
                            message = message + GoBundle.message("go.debugger.unused.functions.are.not.included.message", new Object[0]);
                        } else if (message.contains(" has no member ")) {
                            message = message + GoBundle.message("go.debugger.unused.methods.are.not.included.message", new Object[0]);
                        }

                        callback.errorOccurred(message);
                    });
                });
            }
        } else {
            this.myProcess.send(new DlvRequest.Eval(expression, this.myFrameId, this.myGoroutineId)).onSuccess((variable) -> {
                callback.evaluated(DlvXValue.create(expression, this.myProcess, (DlvXValue)null, variable, this.myFrameId, this.myGoroutineId, AllIcons.Debugger.Watch));
            }).onError((throwable) -> {
                callback.errorOccurred(throwable.getMessage());
            });
        }

    }

    private boolean hasCalls(@NotNull String expression) {


        return ApplicationManager.getApplication().runReadAction((Computable<Boolean>) () -> {
            GoFile file = GoElementFactory.createFileFromText(this.myProcess.getSession().getProject(), "package a; func a() { " + expression + "}");
            GoStatement statement = PsiTreeUtil.findChildOfType(file, GoStatement.class);
            return statement != null && PsiTreeUtil.findChildOfType(statement, GoCallExpr.class) != null;
        });
    }

    private static @Nullable PsiElement findElementAt(@Nullable PsiFile file, int offset) {
        return file != null ? file.findElementAt(offset) : null;
    }

    public @NotNull Promise<ExpressionInfo> getExpressionInfoAtOffsetAsync(@NotNull Project project, @NotNull Document document, int offset, boolean sideEffectsAllowed) {


        return ReadAction.nonBlocking(() -> {
            GoTypeOwner e = null;

            try {
                PsiElement elementAtCursor = findElementAt(PsiDocumentManager.getInstance(project).getPsiFile(document), offset);

                e = PsiTreeUtil.getParentOfType(elementAtCursor,
                        GoExpression.class,
                        GoVarDefinition.class,
                        GoConstDefinition.class,
                        GoParamDefinition.class);
                if (!sideEffectsAllowed && containsCalls(e)) {
                    e = null;
                }
            } catch (IndexNotReadyException ignored) {
            }

            return e != null ? new ExpressionInfo(e.getTextRange()) : null;
        }).withDocumentsCommitted(project).submit(AppExecutorUtil.getAppExecutorService());
    }

    private static boolean containsCalls(@Nullable PsiElement element) {
        return PsiTreeUtil.findChildOfType(element, GoCallExpr.class, false) != null || PsiTreeUtil.findChildOfType(element, GoBuiltinCallExpr.class, false) != null;
    }

    private @NotNull Promise<Void> handleCallResult(@NotNull List<XStackFrame> initialStack, @NotNull String expression, @NotNull XDebuggerEvaluator.@NotNull XEvaluationCallback callback, @NotNull DlvApi.@NotNull DebuggerState state) {

        DlvApi.Location currentLocation = DlvUtil.getCurrentLocation(state);
        boolean inInitialLocation = currentLocation != null && Objects.equals(getFunctionName(this.myLocation), getFunctionName(currentLocation)) && this.myLocation.line == currentLocation.line;

        Promise<Void> var10000;
        if (inInitialLocation) {
            var10000 = this.myProcess.getStacktrace(this.myGoroutineId)
                    .thenAsync((stack) -> {
                        if (stack.size() > initialStack.size()) {
                            return this.myProcess.send(new DlvRequest.ContinueCall())
                                    .thenAsync((s) -> this.handleCallResult(initialStack, expression, callback, s));
                        } else {
                            DlvApi.Thread thread = state.currentThread;
                            List<DlvApi.Variable> returnValues = thread != null ? thread.returnValues : Collections.emptyList();
                            callback.evaluated(DlvXValue.createCallResult(expression, returnValues, this.myProcess, this.myFrameId, this.myGoroutineId));
                            return Promises.resolvedPromise(null); // 注意这里返回的是Void类型的Promise
                        }
                    }); // 需要显式转换为Promise<Void>

            return var10000;
        } else {
            var10000 = this.myProcess.send(new DlvRequest.ContinueCall())
                    .thenAsync((s) -> {
                        return this.handleCallResult(initialStack, expression, callback, s);
                    });

            return var10000; // 同样，这里也需要显式转换为Promise<Void>
        }
    }


    private static @Nullable String getFunctionName(@NotNull DlvApi.@NotNull Location loc) {
    

        DlvApi.Function function = loc.function;
        return function != null ? function.name : null;
    }
}
