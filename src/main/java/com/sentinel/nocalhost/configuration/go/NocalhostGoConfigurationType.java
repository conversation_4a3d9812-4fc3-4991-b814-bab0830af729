package com.sentinel.nocalhost.configuration.go;

import com.intellij.execution.configurations.ConfigurationType;
import icons.NocalhostIcons;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;

public class NocalhostGoConfigurationType implements ConfigurationType {
    @Override
    @NotNull
    @Nls(capitalization = Nls.Capitalization.Title)
    public String getDisplayName() {
        return "JetDev Go";
    }

    @Override
    @Nls(capitalization = Nls.Capitalization.Sentence)
    public String getConfigurationTypeDescription() {
        return "Run and debug go application with JetDev";
    }

    @Override
    public Icon getIcon() {
        return NocalhostIcons.Golang;
    }

    @Override
    @NotNull
    @NonNls
    public String getId() {
        return "NocalhostGoConfigurationType";
    }

    @Override
    public NocalhostGoConfigurationFactory[] getConfigurationFactories() {
        return new NocalhostGoConfigurationFactory[]{new NocalhostGoConfigurationFactory(this)};
    }
}
