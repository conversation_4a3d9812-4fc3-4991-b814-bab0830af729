package com.sentinel.nocalhost.configuration.go.dlv.dump;

import it.unimi.dsi.fastutil.objects.Object2IntMap;
import it.unimi.dsi.fastutil.objects.Object2IntOpenHashMap;
import java.util.ArrayList;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: IntTable.class */
public final class IntTable<T> {
    private final IntTable<T> myBase;
    private final int myBaseSize;
    private final Object2IntMap<T> myIdMap;
    private final List<T> myValues;

    public IntTable(@Nullable IntTable<T> base) {
        this.myBase = base;
        this.myBaseSize = this.myBase != null ? this.myBase.size() : 0;
        this.myIdMap = new Object2IntOpenHashMap<>();
        this.myValues = new ArrayList<>();
        this.myValues.add(null);
    }

    public int addIfAbsent(@Nullable T value) {
        if (value == null) {
            return 0;
        }
        int id = getExistingId(value);
        if (id != 0) {
            return id;
        }
        int index = this.myValues.size();
        int id2 = index + this.myBaseSize;
        this.myIdMap.put(value, id2);
        this.myValues.add(value);
        return id2;
    }

    private int getExistingId(@Nullable T value) {
        IntTable<T> intTable = this;
        while (true) {
            IntTable<T> table = intTable;
            if (table != null) {
                int id = table.myIdMap.getInt(value);
                if (id != 0) {
                    return id;
                }
                intTable = table.myBase;
            } else {
                return 0;
            }
        }
    }

    @Nullable
    public T get(int id) {
        int index;
        IntTable<T> table = findTableForId(id);
        if (table != null && (index = id - table.myBaseSize) >= 0 && index < table.myValues.size()) {
            return table.myValues.get(index);
        }
        return null;
    }

    @Nullable
    private IntTable<T> findTableForId(int id) {
        IntTable<T> intTable = this;
        while (true) {
            IntTable<T> table = intTable;
            if (table != null) {
                if (id > table.myBaseSize) {
                    return table;
                }
                intTable = table.myBase;
            } else {
                return null;
            }
        }
    }

    public int size() {
        return this.myBaseSize + this.myIdMap.size();
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public List<T> getOwnValues() {

        return this.myValues.subList(1, this.myValues.size());
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public IntTable<T> getBase() {
        return this.myBase;
    }
}
