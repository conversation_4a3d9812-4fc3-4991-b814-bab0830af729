package com.sentinel.nocalhost.configuration.go.dlv.location;

import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.search.FilenameIndex;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.util.PathUtil;
import com.intellij.util.containers.ContainerUtil;
import com.sentinel.nocalhost.configuration.go.dlv.DlvVm;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Comparator;
import java.util.List;
import java.util.Set;

/* loaded from: DefaultDlvPositionConverter.class */
public class DefaultDlvPositionConverter implements DlvPositionConverter {

    @NotNull
    protected final Project myProject;

    @NotNull
    protected final Set<String> myRemotePaths;

    public DefaultDlvPositionConverter(@NotNull Project project, @NotNull Set<String> remotePaths) {
        this.myProject = project;
        this.myRemotePaths = remotePaths;
    }

    @Override // com.goide.dlv.location.DlvPositionConverter
    @Nullable
    public String toRemotePath(@NotNull VirtualFile localFile) {
        VirtualFile parent;

        String path = localFile.getPath();
        if (this.myRemotePaths.contains(path)) {
            DlvVm.LOG.debug("Exact path match for local file " + path);
            return path;
        }
        String remotePathBySuffix = findRemotePathBySuffix(localFile);
        if (remotePathBySuffix != null && (parent = localFile.getParent()) != null) {
            VirtualFile bestMatch = getBestMatch(remotePathBySuffix, localFile.getName(), parent.getName());
            if (localFile.equals(bestMatch)) {
                return remotePathBySuffix;
            }
            if (bestMatch != null) {
                DlvVm.LOG.debug("Several files can be mapped to remote path " + remotePathBySuffix + ": " + path + ", " + bestMatch.getPath());
                return null;
            }
            DlvVm.LOG.debug("Cannot check best match for remote path " + remotePathBySuffix);
            return null;
        }
        return null;
    }

    @Override // com.goide.dlv.location.DlvPositionConverter
    @Nullable
    public VirtualFile toLocalFile(@NotNull String remotePath) {

        String name = PathUtil.getFileName(remotePath);
        String parentName = PathUtil.getFileName(PathUtil.getParentPath(remotePath));
        return getBestMatch(remotePath, name, parentName);
    }

    @Nullable
    private VirtualFile getBestMatch(@NotNull String remotePath, String name, String parentName) {

        List<VirtualFile> collect = ReadAction.compute(() -> ContainerUtil.filter(FilenameIndex.getVirtualFilesByName(name, GlobalSearchScope.allScope(this.myProject)), it -> it.getParent() != null && parentName.equals(it.getParent().getName())));
        return collect.size() == 1 ? collect.get(0) : collect.stream().min(Comparator.comparingInt(v -> StringUtil.difference(v.getPath(), remotePath))).orElse(null);
    }

    @Nullable
    private String findRemotePathBySuffix(@NotNull VirtualFile localFile) {
        String path = localFile.getPath();
        return findUniqueRemotePathBySuffix(path.replaceFirst("^" + myProject.getBasePath(), ""));
    }

    @Nullable
    protected String findUniqueRemotePathBySuffix(@Nullable String suffix) {
        if (suffix != null) {
            return ContainerUtil.getOnlyItem(ContainerUtil.filter(this.myRemotePaths, it -> it.endsWith(suffix)));
        }
        return null;
    }
}
