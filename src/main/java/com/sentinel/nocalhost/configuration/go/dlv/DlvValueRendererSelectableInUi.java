package com.sentinel.nocalhost.configuration.go.dlv;

import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: DlvValueRendererSelectableInUi.class */
public interface DlvValueRendererSelectableInUi extends DlvValueRenderer {
    @Nls
    @NotNull
    String getName();

    boolean isApplicable(@Nullable DlvXValue dlvXValue);
}
