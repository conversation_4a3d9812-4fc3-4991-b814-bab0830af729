package com.sentinel.nocalhost.configuration.go.dlv.dump;

import com.goide.i18n.GoBundle;
import com.intellij.execution.filters.HyperlinkInfo;
import com.intellij.execution.ui.ConsoleView;
import com.intellij.execution.ui.ConsoleViewContentType;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ModalityState;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.fileEditor.OpenFileDescriptor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.ui.ColoredTextContainer;
import com.intellij.util.ObjectUtils;
import com.intellij.util.concurrency.AppExecutorUtil;
import com.sentinel.nocalhost.configuration.go.dlv.DlvDebugProcess;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import it.unimi.dsi.fastutil.ints.Int2ByteMap;
import it.unimi.dsi.fastutil.ints.Int2ByteOpenHashMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2ByteMap;
import it.unimi.dsi.fastutil.longs.Long2ByteOpenHashMap;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.jetbrains.annotations.Contract;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: DlvStack.class */
public final class DlvStack {
    private static final int FRAME_SIZE = 3;
    private static final String RUNTIME_PREFIX = "runtime.";
    private final int myHash;
    private final int[] myFrames;
    private final int myAsyncFrameIndex;

    /* JADX INFO: Access modifiers changed from: package-private */
    public DlvStack(@NotNull DlvStackIndex stackIndex, @NotNull List<DlvApi.Location> frames, int asyncFrameIndex) {

        this.myFrames = new int[frames.size() * FRAME_SIZE];
        for (int i = 0; i < frames.size(); i++) {
            DlvApi.Location location = frames.get(i);
            DlvApi.Function function = location.function;
            String funcName = function != null ? function.name : null;
            this.myFrames[FRAME_SIZE * i] = stackIndex.addFuncIfAbsent(funcName);
            this.myFrames[(FRAME_SIZE * i) + 1] = stackIndex.addFileIfAbsent(location.file);
            this.myFrames[(FRAME_SIZE * i) + 2] = location.line;
        }
        this.myAsyncFrameIndex = asyncFrameIndex;
        this.myHash = Arrays.hashCode(this.myFrames) + (31 * this.myAsyncFrameIndex);
    }

    public void renderFrame(@NotNull DlvStackIndex stackIndex, @NotNull ColoredTextContainer renderer, int frameIndex, @NotNull String unknownFuncName, boolean hidden) {

        String funcName = ObjectUtils.notNull(stackIndex.getFuncName(this.myFrames[frameIndex * FRAME_SIZE]), unknownFuncName);
        DlvThreadDump.append(renderer, funcName, hidden);
    }

    public void renderStack(@NotNull DlvDebugProcess process, @NotNull DlvStackIndex stackIndex, @NotNull ConsoleView console, @NotNull String unknownFuncName) {

        for (int i = 0; i < this.myFrames.length / FRAME_SIZE; i++) {
            int base = i * FRAME_SIZE;
            String func = (String) ObjectUtils.notNull(stackIndex.getFuncName(this.myFrames[base]), unknownFuncName);
            ConsoleViewContentType contentType = getLineContentType(func, i);
            console.print(func, contentType);
            String file = stackIndex.getFile(this.myFrames[base + 1]);
            int line = this.myFrames[base + 2];
            if (file != null && line >= 0) {
                console.print("(", contentType);
                console.printHyperlink(getShortFileName(file) + ":" + line, new FileHyperlinkInfo(process, file, line));
                console.print(")", contentType);
            }
            console.print("\n", contentType);
        }
    }

    public void print(@NotNull DlvStackIndex stackIndex, @NotNull String unknownFuncName, @NotNull StringBuilder buffer) {

        for (int i = 0; i < this.myFrames.length / FRAME_SIZE; i++) {
            int base = i * FRAME_SIZE;
            String func = ObjectUtils.notNull(stackIndex.getFuncName(this.myFrames[base]), unknownFuncName);
            buffer.append("  ");
            if (i == this.myAsyncFrameIndex) {
                buffer.append("created at: ");
            }
            buffer.append(func);
            String file = stackIndex.getFile(this.myFrames[base + 1]);
            int line = this.myFrames[base + 2];
            if (file != null && line >= 0) {
                buffer.append(" (");
                buffer.append(file).append(":").append(line);
                buffer.append(")");
            }
            buffer.append("\n");
        }
    }

    @NotNull
    private ConsoleViewContentType getLineContentType(@NotNull String funcName, int frameIndex) {

        if (!isUserLocation(funcName)) {

            return ConsoleViewContentType.LOG_DEBUG_OUTPUT;
        }
        if (frameIndex == this.myAsyncFrameIndex) {

            return ConsoleViewContentType.LOG_VERBOSE_OUTPUT;
        }

        return ConsoleViewContentType.NORMAL_OUTPUT;
    }

    private static boolean isUserLocation(@NotNull String funcName) {

        if (funcName.length() <= RUNTIME_PREFIX.length() || !funcName.startsWith(RUNTIME_PREFIX)) {
            return funcName.indexOf(46) >= 0;
        }
        return Character.isUpperCase(funcName.charAt(RUNTIME_PREFIX.length()));
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DlvStack stack = (DlvStack) o;
        return this.myHash == stack.myHash && Arrays.equals(this.myFrames, stack.myFrames) && this.myAsyncFrameIndex == stack.myAsyncFrameIndex;
    }

    public int hashCode() {
        return this.myHash;
    }

    @Contract("!null->!null")
    private static String getShortFileName(@Nullable String file) {
        if (file == null) {
            return null;
        }
        int lastSlashIndex = file.lastIndexOf("/");
        if (lastSlashIndex >= 0) {
            return file.substring(lastSlashIndex + 1);
        }
        return file;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* loaded from: DlvStack$FileHyperlinkInfo.class */
    public static class FileHyperlinkInfo implements HyperlinkInfo {
        private final DlvDebugProcess myProcess;
        private final String myFile;
        private final int myLine;



        private FileHyperlinkInfo(@NotNull DlvDebugProcess process, @NotNull String file, int line) {

            this.myProcess = process;
            this.myFile = file;
            this.myLine = line;
        }

        public void navigate(@NotNull Project project) {

            ReadAction.nonBlocking(() -> this.myProcess.findFile(this.myFile)).expireWith(this.myProcess).finishOnUiThread(ModalityState.defaultModalityState(), vfile -> {
                if (vfile != null) {
                    new OpenFileDescriptor(project, vfile, this.myLine - 1, 0).navigate(true);
                } else {
                    String msg = GoBundle.message("go.debugger.cannot.find.file", new Object[]{this.myFile});
                    DlvDebugProcess.showNotification(this.myProcess.getSession().getProject(), NotificationType.WARNING, msg);
                }
            }).submit(AppExecutorUtil.getAppExecutorService());
        }
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    /* loaded from: DlvStack$ContentMatcher.class */
    public static class ContentMatcher {
        private static final byte NOT_COMPUTED = 0;
        private static final byte MATCHED = 1;
        private final DlvStackIndex myStackIndex;
        private final String myUnknownFunctionName;
        private final String myText;
        private final boolean myIgnoreCase;
        private final StringBuilder myStackLine;
        private final Int2ByteMap myStackMatchResults;
        private final Int2ObjectMap<Long2ByteMap> myStackLineMatchResults;



        /* JADX INFO: Access modifiers changed from: package-private */
        public ContentMatcher(@NotNull DlvStackIndex stackIndex, @NotNull String unknownFunctionName, @NotNull String text, boolean ignoreCase) {

            this.myStackMatchResults = new Int2ByteOpenHashMap();
            this.myStackLineMatchResults = new Int2ObjectOpenHashMap<>();
            this.myStackIndex = stackIndex;
            this.myUnknownFunctionName = unknownFunctionName;
            this.myText = text;
            this.myIgnoreCase = ignoreCase;
            this.myStackLine = new StringBuilder();
        }

        /* JADX INFO: Access modifiers changed from: package-private */
        public boolean matches(int stackId) {
            byte stackResult = this.myStackMatchResults.get(stackId);
            if (stackResult != 0) {
                return stackResult == MATCHED;
            }
            DlvStack stack = Objects.requireNonNull(this.myStackIndex.getStack(stackId));
            for (int i = NOT_COMPUTED; i < stack.myFrames.length; i += DlvStack.FRAME_SIZE) {
                int funcId = stack.myFrames[i];
                int fileId = stack.myFrames[i + MATCHED];
                int line = stack.myFrames[i + 2];
                long funcAndLine = ((long) funcId << 32) | line;
                Long2ByteOpenHashMap long2ByteOpenHashMap = (Long2ByteOpenHashMap) this.myStackLineMatchResults.get(fileId);
                if (long2ByteOpenHashMap == null) {
                    long2ByteOpenHashMap = new Long2ByteOpenHashMap();
                    this.myStackLineMatchResults.put(fileId, long2ByteOpenHashMap);
                }
                byte lineMatchResult = long2ByteOpenHashMap.get(funcAndLine);
                if (lineMatchResult == 0) {
                    fillStackLine(funcId, fileId, line);
                    lineMatchResult = this.myStackLine.indexOf(this.myText) >= 0 ? (byte) 1 : (byte) 2;
                    long2ByteOpenHashMap.put(funcAndLine, lineMatchResult);
                }
                if (lineMatchResult == MATCHED) {
                    this.myStackMatchResults.put(stackId, (byte) 1);
                    return true;
                }
            }
            this.myStackMatchResults.put(stackId, (byte) 2);
            return false;
        }

        private void fillStackLine(int funcId, int fileId, int line) {
            this.myStackLine.setLength(NOT_COMPUTED);
            String funcName = ObjectUtils.notNull(this.myStackIndex.getFuncName(funcId), this.myUnknownFunctionName);
            this.myStackLine.append(this.myIgnoreCase ? StringUtil.toLowerCase(funcName) : funcName);
            String file = this.myStackIndex.getFile(fileId);
            if (file != null && line >= 0) {
                String shortFileName = DlvStack.getShortFileName(file);
                this.myStackLine.append("(").append(this.myIgnoreCase ? StringUtil.toLowerCase(shortFileName) : shortFileName).append(":").append(line).append(")");
            }
        }
    }
}
