package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.i18n.GoBundle;
import com.goide.psi.GoCallExpr;
import com.goide.psi.GoFieldDefinition;
import com.goide.psi.GoFile;
import com.goide.psi.GoFunctionDeclaration;
import com.goide.psi.GoFunctionType;
import com.goide.psi.GoMethodDeclaration;
import com.goide.psi.GoMethodSpec;
import com.goide.psi.GoNamedElement;
import com.goide.psi.GoParamDefinition;
import com.goide.psi.GoParameters;
import com.goide.psi.GoQualifier;
import com.goide.psi.GoReferenceExpression;
import com.goide.psi.GoSignature;
import com.goide.psi.GoSignatureOwner;
import com.goide.psi.GoType;
import com.goide.psi.GoTypeOwner;
import com.goide.psi.GoTypeSpec;
import com.goide.psi.GoVarDefinition;
import com.goide.psi.presentation.GoPsiPresentationBuilder;
import com.goide.vendor.GoVendoringUtil;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.util.Ref;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.PsiElement;
import com.intellij.psi.ResolveState;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.util.ObjectUtils;
import com.intellij.util.Processor;
import com.intellij.util.containers.ContainerUtil;
import com.intellij.xdebugger.XDebugSession;
import com.intellij.xdebugger.XDebuggerUtil;
import com.intellij.xdebugger.XSourcePosition;
import com.intellij.xdebugger.frame.XSuspendContext;
import com.intellij.xdebugger.stepping.XSmartStepIntoHandler;
import com.intellij.xdebugger.stepping.XSmartStepIntoVariant;

import java.util.*;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;


public class DlvSmartStepIntoHandler extends XSmartStepIntoHandler<DlvSmartStepIntoHandler.Variant> {
    private final XDebugSession mySession;

    DlvSmartStepIntoHandler(@NotNull XDebugSession session) {

        super();
        this.mySession = session;
    }

    public @NotNull List<DlvSmartStepIntoHandler.Variant> computeSmartStepVariants(@NotNull XSourcePosition position) {


        Document document = FileDocumentManager.getInstance().getDocument(position.getFile());
        List<DlvSmartStepIntoHandler.Variant> var10000;
        if (document == null) {
            var10000 = Collections.emptyList();


            return var10000;
        } else {
            DlvDebugProcess process = ObjectUtils.tryCast(this.mySession.getDebugProcess(), DlvDebugProcess.class);
            if (process == null) {
                var10000 = Collections.emptyList();


                return var10000;
            } else {
                DlvStackFrame frame = ObjectUtils.tryCast(this.mySession.getCurrentStackFrame(), DlvStackFrame.class);
                if (frame == null) {
                    var10000 = Collections.emptyList();

                    return var10000;
                } else {
                    int line = position.getLine();
                    int minOffset = document.getLineStartOffset(line);
                    DlvSmartStepIntoHandler.CallProcessor processor = new DlvSmartStepIntoHandler.CallProcessor(process, document, frame.getGoroutineId(), minOffset, line);
                    XDebuggerUtil.getInstance().iterateLine(this.mySession.getProject(), document, line, processor);
                    var10000 = ContainerUtil.sorted(processor.getVariants());


                    return var10000;
                }
            }
        }
    }

    public void startStepInto(@NotNull DlvSmartStepIntoHandler.Variant variant, @Nullable XSuspendContext context) {

        this.stepInto(variant);
    }

    void stepInto(@NotNull DlvSmartStepIntoHandler.Variant variant) {


        variant.stepInto();
    }

    public String getPopupTitle(@NotNull XSourcePosition position) {


        return GoBundle.message("go.debugger.smart.step.into.popup.title");
    }

    private static final class CallProcessor implements Processor<PsiElement> {
        private final DlvDebugProcess myProcess;
        private final Document myDocument;
        private final long myGoroutineId;
        private final int myMinOffset;
        private final Ref<Integer> myMaxLine;
        private final List<DlvSmartStepIntoHandler.Variant> myVariants;
        private final Set<PsiElement> myVisitedCalls;
        private final Set<PsiElement> myVisitedResolves;
        private final Map<PsiElement, DlvSmartStepIntoHandler.InterfaceMethodCallVariant> myInterfaceMethods;
        private boolean myUnhandledCalls;

        private CallProcessor(@NotNull DlvDebugProcess process, @NotNull Document document, long goroutineId, int minOffset, int line) {

            super();
            this.myVariants = new ArrayList<>();
            this.myVisitedCalls = new HashSet<>();
            this.myVisitedResolves = new HashSet<>();
            this.myInterfaceMethods = new HashMap<>();
            this.myProcess = process;
            this.myDocument = document;
            this.myGoroutineId = goroutineId;
            this.myMinOffset = minOffset;
            this.myMaxLine = Ref.create(line + 1);
        }

        public boolean process(PsiElement element) {
            GoCallExpr call = PsiTreeUtil.getParentOfType(element, GoCallExpr.class, false);
            GoReferenceExpression expression = ObjectUtils.tryCast(call != null ? call.getExpression() : null, GoReferenceExpression.class);
            if (expression != null && expression.getTextRange().getStartOffset() >= this.myMinOffset && this.myVisitedCalls.add(call)) {
                this.updateMaxLine(call);
                ContainerUtil.addIfNotNull(this.myVariants, this.getVariant(expression));
                this.process(call.getParent());

                for (GoCallExpr childCall : PsiTreeUtil.findChildrenOfType(call, GoCallExpr.class)) {
                    this.process(childCall);
                }
            }

            return true;
        }

        private void updateMaxLine(@NotNull PsiElement element) {


            int expressionEndLine = this.myDocument.getLineNumber(element.getTextRange().getEndOffset()) + 1;
            if (expressionEndLine > this.myMaxLine.get()) {
                this.myMaxLine.set(expressionEndLine);
            }

        }

        private @Nullable DlvSmartStepIntoHandler.Variant getVariant(@NotNull GoReferenceExpression expr) {


            PsiElement resolve = expr.resolve();
            if (resolve instanceof GoFunctionDeclaration) {
                return this.myVisitedResolves.add(resolve) ? this.getFunctionVariant(expr, (GoFunctionDeclaration)resolve) : null;
            } else if (resolve instanceof GoMethodDeclaration) {
                return this.myVisitedResolves.add(resolve) ? this.getMethodVariant(expr, (GoMethodDeclaration)resolve) : null;
            } else if (resolve instanceof GoMethodSpec) {
                return this.getInterfaceMethodVariant(expr, (GoMethodSpec)resolve);
            } else if (!(resolve instanceof GoParamDefinition) && !(resolve instanceof GoVarDefinition) && !(resolve instanceof GoFieldDefinition)) {
                this.myUnhandledCalls = true;
                return null;
            } else {
                return this.myVisitedResolves.add(resolve) ? this.getFunctionValueVariant(expr, (GoTypeOwner)resolve) : null;
            }
        }

        private @Nullable DlvSmartStepIntoHandler.Variant getFunctionVariant(@NotNull GoReferenceExpression expr, @NotNull GoFunctionDeclaration resolved) {


            return this.createVariant(expr, resolved, (offset, function, packagePath, text) -> new FunctionCallVariant(expr, text, this.myProcess, this.myGoroutineId, this.myMaxLine, packagePath, function));
        }

        private @Nullable DlvSmartStepIntoHandler.Variant getMethodVariant(@NotNull GoReferenceExpression expr, @NotNull GoMethodDeclaration resolved) {


            GoType receiverType = resolved.getReceiverType();
            if (receiverType == null) {
                this.myUnhandledCalls = true;
                return null;
            } else {
                return this.createVariant(expr, resolved, (offset, function, packagePath, text) -> new MethodCallVariant(expr, text, this.myProcess, this.myGoroutineId, this.myMaxLine, packagePath, receiverType.getText(), function));
            }
        }

        private @Nullable DlvSmartStepIntoHandler.Variant getInterfaceMethodVariant(@NotNull GoReferenceExpression expr, @NotNull GoMethodSpec resolved) {


            GoTypeSpec interfaceType = PsiTreeUtil.getParentOfType(resolved, GoTypeSpec.class);
            String receiverType = interfaceType != null ? interfaceType.getName() : null;
            if (receiverType == null) {
                this.myUnhandledCalls = true;
                return null;
            } else {
                GoQualifier qualifier = expr.getQualifier();
                if (!PsiTreeUtil.findChildrenOfType(qualifier, GoCallExpr.class).isEmpty()) {
                    this.myUnhandledCalls = true;
                    return null;
                } else {
                    String interfaceValue = qualifier != null ? qualifier.getText() : null;
                    if (interfaceValue == null) {
                        this.myUnhandledCalls = true;
                        return null;
                    } else {
                        DlvSmartStepIntoHandler.InterfaceMethodCallVariant existing = this.myInterfaceMethods.get(resolved);
                        if (existing != null) {
                            existing.addInterfaceValue(interfaceValue);
                            return null;
                        } else {
                            DlvSmartStepIntoHandler.InterfaceMethodCallVariant variant = this.createVariant(expr, resolved, (offset, function, packagePath, text) -> new InterfaceMethodCallVariant(expr, text, this.myProcess, this.myGoroutineId, this.myMaxLine, function, interfaceValue));
                            if (variant != null) {
                                this.myInterfaceMethods.put(resolved, variant);
                            }

                            return variant;
                        }
                    }
                }
            }
        }

        private @Nullable DlvSmartStepIntoHandler.Variant getFunctionValueVariant(@NotNull GoReferenceExpression expr, @NotNull GoTypeOwner resolve) {


            GoType type = resolve.getGoUnderlyingType(null);
            if (type instanceof GoFunctionType && PsiTreeUtil.findChildrenOfType(expr, GoCallExpr.class).isEmpty()) {
                String text = getVariantText(expr, type);
                if (text != null) {
                    return new DlvSmartStepIntoHandler.FunctionValueCallVariant(expr, text, this.myProcess, this.myGoroutineId, this.myMaxLine, expr.getText());
                }
            }

            this.myUnhandledCalls = true;
            return null;
        }

        <T> @Nullable T createVariant(@NotNull GoReferenceExpression expr, @NotNull GoNamedElement resolved, @NotNull DlvSmartStepIntoHandler.CallProcessor.VariantFactory<T> factory) {


            String functionName = resolved.getName();
            if (functionName == null) {
                this.myUnhandledCalls = true;
                return null;
            } else {
                String packagePath = getPackagePath(resolved.getContainingFile());
                if (packagePath == null) {
                    this.myUnhandledCalls = true;
                    return null;
                } else {
                    String text = getVariantText(expr, resolved);
                    if (text == null) {
                        this.myUnhandledCalls = true;
                        return null;
                    } else {
                        return factory.create(expr.getTextRange().getStartOffset(), functionName, packagePath, text);
                    }
                }
            }
        }

        private static @Nullable String getVariantText(@NotNull GoReferenceExpression expr, @NotNull PsiElement signatureOwner) {


            GoSignature var10000;
            if (signatureOwner instanceof GoSignatureOwner owner) {
                var10000 = owner.getSignature();
            } else {
                var10000 = null;
            }

            GoSignature signature = var10000;
            GoParameters parameters = signature != null ? signature.getParameters() : null;
            if (parameters == null) {
                return null;
            } else {
                String name = expr.getText();
                String var8;
                if (signatureOwner instanceof GoMethodDeclaration) {
                    GoType receiverType = ((GoMethodDeclaration)signatureOwner).getReceiverType();
                    if (receiverType == null) {
                        return null;
                    }

                    var8 = receiverType.getText();
                    name = var8 + "." + ((GoMethodDeclaration)signatureOwner).getName();
                }

                if (signatureOwner instanceof GoMethodSpec) {
                    GoTypeSpec interfaceType = PsiTreeUtil.getParentOfType(signatureOwner, GoTypeSpec.class);
                    if (interfaceType == null) {
                        return null;
                    }

                    var8 = interfaceType.getName();
                    name = var8 + "." + ((GoMethodSpec)signatureOwner).getName();
                }

                return name + "(" + getParametersText(parameters) + ")";
            }
        }

        private static @NotNull String getParametersText(@NotNull GoParameters parameters) {


            return GoPsiPresentationBuilder.create().withoutResolve().withoutParameterNames().oneline().build(parameters);
        }

        private static @Nullable String getPackagePath(@NotNull GoFile file) {


            String packageName = file.getPackageName();
            if ("main".equals(packageName)) {
                return packageName;
            } else {
                String importPath = file.getImportPath(GoVendoringUtil.isVendoringEnabled(file));
                return importPath != null ? importPath : file.getPackageName();
            }
        }

        @NotNull List<DlvSmartStepIntoHandler.Variant> getVariants() {

            return this.myUnhandledCalls && this.myVariants.size() == 1 ? Collections.emptyList() : this.myVariants;
        }

        private interface VariantFactory<T> {
            @NotNull T create(int var1, @NotNull String var2, @NotNull String var3, @NotNull String var4);
        }
    }

    public abstract static class Variant extends XSmartStepIntoVariant implements Comparable<DlvSmartStepIntoHandler.Variant> {
        private final PsiElement myHighlightElement;
        private final int myStartOffset;
        private final String myText;
        private final Ref<Integer> myMaxLineNum;
        final DlvDebugProcess myProcess;
        final long myGoroutineId;
        boolean myShouldFail;

        protected Variant(@NotNull GoReferenceExpression expr, @NotNull String text, @NotNull DlvDebugProcess process, long goroutineId, @NotNull Ref<Integer> maxLineNum) {


            super();
            this.myHighlightElement = expr.getParent();
            this.myStartOffset = expr.getTextRange().getStartOffset();
            this.myText = text;
            this.myProcess = process;
            this.myGoroutineId = goroutineId;
            this.myMaxLineNum = maxLineNum;
        }

        public @Nullable TextRange getHighlightRange() {
            return this.myHighlightElement != null ? this.myHighlightElement.getTextRange() : null;
        }

        public String getText() {
            return this.myText;
        }

        void stepInto() {

            this.stepInto(this.myMaxLineNum.get());
        }

        abstract void stepInto(int var1);

        public int compareTo(@NotNull DlvSmartStepIntoHandler.Variant o) {

            return Integer.compare(this.myStartOffset, o.myStartOffset);
        }

    }

    private static final class FunctionValueCallVariant extends DlvSmartStepIntoHandler.Variant {
        private final String myFunctionValue;

        private FunctionValueCallVariant(@NotNull GoReferenceExpression expr, @NotNull String text, @NotNull DlvDebugProcess process, long goroutineId, @NotNull Ref<Integer> maxLineNum, @NotNull String functionValue) {


            super(expr, text, process, goroutineId, maxLineNum);
            this.myFunctionValue = functionValue;
        }

        void stepInto(int maxLine) {
            String functionValue = this.myShouldFail ? "" : this.myFunctionValue;
            this.myProcess.getSmartStepInto().smartStepIntoFunctionValue(this.myGoroutineId, maxLine, functionValue);
        }
    }

    private static final class InterfaceMethodCallVariant extends DlvSmartStepIntoHandler.Variant {
        private final String myFunctionName;
        private final Set<String> myInterfaceValues;

        private InterfaceMethodCallVariant(@NotNull GoReferenceExpression expr, @NotNull String text, @NotNull DlvDebugProcess process, long goroutineId, @NotNull Ref<Integer> maxLineNum, @NotNull String functionName, @NotNull String interfaceValue) {
            super(expr, text, process, goroutineId, maxLineNum);
            this.myInterfaceValues = new HashSet<>();
            this.myFunctionName = functionName;
            this.myInterfaceValues.add(interfaceValue);
        }

        void stepInto(int maxLine) {
            String functionName = this.myShouldFail ? "" : this.myFunctionName;
            this.myProcess.getSmartStepInto().smartStepIntoInterfaceMethod(this.myGoroutineId, maxLine, this.myInterfaceValues, functionName);


        }

        void addInterfaceValue(@NotNull String interfaceValue) {


            this.myInterfaceValues.add(interfaceValue);
        }
    }

    private static final class MethodCallVariant extends DlvSmartStepIntoHandler.Variant {
        private final String myPackagePath;
        private final String myReceiverType;
        private final String myFunctionName;

        private MethodCallVariant(@NotNull GoReferenceExpression expr, @NotNull String text, @NotNull DlvDebugProcess process, long goroutineId, @NotNull Ref<Integer> maxLineNum, @NotNull String packagePath, @NotNull String receiverType, @NotNull String functionName) {


            super(expr, text, process, goroutineId, maxLineNum);
            this.myPackagePath = packagePath;
            this.myReceiverType = receiverType;
            this.myFunctionName = functionName;
        }

        void stepInto(int maxLine) {
            String functionName = this.myShouldFail ? "" : this.myFunctionName;
            this.myProcess.getSmartStepInto().smartStepIntoMethod(this.myGoroutineId, maxLine, this.myPackagePath, this.myReceiverType, functionName);


        }
    }

    private static final class FunctionCallVariant extends DlvSmartStepIntoHandler.Variant {
        private final String myPackagePath;
        private final String myFunctionName;

        private FunctionCallVariant(@NotNull GoReferenceExpression expr, @NotNull String text, @NotNull DlvDebugProcess process, long goroutineId, @NotNull Ref<Integer> maxLineNum, @NotNull String packagePath, @NotNull String functionName) {
         

            super(expr, text, process, goroutineId, maxLineNum);
            this.myPackagePath = packagePath;
            this.myFunctionName = functionName;
        }

        void stepInto(int maxLine) {
            String functionName = this.myShouldFail ? "" : this.myFunctionName;
            this.myProcess.getSmartStepInto().smartStepIntoFunction(this.myGoroutineId, maxLine, this.myPackagePath, functionName);


        }
    }
}
