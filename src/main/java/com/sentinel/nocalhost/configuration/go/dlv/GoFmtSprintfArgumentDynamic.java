package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.psi.GoExpression;
import com.goide.psi.GoFile;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import com.goide.psi.impl.GoElementFactory;
import com.goide.util.Value;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.util.containers.ContainerUtil;
import java.math.BigInteger;
import java.util.Iterator;
import java.util.List;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;

class GoFmtSprintfArgumentDynamic implements GoFmtSprintfArgument {
    private final Project myProject;
    private final String myReceiverName;
    private final String myExpression;
    private final boolean myDereference;
    private final boolean myTakeAddress;

    GoFmtSprintfArgumentDynamic(@NotNull Project project, @NotNull String receiverName, @NotNull String expression, boolean dereference, boolean takeAddress) {


        super();
        this.myProject = project;
        this.myReceiverName = receiverName;
        this.myExpression = expression;
        this.myDereference = dereference;
        this.myTakeAddress = takeAddress;
    }

    public @Nullable BigInteger getValueAddress(@NotNull DlvApi.@NotNull Variable receiver) {

        DlvApi.Variable field = this.getVariable(receiver, this.myExpression);
        return field == null || !field.isPtr() && !this.myTakeAddress ? null : field.addr;
    }

    public @NotNull Promise<@Nullable Value> getValue(@NotNull DlvXValue dlvXValue, DlvApi.@NotNull Variable receiver) {

        DlvApi.Variable var = this.getVariable(receiver, this.myExpression);
        Promise<Value> var10000;
        if (var == null) {
            var10000 = Promises.resolvedPromise(null);


            return var10000;
        } else {
            if (this.myDereference && var.isPtr() && var.children.length == 1) {
                var = var.children[0];
            }

            if (!var.isSlice() && !var.isArray()) {
                DlvApi.Variable finalVar = var;
                var10000 = Promises.resolvedPromise(ReadAction.compute(() -> {
                    try {
                        GoExpression expression = finalVar.isString() ? GoElementFactory.createStringLiteral(this.myProject, "`" + finalVar.value + "`") : this.tryCreateExpression(finalVar.value);
                        return expression != null ? (expression).getValue() : null;
                    } catch (Throwable var3) {
                        return null;
                    }
                }));


                return var10000;
            } else if (var.type.endsWith("]uint8")) {
                DlvRequest.DlvLoadConfig config = (new DlvRequest.DlvLoadConfig(JetDevSettings.getInstance())).setMaxVariableRecurse(0);
                String expr = DlvXValue.evaluationExpression(var);
                var10000 = dlvXValue.getProcess().send(new DlvRequest.Eval(expr, dlvXValue.myFrameId, dlvXValue.myGoroutineId, config)).thenAsync((it) -> {
                    byte[] bytes = DlvUtil.readBytes(it);
                    return Promises.resolvedPromise(bytes != null ? Value.of(new Value.ByteArray(bytes)) : null);
                });


                return var10000;
            } else {
                var10000 = Promises.resolvedPromise(null);

                return var10000;
            }
        }
    }

    private @Nullable GoExpression tryCreateExpression(@NotNull String text) {

        GoFile file = GoElementFactory.createFileFromText(this.myProject, "package a; func a() {\n " + text + "}");
        return (GoExpression)PsiTreeUtil.findChildOfType(file, GoExpression.class);
    }

    @Nullable
    private DlvApi.@Nullable Variable getVariable(@NotNull DlvApi.@NotNull Variable receiver, @NotNull String expression) {


        List<String> accessors = StringUtil.split(expression, ".");
        if (accessors.size() < 2) {
            return expression.equals(this.myReceiverName) ? receiver : null;
        } else {
            String first = ContainerUtil.getFirstItem(accessors);
            if (!this.myReceiverName.equals(first)) {
                return null;
            } else {

                return getVariable(receiver, accessors);
            }
        }
    }

    private static DlvApi.Variable getVariable(DlvApi.@NotNull Variable receiver, List<String> accessors) {
        DlvApi.Variable result = receiver;
        List<String> rest = ContainerUtil.subList(accessors, 1);

        String fieldName;
        for(Iterator<String> var7 = rest.iterator(); var7.hasNext(); result = result != null ? DlvUtil.getByName(result.children, fieldName) : null) {
            fieldName = var7.next();
            if (result != null && result.isPtr() && result.children.length == 1) {
                result = result.children[0];
            }
        }
        return result;
    }

    int getAccessorsDepth() {
        List<String> accessors = StringUtil.split(this.myExpression, ".");
        return accessors.size() > 1 ? accessors.size() - 1 : 0;
    }
}
