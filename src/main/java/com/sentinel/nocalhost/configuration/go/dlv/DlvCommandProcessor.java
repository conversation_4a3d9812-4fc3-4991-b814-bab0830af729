package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.dlv.JsonReaderEx;
import com.goide.i18n.GoBundle;
import com.google.gson.GsonBuilder;
import com.google.gson.stream.JsonReader;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.util.SmartList;
import com.intellij.util.containers.ContainerUtil;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvResponse;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.Promises;
import org.jetbrains.rpc.CommandProcessor;
import org.jetbrains.rpc.CommandProcessorKt;
import org.jetbrains.rpc.RequestCallback;

public abstract class DlvCommandProcessor extends CommandProcessor<JsonReaderEx, DlvResponse, DlvResponse> {

    public DlvCommandProcessor() {
    }

    public @Nullable DlvResponse readIfHasSequence(@NotNull JsonReaderEx message) {

        return new DlvResponse.CommandResponseImpl(message, null);
    }

    public int getSequence(@NotNull DlvResponse response) {

        return response.id();
    }

    public void acceptNonSequence(JsonReaderEx message) {
    }

    public void processIncomingJson(@NotNull JsonReaderEx reader) {

        this.getMessageManager().processIncoming(reader);
    }

    public void call(@NotNull DlvResponse response, @NotNull RequestCallback<DlvResponse> callback) {

        if (response.result() != null) {
            callback.onSuccess(response, this);
        } else {
            callback.onError(Promises.createError(createMessage(response)));
        }
    }

    private static @NotNull String createMessage(@NotNull DlvResponse r) {

        DlvResponse.ErrorInfo e = r.error();
        String var10000;
        if (e == null) {
            var10000 = GoBundle.message("go.debugger.internal.messaging.error");
        } else {
            List<String> data = e.data();
            String message = e.message();
            if (ContainerUtil.isEmpty(data)) {
                var10000 = StringUtil.defaultIfEmpty(message, "<null>");
            } else {
                List<String> list = new SmartList<>(message);
                list.addAll(data);
                var10000 = list.toString();


            }
        }
        return var10000;
    }

    public <RESULT> @NotNull RESULT readResult(@NotNull String method, @NotNull DlvResponse successResponse) {

        JsonReaderEx result = successResponse.result();
        assert result != null : "success result should be not null";
        JsonReader reader = result.asGson();
        Object o = (new GsonBuilder()).create().fromJson(reader, getResultType(method.replaceFirst("RPCServer\\.", "")));

        return (RESULT) o;
    }

    private static @NotNull Type getResultType(@NotNull String method) {

        Class<?>[] var1 = DlvRequest.class.getDeclaredClasses();
        for (Class<?> c : var1) {
            if (method.equals(c.getSimpleName())) {
                Type s = c.getGenericSuperclass();
                assert s instanceof ParameterizedType : c.getCanonicalName() + " should have a generic parameter for correct callback processing";
                Type[] arguments = ((ParameterizedType) s).getActualTypeArguments();
                assert arguments.length == 1 : c.getCanonicalName() + " should have only one generic argument for correct callback processing";
                return arguments[0];
            }
        }
        CommandProcessorKt.getLOG().error("Unknown response " + method + ", please register an appropriate request into DlvRequest");
        return Object.class;
    }
}
