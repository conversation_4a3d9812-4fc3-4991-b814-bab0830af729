package com.sentinel.nocalhost.configuration.go.dlv;


import com.goide.i18n.GoBundle;
import com.intellij.xdebugger.frame.presentation.XRegularValuePresentation;
import com.intellij.xdebugger.frame.presentation.XValuePresentation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

class DvlUnreadableValuePresentation extends XRegularValuePresentation {
    private final String myUnreadableReason;

    DvlUnreadableValuePresentation(@Nullable String type, @NotNull String unreadableReason) {
        super("", type);
        this.myUnreadableReason = unreadableReason;
    }

    public void renderValue(@NotNull XValuePresentation.@NotNull XValueTextRenderer renderer) {

        super.renderValue(renderer);
        renderer.renderError(GoBundle.message("go.debugger.unreadable.error", new Object[]{this.myUnreadableReason}));
    }
}