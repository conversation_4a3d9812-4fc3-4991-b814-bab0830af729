package com.sentinel.nocalhost.configuration.go.dlv;

import com.intellij.execution.configurations.ModuleBasedConfiguration;
import com.intellij.execution.configurations.RunProfile;
import com.intellij.openapi.module.Module;
import com.intellij.xdebugger.XDebugSession;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: DlvUtil.class */
public final class DlvUtil {


    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public static Module getModule(@NotNull XDebugSession session) {

        RunProfile runProfile = session.getRunProfile();
        if (runProfile instanceof ModuleBasedConfiguration moduleBasedConfiguration) {
            return moduleBasedConfiguration.getConfigurationModule().getModule();
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public static byte[] readBytes(@NotNull DlvApi.Variable var) {

        try {
            byte[] result = new byte[var.children.length];
            for (int i = 0; i < var.children.length; i++) {
                DlvApi.Variable child = var.children[i];
                result[i] = (byte) Integer.parseInt(child.value);
            }
            return result;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public static DlvApi.Variable getByName(DlvApi.Variable[] vars, @NotNull @NonNls String name) {

        for (DlvApi.Variable var : vars) {
            if (name.equals(var.name)) {
                return var;
            }
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public static String currentFunctionName(@NotNull DlvApi.DebuggerState s) {

        DlvApi.Location loc = getCurrentLocation(s);
        if (loc != null) {
            return loc.name();
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public static DlvApi.Location getCurrentLocation(@NotNull DlvApi.DebuggerState s) {

        DlvApi.Goroutine goroutine = s.currentGoroutine;
        if (goroutine != null) {
            return goroutine.currentLoc;
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public static boolean isAutogenerated(@Nullable DlvApi.Location loc) {
        return loc != null && "<autogenerated>".equals(loc.file) && loc.line == 1;
    }
}
