package com.sentinel.nocalhost.configuration.go.dlv;


import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.AsyncPromise;
import org.jetbrains.debugger.Vm;
import org.jetbrains.debugger.connection.RemoteVmConnection;
import org.jetbrains.ide.BuiltInServerManager;

import java.net.InetSocketAddress;

public class DlvRemoteVmConnection extends RemoteVmConnection<DlvVm> {
    private final DlvDisconnectOption myDisconnectOption;
    private final Bootstrap myBootstrap;

    public DlvRemoteVmConnection(@NotNull DlvDisconnectOption disconnectOption) {

        this(disconnectOption, null);
    }

    public DlvRemoteVmConnection(@NotNull DlvDisconnectOption disconnectOption, @Nullable Bootstrap bootstrap) {


        super();
        this.myDisconnectOption = disconnectOption;
        this.myBootstrap = bootstrap;
    }

    public @NotNull Bootstrap createBootstrap(@NotNull InetSocketAddress address, final @NotNull AsyncPromise<DlvVm> vmResult) {

        Bootstrap bootstrap = this.myBootstrap != null ? this.myBootstrap : BuiltInServerManager.getInstance().createClientBootstrap();

        return bootstrap.handler(new ChannelInitializer<>() {
            protected void initChannel(@NotNull Channel channel) {

                channel.pipeline().addLast(new ChannelInboundHandlerAdapter() {
                    public void channelActive(@NotNull ChannelHandlerContext ctx) {


                        vmResult.setResult(new DlvVm(DlvRemoteVmConnection.this.getDebugEventListener(), ctx.channel(), DlvRemoteVmConnection.this.myDisconnectOption));
                    }

                    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
                        vmResult.setError(cause);
                        ctx.close();
                    }
                });
            }
        });
    }

    protected @NotNull String connectedAddressToPresentation(@NotNull InetSocketAddress address, @NotNull Vm vm) {


        return address.toString();
    }
}
