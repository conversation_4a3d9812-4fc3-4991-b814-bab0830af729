package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.dlv.DlvStackFuncName;
import com.goide.execution.GoBuildingRunConfiguration;
import com.goide.execution.testing.GoTestRunConfiguration;
import com.goide.psi.GoFile;
import com.goide.psi.GoInterfaceType;
import com.goide.psi.GoPsiTreeUtil;
import com.goide.psi.GoStructType;
import com.goide.psi.GoType;
import com.goide.psi.GoTypeReferenceExpression;
import com.goide.psi.impl.GoElementFactory;
import com.goide.psi.impl.GoPackage;
import com.goide.psi.presentation.GoPsiPresentationBuilder;
import com.goide.sdk.GoPackageUtil;
import com.goide.util.GoUtil;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.module.Module;
import com.intellij.openapi.project.DumbService;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.TextRange;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.goide.dlv.DlvPackagePath;
import com.intellij.psi.PsiManager;
import com.intellij.psi.ResolveState;
import com.intellij.util.IncorrectOperationException;
import com.intellij.util.ObjectUtils;
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread;
import com.intellij.util.containers.ContainerUtil;
import com.intellij.xdebugger.XDebugSession;
import java.nio.file.Path;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: DlvSymbolPrettier.class */
public class DlvSymbolPrettier {
    private final Project myProject;
    private final Module myModule;
    private final XDebugSession mySession;
    private final ConcurrentMap<String, String> myPrettyTypesCache;
    private final ConcurrentMap<DlvPackagePath, Set<String>> myPackageNames;
    private final ConcurrentMap<String, DlvStackFuncName> myFunctionsCache;



    /* JADX INFO: Access modifiers changed from: package-private */
    public DlvSymbolPrettier(@NotNull XDebugSession session) {

        this.myPrettyTypesCache = new ConcurrentHashMap<>();
        this.myPackageNames = new ConcurrentHashMap<>();
        this.myFunctionsCache = new ConcurrentHashMap<>();
        this.myProject = session.getProject();
        this.myModule = DlvUtil.getModule(session);
        this.mySession = session;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @RequiresBackgroundThread
    @NotNull
    public DlvStackFuncName prettifyFunc(@NotNull String dlvFunc) {

        ApplicationManager.getApplication().assertIsNonDispatchThread();

        return this.myFunctionsCache.computeIfAbsent(dlvFunc, __ -> doPrettifyFunc(dlvFunc));
    }

    @NotNull
    private DlvStackFuncName doPrettifyFunc(@NotNull String dlvFuncString) {

        DlvFunc dlvFunc = DlvFunc.parse(dlvFuncString);
        if (dlvFunc == null) {
            return new DlvStackFuncName(dlvFuncString, "");
        }
        Set<String> pkgNames = getPackageNames(dlvFunc.getDlvPackagePath());
        String pkgName =  ContainerUtil.getOnlyItem(pkgNames);
        if (pkgName != null) {
            return new DlvStackFuncName(pkgName + "." + dlvFunc.getName(), dlvFunc.getDlvPackagePath().getPackagePath());
        }
        return new DlvStackFuncName(dlvFuncString, "");
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @RequiresBackgroundThread
    @NotNull
    public String prettifyType(@NotNull String dlvType) {

        ApplicationManager.getApplication().assertIsNonDispatchThread();

        return this.myPrettyTypesCache.computeIfAbsent(dlvType, __ -> doPrettifyType(dlvType));
    }

    @NotNull
    private String doPrettifyType(@NotNull String dlvType) {

        String typeForEditor = prepareTypeForEditor(dlvType);
        if (typeForEditor == null || typeForEditor.length() != dlvType.length()) {

            return dlvType;
        }
        GoType type = GoElementFactory.createType(this.myProject, typeForEditor, null);
        if (type == null || !typeForEditor.equals(type.getText())) {

            return dlvType;
        }
        if (!(type instanceof GoStructType) && !(type instanceof GoInterfaceType)) {
            List<GoTypeReferenceExpression> references = getTypeReferences(type);
            int typeStart = type.getTextRange().getStartOffset();
            for (GoTypeReferenceExpression ref : references) {
                if (!replaceWithShortReference(dlvType, ref, typeStart)) {

                    return dlvType;
                }
            }
        }

        return GoPsiPresentationBuilder.create().withoutResolve().withoutStructureAndInterfaceContent().withoutParameterNames().oneline().build(type);
    }

    private boolean replaceWithShortReference(@NotNull String dlvType, @NotNull GoTypeReferenceExpression typeRef, int typeStart) {

        TextRange range = typeRef.getTextRange().shiftLeft(typeStart);
        if (!TextRange.from(0, dlvType.length()).contains(range)) {
            return false;
        }
        String origDlvType = range.substring(dlvType);
        if (origDlvType.equals(typeRef.getText())) {
            return true;
        }
        DlvNamedType dlvNamedType = DlvNamedType.parse(origDlvType);
        if (dlvNamedType == null) {
            return false;
        }
        Set<String> pkgNames = getPackageNames(dlvNamedType.getDlvPackagePath());
        String pkgName =  ContainerUtil.getOnlyItem(pkgNames);
        if (pkgName == null) {
            return false;
        }
        String text = "package p; var a = " + pkgName + "." + dlvNamedType.getTypeName() + "{}";
        GoTypeReferenceExpression updatedRef = GoElementFactory.createElement(this.myProject, text, GoTypeReferenceExpression.class);
        if (updatedRef == null) {
            return false;
        }
        try {
            typeRef.replace(updatedRef);
            return true;
        } catch (IncorrectOperationException e) {
            return false;
        }
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @RequiresBackgroundThread
    @NotNull
    public Set<String> getPackageNames(@NotNull DlvPackagePath dlvPackagePath) {

        ApplicationManager.getApplication().assertIsNonDispatchThread();
        if (DumbService.isDumb(this.myProject)) {

            return Set.of();
        }

        return this.myPackageNames.computeIfAbsent(dlvPackagePath, __ -> computePackageNames(dlvPackagePath));
    }

    @NotNull
    private Set<String> computePackageNames(@NotNull DlvPackagePath dlvPackagePath) {
        GoTestRunConfiguration runConfiguration;

        String packagePath = dlvPackagePath.getPackagePath();
        if (StringUtil.isEmpty(packagePath)) {

            return Set.of();
        }
        if ("main".equals(packagePath) && !dlvPackagePath.isTestPackage()) {

            return Set.of("main");
        }
        if ("command-line-arguments".equals(packagePath) && (runConfiguration =  ObjectUtils.tryCast(this.mySession.getRunProfile(), GoTestRunConfiguration.class)) != null && runConfiguration.getKind() == GoBuildingRunConfiguration.Kind.FILE) {
            List<String> paths = runConfiguration.getFilePaths();
            PsiManager psiManager = PsiManager.getInstance(this.myProject);
            Set<String> result = new TreeSet<>();
            for (String path : paths) {
                VirtualFile vfile = VirtualFileManager.getInstance().findFileByNioPath(Path.of(path));
                GoFile file =  ObjectUtils.tryCast(vfile != null ? psiManager.findFile(vfile) : null, GoFile.class);
                String packageName = file != null ? file.getPackageName() : null;
                if (packageName != null) {
                    result.add(packageName);
                }
            }
            return result;
        }
        Collection<GoPackage> packages = GoPackageUtil.findByImportPath(packagePath, this.myProject, this.myModule, ResolveState.initial());
        List<String> packageNames = ContainerUtil.map(packages, p -> p.getName() + (dlvPackagePath.isTestPackage() ? "_test" : ""));
        return new TreeSet<>(packageNames);
    }

    @NotNull
    private static List<GoTypeReferenceExpression> getTypeReferences(@NotNull GoType type) {

        return ContainerUtil.sorted(GoPsiTreeUtil.goTraverser().withRoot(type).filter(GoTypeReferenceExpression.class).toList(), (o1, o2) -> Integer.compare(o2.getTextRange().getStartOffset(), o1.getTextRange().getStartOffset()));
    }

    @Nullable
    private static String prepareTypeForEditor(@NotNull String dlvType) {

        StringBuilder result = new StringBuilder();
        int i = 0;
        while (i < dlvType.length()) {
            char c = dlvType.charAt(i);
            if (GoUtil.isValidGoIdentifierCharacter(c)) {
                result.append(c);
            } else if ("*{}[]() ,;".indexOf(c) >= 0) {
                result.append(c);
            } else if (c == '<') {
                boolean startOfArrow = i < dlvType.length() - 1 && dlvType.charAt(i + 1) == '-';
                if (startOfArrow) {
                    result.append(c);
                    result.append(dlvType.charAt(i + 1));
                    i++;
                } else {
                    return null;
                }
            } else {
                result.append('_');
            }
            i++;
        }
        return result.toString();
    }
}
