package com.sentinel.nocalhost.configuration.go.dlv.protocol;

import com.google.gson.stream.JsonWriter;
import com.sentinel.nocalhost.configuration.go.dlv.JetDevSettings;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.jsonProtocol.OutMessage;
import org.jetbrains.jsonProtocol.Request;

import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

/* loaded from: DlvRequest.class */
public abstract class DlvRequest<T> extends OutMessage implements Request<T> {
    private static final String PARAMS = "params";
    private static final String ID = "id";
    private boolean argumentsObjectStarted;

    /* loaded from: DlvRequest$ListBreakpoints.class */
    public static final class ListBreakpoints extends DlvObjRequest<List<DlvApi.Breakpoint>> {
    }

    @Retention(RetentionPolicy.RUNTIME)
    /* loaded from: DlvRequest$Name.class */
    public @interface Name {
        String value();
    }

    /* loaded from: DlvRequest$Recorded.class */
    public static final class Recorded extends DlvObjRequest<DlvApi.Recorded> {
    }

    /* loaded from: DlvRequest$StopRecording.class */
    public static final class StopRecording extends DlvObjRequest<Void> {
    }

    private DlvRequest() {
        try {
            getWriter().name("method").value(getMethodName());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    public String getMethodName() {
        Name annotation =  getClass().getAnnotation(Name.class);

        return "RPCServer." + (annotation != null ? annotation.value() : getClass().getSimpleName());
    }

    public final void beginArguments() {
        if (!this.argumentsObjectStarted) {
            this.argumentsObjectStarted = true;
            if (needObject()) {
                try {
                    getWriter().name(PARAMS);
                    getWriter().beginArray();
                    getWriter().beginObject();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    protected boolean needObject() {
        return true;
    }

    public final void finalize(int id) {
        try {
            if (this.argumentsObjectStarted && needObject()) {
                getWriter().endObject();
                getWriter().endArray();
            }
            getWriter().name(ID).value(id);
            getWriter().endObject();
            getWriter().close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /* loaded from: DlvRequest$DlvObjRequest.class */
    public static class DlvObjRequest<T> extends DlvRequest<T> {

        /* loaded from: DlvRequest$DlvObjRequest$BodyMaker.class */
        private interface BodyMaker {
            void make(JsonWriter jsonWriter) throws IOException;
        }

        public DlvObjRequest() {
            this(null);
        }

        public DlvObjRequest(@Nullable BodyMaker consumer) {
            try {
                JsonWriter writer = getWriter();
                writer.name(DlvRequest.PARAMS).beginArray().beginObject();
                if (consumer != null) {
                    consumer.make(writer);
                }
                writer.endObject().endArray();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        @Override // com.goide.dlv.protocol.DlvRequest
        protected boolean needObject() {
            return false;
        }
    }

    /* loaded from: DlvRequest$CreateBreakpoint.class */
    public static final class CreateBreakpoint extends DlvObjRequest<DlvApi.Breakpoint> {

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public CreateBreakpoint(@NotNull String path, int line, @Nullable String condition) {
            super(w -> {
                w.name("Breakpoint").beginObject().name("file").value(path).name("line").value(line).name("Cond").value(condition).endObject();
            });

        }
    }

    /* loaded from: DlvRequest$ClearBreakpoint.class */
    public static final class ClearBreakpoint extends DlvObjRequest<DlvApi.Breakpoint> {
        public ClearBreakpoint(int id) {
            super(w -> {
                w.name(DlvRequest.ID).value(id);
            });
        }
    }

    /* loaded from: DlvRequest$FindLocation.class */
    public static final class FindLocation extends DlvObjRequest<List<DlvApi.Location>> {
        public FindLocation(@NonNls String location) {
            super(w -> {
                w.name("Loc").value(location).name("Scope").beginObject().name("GoroutineID").value(-1L).name("Frame").value(0L).endObject();
            });
        }
    }

    /* loaded from: DlvRequest$Stacktrace.class */
    public static final class Stacktrace extends DlvObjRequest<List<DlvApi.Location>> {
        public Stacktrace(long goroutineId) {
            super(w -> {
                w.name("Id").value(goroutineId).name("Depth").value(JetDevSettings.getInstance().stacktraceDepth);
            });
        }
    }

    /* loaded from: DlvRequest$Locals.class */
    private static abstract class Locals<T> extends DlvObjRequest<T> {
        Locals(long goroutineId, int frameId) {
            super(w -> {
                DlvRequest.writeScope(frameId, goroutineId, w);
            });
        }
    }

    /* loaded from: DlvRequest$ListLocalVars.class */
    public static final class ListLocalVars extends Locals<List<DlvApi.Variable>> {
        public ListLocalVars(long goroutineId, int frameId) {
            super(goroutineId, frameId);
        }
    }

    /* loaded from: DlvRequest$ListFunctionArgs.class */
    public static final class ListFunctionArgs extends Locals<List<DlvApi.Variable>> {
        public ListFunctionArgs(long goroutineId, int frameId) {
            super(goroutineId, frameId);
        }
    }

    /* loaded from: DlvRequest$Command.class */
    public static final class Command extends DlvObjRequest<DlvApi.DebuggerState> {

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public Command(@NotNull String command) {
            super(w -> {
                w.name("Name").value(command);
            });

        }
    }

    /* loaded from: DlvRequest$CancelNext.class */
    public static final class CancelNext extends DlvObjRequest<DlvApi.Breakpoint> {
        public CancelNext() {
            super(w -> {
            });
        }
    }

    /* loaded from: DlvRequest$State.class */
    public static final class State extends DlvObjRequest<DlvApi.DebuggerState> {
        public State() {
            this(false);
        }

        public State(boolean nonBlocking) {
            super(w -> {
                w.name("NonBlocking").value(nonBlocking);
            });
        }
    }

    /* loaded from: DlvRequest$ListSources.class */
    public static final class ListSources extends DlvObjRequest<List<String>> {
        public ListSources() {
            super(w -> {
            });
        }
    }

    @Name("Command")
    /* loaded from: DlvRequest$SwitchThreadCommand.class */
    public static final class SwitchThreadCommand extends DlvObjRequest<DlvApi.DebuggerState> {
        public SwitchThreadCommand(int threadId) {
            super(w -> {
                w.name("Name").value(DlvApi.SWITCH_THREAD).name("ThreadID").value(threadId);
            });
        }
    }

    @Name("Command")
    /* loaded from: DlvRequest$SwitchGoroutineCommand.class */
    public static final class SwitchGoroutineCommand extends DlvObjRequest<DlvApi.DebuggerState> {
        public SwitchGoroutineCommand(long goroutineId) {
            super(w -> {
                w.name("Name").value(DlvApi.SWITCH_GOROUTINE).name("GoroutineID").value(goroutineId);
            });
        }
    }

    /* loaded from: DlvRequest$Detach.class */
    public static final class Detach extends DlvObjRequest<Object> {
        public Detach(boolean kill) {
            super(w -> {
                w.name("Kill").value(kill);
            });
        }
    }

    /* loaded from: DlvRequest$Eval.class */
    public static final class Eval extends DlvObjRequest<DlvApi.Variable> {


        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public Eval(@NotNull String symbol, int frameId, long goroutineId) {
            super(writer -> {
                DlvRequest.writeScope(frameId, goroutineId, writer).name("Expr").value(symbol);
            });

        }

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public Eval(@NotNull String symbol, int frameId, long goroutineId, long len) {
            super(writer -> {
                DlvRequest.writeScope(frameId, goroutineId, writer, len).name("Expr").value(symbol);
            });

        }

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public Eval(@NotNull String symbol, int frameId, long goroutineId, @NotNull DlvLoadConfig config) {
            super(writer -> {
                DlvRequest.writeScope(frameId, goroutineId, writer, config).name("Expr").value(symbol);
            });

        }
    }

    /* loaded from: DlvRequest$ListGoroutines.class */
    public static final class ListGoroutines extends DlvObjRequest<DlvApi.ListGoroutinesOut> {
        public ListGoroutines(int start, int count) {
            super(w -> {
                w.name("Start").value(start).name("Count").value(count);
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static JsonWriter writeScope(int frameId, long goroutineId, @NotNull JsonWriter writer) throws IOException {

        return writeScope(frameId, goroutineId, writer, new DlvLoadConfig(JetDevSettings.getInstance()));
    }

    @NotNull
    private static JsonWriter writeScope(int frameId, long goroutineId, @NotNull JsonWriter writer, long len) throws IOException {

        writer.name("Scope").beginObject().name("GoroutineID").value(goroutineId).name("Frame").value(frameId).endObject();

        return writeLoadConfig(writer, new DlvLoadConfig(JetDevSettings.getInstance()).setMaxStringLen((int) len).setMaxArrayValues((int) len));
    }

    @NotNull
    private static JsonWriter writeScope(int frameId, long goroutineId, @NotNull JsonWriter writer, @NotNull DlvLoadConfig config) throws IOException {

        writer.name("Scope").beginObject().name("GoroutineID").value(goroutineId).name("Frame").value(frameId).endObject();

        return writeLoadConfig(writer, config);
    }

    private static JsonWriter writeLoadConfig(@NotNull JsonWriter writer, @NotNull DlvLoadConfig config) throws IOException {
        return writer.name(config.getName()).beginObject().name("FollowPointers").value(config.isFollowPointer()).name("MaxStructFields").value(config.getMaxStructFields()).name("MaxVariableRecurse").value(config.getMaxVariableRecurse()).name("MaxStringLen").value(config.getMaxStringLen()).name("MaxArrayValues").value(config.getMaxArrayValues()).endObject();
    }

    /* loaded from: DlvRequest$Set.class */
    public static final class Set extends DlvObjRequest<Object> {


        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public Set(@NotNull String symbol, @NotNull String value, long goroutineId, int frameId) {
            super(w -> {
                DlvRequest.writeScope(frameId, goroutineId, w, value.length()).name("Symbol").value(symbol).name("Value").value(value);
            });

        }
    }

    @Name("Command")
    /* loaded from: DlvRequest$ContinueCall.class */
    public static final class ContinueCall extends DlvObjRequest<DlvApi.DebuggerState> {
        public ContinueCall() {
            super(w -> {
                w.name("Name").value(DlvApi.CONTINUE);
                DlvRequest.writeLoadConfig(w, new DlvLoadConfig(JetDevSettings.getInstance()).setName("ReturnInfoLoadConfig"));
            });
        }
    }

    @Name("Command")
    /* loaded from: DlvRequest$Call.class */
    public static final class Call extends DlvObjRequest<DlvApi.DebuggerState> {


        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        public Call(@NotNull String expr) {
            super(w -> {
                w.name("Name").value("call").name("expr").value(expr);
                JetDevSettings dlvSettings = JetDevSettings.getInstance();
                DlvRequest.writeLoadConfig(w, new DlvLoadConfig(dlvSettings).setName("ReturnInfoLoadConfig").setMaxStringLen(dlvSettings.maxStringLenInCall));
            });

        }
    }

    /* loaded from: DlvRequest$GetVersion.class */
    public static final class GetVersion extends DlvObjRequest<DlvApi.GetVersion> {
        public GetVersion() {
            super(w -> {
            });
        }
    }

    /* loaded from: DlvRequest$IsMulticlient.class */
    public static final class IsMulticlient extends DlvObjRequest<Boolean> {
        public IsMulticlient() {
            super(w -> {
            });
        }
    }

    /* loaded from: DlvRequest$AttachedToExistingProcess.class */
    public static final class AttachedToExistingProcess extends DlvObjRequest<Boolean> {
        public AttachedToExistingProcess() {
            super(w -> {
            });
        }
    }

    /* loaded from: DlvRequest$DlvLoadConfig.class */
    public static final class DlvLoadConfig {
        private String myName;
        private boolean myFollowPointer;
        private int myMaxStructFields;
        private int myMaxVariableRecurse;
        private int myMaxStringLen;
        private int myMaxArrayValues;


        public DlvLoadConfig(@NotNull JetDevSettings dlvSettings) {

            this.myName = "Cfg";
            this.myFollowPointer = true;
            this.myMaxVariableRecurse = 1;
            this.myMaxStructFields = dlvSettings.maxStructFields;
            this.myMaxStringLen = dlvSettings.maxStringLen;
            this.myMaxArrayValues = dlvSettings.maxArrayValues;
        }

        public String getName() {
            return this.myName;
        }

        @NotNull
        public DlvLoadConfig setName(String name) {
            this.myName = name;

            return this;
        }

        public boolean isFollowPointer() {
            return this.myFollowPointer;
        }

        @NotNull
        public DlvLoadConfig setFollowPointer(boolean followPointer) {
            this.myFollowPointer = followPointer;

            return this;
        }

        public int getMaxStructFields() {
            return this.myMaxStructFields;
        }

        @NotNull
        public DlvLoadConfig setMaxStructFields(int maxStructFields) {
            this.myMaxStructFields = maxStructFields;

            return this;
        }

        public int getMaxVariableRecurse() {
            return this.myMaxVariableRecurse;
        }

        @NotNull
        public DlvLoadConfig setMaxVariableRecurse(int maxVariableRecurse) {
            this.myMaxVariableRecurse = maxVariableRecurse;

            return this;
        }

        public long getMaxStringLen() {
            return this.myMaxStringLen;
        }

        @NotNull
        public DlvLoadConfig setMaxStringLen(int maxStringLen) {
            this.myMaxStringLen = maxStringLen;

            return this;
        }

        public long getMaxArrayValues() {
            return this.myMaxArrayValues;
        }

        @NotNull
        public DlvLoadConfig setMaxArrayValues(int maxArrayValues) {
            this.myMaxArrayValues = maxArrayValues;

            return this;
        }
    }
}
