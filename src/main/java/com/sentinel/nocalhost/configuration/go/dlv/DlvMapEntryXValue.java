package com.sentinel.nocalhost.configuration.go.dlv;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.util.Pair;
import com.intellij.xdebugger.frame.*;
import com.intellij.xdebugger.frame.presentation.XRegularValuePresentation;
import com.intellij.xdebugger.impl.ui.tree.nodes.XValuePresentationUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: DlvMapEntryXValue.class */
public class DlvMapEntryXValue extends XNamedValue {

    @NotNull
    private final DlvXValue myKey;

    @NotNull
    private final DlvXValue myValue;

    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
    public DlvMapEntryXValue(@NotNull String name, @NotNull DlvXValue key, @NotNull DlvXValue value) {
        super(name);

        this.myKey = key;
        this.myValue = value;
    }

    @Nullable
    public String getEvaluationExpression() {
        return "";
    }

    public void computePresentation(@NotNull XValueNode node, @NotNull XValuePlace place) {
        this.myKey.computePresentation(node).thenAsync(keyPresentation -> this.myValue.computePresentation(node).then(valuePresentation -> Pair.create(keyPresentation, valuePresentation))).onSuccess(keyAndValuePresentation -> {
            String keyText = XValuePresentationUtil.computeValueText(keyAndValuePresentation.first);
            String valueText = XValuePresentationUtil.computeValueText(keyAndValuePresentation.second);
            node.setPresentation(AllIcons.Debugger.Value, new XRegularValuePresentation(keyText + " -> " + valueText, null), true);
        });
    }

    public void computeChildren(@NotNull XCompositeNode node) {
        XValueChildrenList list = new XValueChildrenList(2);
        list.add("key", this.myKey);
        list.add("value", this.myValue);
        node.addChildren(list, true);
    }
}
