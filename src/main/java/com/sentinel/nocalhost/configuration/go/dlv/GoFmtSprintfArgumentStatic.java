package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.util.Value;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;

class GoFmtSprintfArgumentStatic implements GoFmtSprintfArgument {
    private final Value myValue;

    GoFmtSprintfArgumentStatic(@NotNull Value value) {

        super();
        this.myValue = value;
    }

    public @NotNull Promise<Value> getValue(@NotNull DlvXValue dlvXValue, DlvApi.@NotNull Variable receiver) {


        return Promises.resolvedPromise(this.myValue);
    }
}

