package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.dlv.DlvDataViewsSettingsUi;
import com.goide.dlv.DlvSettingsUi;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.PersistentStateComponent;
import com.intellij.openapi.components.SettingsCategory;
import com.intellij.openapi.components.State;
import com.intellij.openapi.components.Storage;
import com.intellij.openapi.options.Configurable;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.ProjectManager;
import com.intellij.util.xmlb.XmlSerializerUtil;
import com.intellij.xdebugger.XDebuggerManager;
import com.intellij.xdebugger.settings.DebuggerConfigurableProvider;
import com.intellij.xdebugger.settings.DebuggerSettingsCategory;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NotNull;
@State(
        name = "JetDevSettings",
        storages = {@Storage("dlv.xml")},
        category = SettingsCategory.TOOLS
)
public final class JetDevSettings implements PersistentStateComponent<JetDevSettings> {
    public static final int MAX_STRUCT_FIELDS = -1;
    public static final int MAX_STRING_LEN = 1024;
    public static final int MAX_STRING_LEN_IN_CALL = 4096;
    public static final int MAX_ARRAY_VALUES = 100;
    public boolean rebuildTransitivePackages = true;
    public int stacktraceDepth = 50;
    public @Nls String integerFormat;
    public int goroutinesLimit;
    public boolean showTypes;
    public boolean showPointerAddresses;
    public boolean useCustomDelveSettings;
    public int maxStructFields;
    public int maxStringLen;
    public int maxStringLenInCall;
    public int maxArrayValues;

    public JetDevSettings() {
        this.integerFormat = DlvValueRenderers.DECIMAL.getName();
        this.goroutinesLimit = 10000;
        this.showTypes = true;
        this.showPointerAddresses = true;
        this.useCustomDelveSettings = false;
        this.maxStructFields = -1;
        this.maxStringLen = 1024;
        this.maxStringLenInCall = 4096;
        this.maxArrayValues = 100;
    }

    public @NotNull JetDevSettings getState() {


        return this;
    }

    public static @NotNull JetDevSettings getInstance() {


        return ApplicationManager.getApplication().getService(JetDevSettings.class);
    }

    public void loadState(@NotNull JetDevSettings state) {


        XmlSerializerUtil.copyBean(state, this);
    }

    public @NotNull DlvValueRenderer getDefaultIntegerRenderer() {
        Iterator<DlvValueRendererSelectableInUi> var1 = DlvValueRenderers.getRenderersSelectableInUi().iterator();

        DlvValueRendererSelectableInUi renderer;
        do {
            if (!var1.hasNext()) {


                return DlvValueRenderers.DECIMAL;
            }

            renderer = (DlvValueRendererSelectableInUi)var1.next();
        } while(!renderer.getName().equals(this.integerFormat));

   

        return renderer;
    }

    public static void onDataViewsSettingsChanged() {
        Project[] var0 = ProjectManager.getInstance().getOpenProjects();

        for (Project project : var0) {
            for (DlvDebugProcess process : XDebuggerManager.getInstance(project).getDebugProcesses(DlvDebugProcess.class)) {
                if (!process.getSession().isStopped()) {
                    process.getSession().rebuildViews();
                }
            }
        }

    }

    public static final class DlvConfigurableProvider extends DebuggerConfigurableProvider {
        public DlvConfigurableProvider() {
        }

        public @NotNull Collection<? extends Configurable> getConfigurables(@NotNull DebuggerSettingsCategory category) {
   

            List<DlvDataViewsSettingsUi> dlvDataViewsSettingsUis;
            if (category == DebuggerSettingsCategory.GENERAL) {
                return Collections.singletonList(new DlvSettingsUi());
          

            } else if (category == DebuggerSettingsCategory.DATA_VIEWS) {
                dlvDataViewsSettingsUis = Collections.singletonList(new DlvDataViewsSettingsUi());
            

                return dlvDataViewsSettingsUis;
            } else {

                return Collections.emptyList();
            }
        }
    }
}
