package com.sentinel.nocalhost.configuration.go.dlv.location;


import com.goide.dlv.DlvVm;
import com.intellij.openapi.util.Ref;
import com.intellij.openapi.vfs.VirtualFile;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public interface DlvPositionConverter {
    @Nullable String toRemotePath(@NotNull VirtualFile var1);

    @Nullable VirtualFile toLocalFile(@NotNull String var1);

    public static class Caching implements DlvPositionConverter {
        private final DlvPositionConverter myDelegate;
        private final ReadWriteLock myLock;
        private final Map<VirtualFile, Ref<String>> myLocalToRemote;
        private final Map<String, Ref<VirtualFile>> myRemoteToLocal;

        public Caching(@NotNull DlvPositionConverter delegate) {
            super();
            this.myLock = new ReentrantReadWriteLock();
            this.myLocalToRemote = new HashMap<>();
            this.myRemoteToLocal = new HashMap<>();
            this.myDelegate = delegate;
        }

        public @Nullable String toRemotePath(@NotNull VirtualFile localFile) {


            this.myLock.readLock().lock();

            Ref<String> result;
            String localPath;
            try {
                result = this.myLocalToRemote.get(localFile);
                if (result != null) {
                    localPath = result.get();
                    return localPath;
                }
            } finally {
                this.myLock.readLock().unlock();
            }

            this.myLock.writeLock().lock();

            String var14;
            try {
                result = this.myLocalToRemote.get(localFile);
                if (result != null) {
                    localPath = result.get();
                    return localPath;
                }

                localPath = localFile.getPath();
                DlvVm.LOG.debug("Find remote path for " + localPath);
                String remotePath = this.myDelegate.toRemotePath(localFile);
                this.myLocalToRemote.put(localFile, Ref.create(remotePath));
                if (remotePath != null) {
                    Ref<VirtualFile> backRef = this.myRemoteToLocal.get(remotePath);
                    if (backRef != null) {
                        VirtualFile existing = backRef.get();
                        if (!localFile.equals(existing)) {
                            DlvVm.LOG.debug("Remap remote path " + remotePath + " from " + (existing != null ? existing.getPath() : null) + " to " + localPath);
                        }
                    }

                    this.myRemoteToLocal.put(remotePath, Ref.create(localFile));
                }

                DlvVm.LOG.debug("Map path local: " + localPath + ", remote: " + remotePath);
                var14 = remotePath;
            } finally {
                this.myLock.writeLock().unlock();
            }

            return var14;
        }

        public @Nullable VirtualFile toLocalFile(@NotNull String remotePath) {

            this.myLock.readLock().lock();

            Ref<VirtualFile> result;
            VirtualFile localFile;
            try {
                result = this.myRemoteToLocal.get(remotePath);
                if (result != null) {
                    localFile = result.get();
                    return localFile;
                }
            } finally {
                this.myLock.readLock().unlock();
            }

            this.myLock.writeLock().lock();

            VirtualFile var13;
            try {
                result = this.myRemoteToLocal.get(remotePath);
                if (result != null) {
                    localFile = (VirtualFile)result.get();
                    return localFile;
                }

                DlvVm.LOG.debug("Find local path for " + remotePath);
                localFile = this.myDelegate.toLocalFile(remotePath);
                this.myRemoteToLocal.put(remotePath, Ref.create(localFile));
                if (localFile != null) {
                    Ref<String> backRef = this.myLocalToRemote.get(localFile);
                    if (backRef != null) {
                        String existing = backRef.get();
                        if (remotePath.equals(existing)) {
                            DlvVm.LOG.debug("Remap local path " + localFile.get() + " from " + existing + " to " + remotePath);
                        }

                        this.myLocalToRemote.put(localFile, Ref.create(remotePath));
                    }
                }

                String var10001 = localFile != null ? localFile.getPath() : null;
                DlvVm.LOG.debug("Map path local: " + var10001 + ", remote: " + remotePath);
                var13 = localFile;
            } finally {
                this.myLock.writeLock().unlock();
            }

            return var13;
        }
    }
}