package com.sentinel.nocalhost.configuration.go.dlv.dump;

import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* loaded from: DlvStackIndex.class */
public class DlvStackIndex {
    private static int ourMinTableSize = 100;
    private final IntTable<String> myFuncTable;
    private final IntTable<String> myFileTable;
    private final IntTable<DlvStack> myStackTable;


    /* JADX INFO: Access modifiers changed from: package-private */
    public DlvStackIndex(@Nullable DlvStackIndex base) {
        this.myFuncTable = createTable(base != null ? base.myFuncTable : null);
        this.myFileTable = createTable(base != null ? base.myFileTable : null);
        this.myStackTable = createTable(base != null ? base.myStackTable : null);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public int addFuncIfAbsent(@Nullable String func) {
        return this.myFuncTable.addIfAbsent(func);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public int addFileIfAbsent(@Nullable String file) {
        return this.myFileTable.addIfAbsent(file);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public int addStackIfAbsent(@Nullable DlvStack stack) {
        return this.myStackTable.addIfAbsent(stack);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public String getFuncName(int funcId) {
        return this.myFuncTable.get(funcId);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public String getFile(int fileId) {
        return this.myFileTable.get(fileId);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @Nullable
    public DlvStack getStack(int stackId) {
        return this.myStackTable.get(stackId);
    }

    int getStackCount() {
        return this.myStackTable.size();
    }

    @NotNull
    static <T> IntTable<T> createTable(@Nullable IntTable<T> base) {
        IntTable<T> base2 = findNonEmpty(base);
        if (base2 == null) {
            return new IntTable<>(null);
        }
        List<T> baseOwnValues = base2.getOwnValues();
        if (baseOwnValues.size() >= ourMinTableSize) {
            return new IntTable<>(base2);
        }
        IntTable<T> result = new IntTable<>(base2.getBase());
        for (T value : base2.getOwnValues()) {
            result.addIfAbsent(value);
        }

        return result;
    }

    @Nullable
    private static <T> IntTable<T> findNonEmpty(@Nullable IntTable<T> table) {
        while (table != null) {
            if (!table.getOwnValues().isEmpty()) {
                return table;
            }
            table = table.getBase();
        }
        return null;
    }

    public static int getMinTableSize() {
        return ourMinTableSize;
    }

    public static void setMinTableSize(int minTableSize) {
        ourMinTableSize = minTableSize;
    }
}
