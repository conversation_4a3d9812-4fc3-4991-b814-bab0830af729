package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.i18n.GoBundle;
import com.goide.inspections.fmtstring.GoFmtStringUtil;
import com.goide.psi.GoBlock;
import com.goide.psi.GoCallExpr;
import com.goide.psi.GoExpression;
import com.goide.psi.GoMethodDeclaration;
import com.goide.psi.GoPointerType;
import com.goide.psi.GoReceiver;
import com.goide.psi.GoReferenceExpression;
import com.goide.psi.GoReturnStatement;
import com.goide.psi.GoSignature;
import com.goide.psi.GoType;
import com.goide.psi.GoTypeSpec;
import com.goide.psi.GoUnaryExpr;
import com.goide.psi.impl.GoPsiImplUtil;
import com.goide.psi.impl.GoTypeUtil;
import com.goide.stubs.index.GoMethodIndex;
import com.goide.util.GoPathResolveScope;
import com.goide.util.GoStdlibUtil;
import com.goide.util.Value;
import com.goide.vendor.GoVendoringUtil;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.module.Module;
import com.intellij.openapi.project.DumbService;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Pair;
import com.intellij.openapi.util.Ref;
import com.intellij.openapi.util.TextRange;
import com.intellij.openapi.util.registry.Registry;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.psi.PsiElement;
import com.intellij.psi.SyntaxTraverser;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.util.ObjectUtils;
import com.intellij.util.containers.ContainerUtil;
import com.intellij.util.indexing.IdFilter;
import com.intellij.xdebugger.XDebugSession;
import com.intellij.xdebugger.frame.XValueNode;
import com.intellij.xdebugger.frame.presentation.XKeywordValuePresentation;
import com.intellij.xdebugger.frame.presentation.XNumericValuePresentation;
import com.intellij.xdebugger.frame.presentation.XRegularValuePresentation;
import com.intellij.xdebugger.frame.presentation.XStringValuePresentation;
import com.intellij.xdebugger.frame.presentation.XValuePresentation;
import com.intellij.xdebugger.impl.ui.tree.nodes.WatchNodeImpl;
import com.intellij.xdebugger.impl.ui.tree.nodes.XValueNodeImpl;
import java.math.BigInteger;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.DateTimeException;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.WeakHashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.AsyncPromise;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;

public class DlvValueRenderers {
    private static final @NonNls ZoneId UTC = ZoneId.of("UTC");
    private final ConcurrentMap<Object, DlvValueRenderer> myCustomRenderers = new ConcurrentHashMap<>();
    static final DlvValueRendererSelectableInUi BINARY = new DlvValueRenderers.IntegerValueRenderer(GoBundle.message("go.debugger.renderers.binary.name")) {
        public @NotNull String formatNumericValue(@NotNull DlvApi.@NotNull Variable var) {

            String value = var.value;

            String var10000;
            try {
                long v = (new BigInteger(value, 10)).longValue();
                var10000 = "0b" + Long.toBinaryString(v);
            } catch (NumberFormatException var5) {
                var10000 = StringUtil.notNullize(value);


                return var10000;
            }



            return var10000;
        }
    };
    static final DlvValueRendererSelectableInUi DECIMAL = new DlvValueRenderers.IntegerValueRenderer(GoBundle.message("go.debugger.renderers.decimal.name")) {
        public @NotNull String formatNumericValue(@NotNull DlvApi.@NotNull Variable var) {


            return StringUtil.notNullize(var.value);
        }
    };
    static final DlvValueRendererSelectableInUi HEX = new DlvValueRenderers.IntegerValueRenderer(GoBundle.message("go.debugger.renderers.hex.name")) {
        public @NotNull String formatNumericValue(@NotNull DlvApi.@NotNull Variable var) {


            String value = var.value;

            String var10000;
            try {
                long v = (new BigInteger(value, 10)).longValue();
                var10000 = "0x" + StringUtil.toUpperCase(Long.toHexString(v));
            } catch (NumberFormatException var5) {
                var10000 = StringUtil.notNullize(value);


                return var10000;
            }



            return var10000;
        }
    };
    public static final DlvValueRenderer DEFAULT = new DlvValueRenderer() {
        public @NotNull Promise<@NotNull XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            DlvApi.Variable var = value.getVariable();
            String varValue = ObjectUtils.notNull(var.value, "");


            return value.getTypePresentationAsync(node).then((typePresentation) -> {
                if (var.isNumber()) {
                    return new XNumericValuePresentation(varValue) {
                        public @Nullable String getType() {
                            return typePresentation;
                        }

                        public @NotNull String getSeparator() {


                            return StringUtil.isEmpty(typePresentation) && StringUtil.isEmpty(varValue) ? "" : super.getSeparator();
                        }
                    };
                } else if (var.isString()) {
                    return new XStringValuePresentation(varValue) {
                        public @Nullable String getType() {
                            return typePresentation;
                        }
                    };
                } else if (var.isBool()) {
                    return new XKeywordValuePresentation(varValue) {
                        public @Nullable String getType() {
                            return typePresentation;
                        }

                        public @NotNull String getSeparator() {


                            return StringUtil.isEmpty(typePresentation) && StringUtil.isEmpty(varValue) ? "" : super.getSeparator();
                        }
                    };
                } else {
                    return !var.isSlice() && !var.isArray() ? new DlvRegularValuePresentation(varValue, typePresentation) : new DefaultSliceOrArrayPresentation(var, typePresentation);
                }
            });
        }
    };

    public DlvValueRenderers() {
    }

    public @NotNull DlvValueRenderer getRenderer(@Nullable DlvStackFrame frame, @NotNull XValueNode node, @NotNull DlvXValue value) {

        if (value.isUnreadable()) {
            return new DlvValueRenderers.DlvUnreadableValueRenderer();
        } else if (value.isNil()) {
            return new DlvValueRenderers.DlvNilValueRenderer();
        } else {
            Object id = frame != null ? getId(frame, node) : null;
            DlvValueRenderer customRenderer = id != null ? this.myCustomRenderers.get(id) : null;
            return customRenderer != null ? customRenderer : getDefaultRenderer(node);
        }
    }

    public void setRenderer(@NotNull DlvStackFrame frame, @NotNull XValueNode node, @NotNull DlvValueRenderer renderer) {


        Object id = getId(frame, node);
        if (id != null) {
            DlvValueRenderer defaultRenderer = getDefaultRenderer(node);
            if (renderer == defaultRenderer) {
                this.myCustomRenderers.remove(id);
            } else {
                this.myCustomRenderers.put(id, renderer);
            }

        }
    }

    public static @NotNull Collection<DlvValueRendererSelectableInUi> getRenderersSelectableInUi() {


        return Arrays.asList(BINARY, DECIMAL, HEX);
    }

    private static @NotNull DlvValueRenderer getDefaultRenderer(@NotNull XValueNode node) {


        DlvXValue dlvValue = DlvXValue.getNodeValue(node);
        if (dlvValue != null) {
            String type = dlvValue.getVariable().type;
            if ("time.Duration".equals(type)) {
                return new DlvValueRenderers.DlvDurationValueRenderer();
            }

            if ("time.Time".equals(type)) {
                return new DlvValueRenderers.DlvTimeValueRenderer(dlvValue);
            }

            if ("net.IP".equals(type)) {
                return new DlvValueRenderers.DlvIpValueRenderer();
            }

            if ("net.IPMask".equals(type)) {
                return new DlvValueRenderers.DlvIpMaskValueRenderer();
            }

            if ("net.IPNet".equals(type)) {
                return new DlvValueRenderers.DlvIpNetValueRenderer();
            }

            if ("net/http.Request".equals(type)) {
                return new DlvValueRenderers.DlvHttpRequestRenderer();
            }

            Project project = dlvValue.getProject();
            if (project != null) {
                DlvValueRenderer defaultRenderer = dlvValue.getVariable().isInteger() ? JetDevSettings.getInstance().getDefaultIntegerRenderer() : DEFAULT;
                return new DlvValueRenderers.DlvStringerValueRenderer(project, defaultRenderer, dlvValue);
            }
        }


        return DEFAULT;
    }

    private static @Nullable Object getId(@NotNull DlvStackFrame frame, @NotNull XValueNode node) {

        if (node instanceof XValueNodeImpl) {
            DlvXValue dlvValue = ObjectUtils.tryCast(((XValueNodeImpl)node).getValueContainer(), DlvXValue.class);
            if (dlvValue != null) {
                DlvApi.Function function = frame.getLocation().function;
                String functionName = function != null ? function.name : null;
                if (functionName == null) {
                    return null;
                }

                DlvApi.Variable var = dlvValue.getVariable();
                return Pair.create(Pair.create(functionName, var.addr), Pair.create(var.name, node instanceof WatchNodeImpl));
            }
        }

        return node;
    }

    private static @NotNull ZoneId tryGetZoneId(@NotNull String zoneName, int offsetHours) {


        ZoneId var10000;
        try {
            var10000 = ZoneId.of(zoneName);
        } catch (DateTimeException var3) {
            var10000 = ZoneId.of(ZoneOffset.ofHours(offsetHours).getId());


            return var10000;
        }



        return var10000;
    }

    private static Promise<DlvApi.Variable> loadChildrenIfNeeded(@NotNull DlvXValue value, @NotNull DlvApi.@NotNull Variable var) {


        return var.children.length == 0 ? value.getProcess().send(new DlvRequest.Eval(DlvXValue.evaluationExpression(var), value.myFrameId, value.myGoroutineId)) : Promises.resolvedPromise(var);
    }

    private static final class DlvUnreadableValueRenderer implements DlvValueRenderer {
        private DlvUnreadableValueRenderer() {
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {

            return value.getTypePresentationAsync(node).then((type) -> new DvlUnreadableValuePresentation(type, value.getVariable().unreadable));
        }
    }

    private static final class DlvNilValueRenderer implements DlvValueRenderer {
        private DlvNilValueRenderer() {
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            return value.getTypePresentationAsync(node).thenAsync((type) -> getNonNilConcreteValueTypeAsync(node, value).then((typedNil) -> new XValuePresentation() {
                public void renderValue(@NotNull XValuePresentation.@NotNull XValueTextRenderer renderer) {


                    if (typedNil != null) {
                        renderer.renderValue(typedNil);
                    } else {
                        renderer.renderKeywordValue("nil");
                    }

                }

                public @Nullable String getType() {
                    return type;
                }
            }));
        }

        private static @NotNull Promise<@Nullable String> getNonNilConcreteValueTypeAsync(@NotNull XValueNode node, @NotNull DlvXValue value) {

            DlvXValue pointer = value.getTopFollowedPointer();
            DlvApi.Variable variable = pointer != null ? pointer.getVariable() : null;
            boolean nonNilConcreteValue = variable != null && variable.isInterface() && variable.children.length == 1 && !DlvXValue.isZero(variable.children[0].addr);
            Promise<String> var10000;
            if (!nonNilConcreteValue) {
                var10000 = Promises.resolvedPromise(null);


                return var10000;
            } else {
                var10000 = value.getTypePartsAsync(node).then((typeParts) -> {
                    if (typeParts.size() > 1) {
                        List<String> typesExceptFirst = typeParts.subList(1, typeParts.size());
                        return String.join("(", typesExceptFirst) + "(nil" + ")".repeat(typesExceptFirst.size());
                    } else {
                        return null;
                    }
                });


                return var10000;
            }
        }
    }

    private static class DlvDurationValueRenderer implements DlvValueRenderer {
        private DlvDurationValueRenderer() {
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            return value.getTypePresentationAsync(node).then((type) -> new XValuePresentation() {
                public void renderValue(@NotNull XValuePresentation.@NotNull XValueTextRenderer renderer) {

                    String numericValue = DlvDurationValueRenderer.getNumericValue(value.getVariable().value);
                    renderer.renderNumericValue(numericValue);

                    try {
                        long ns = Long.parseLong(numericValue);
                        renderer.renderSpecialSymbol(" ");
                        renderer.renderComment(DlvDurationValueRenderer.formatDuration(ns));
                    } catch (NumberFormatException ignored) {
                    }

                }

                public String getType() {
                    return type;
                }
            });
        }

        private static String getNumericValue(@NotNull @NonNls String value) {

            return switch (value) {
                case "Nanosecond", "Nanosecond (1)" -> "1";
                case "Microsecond", "Microsecond (1000)" -> "1000";
                case "Millisecond", "Millisecond (1000000)" -> "1000000";
                case "Second", "Second (1000000000)" -> "1000000000";
                case "Minute", "Minute (60000000000)" -> "60000000000";
                case "Hour", "Hour (3600000000000)" -> "3600000000000";
                default -> value;
            };
        }

        private static @NonNls String formatDuration(long ns) {
            if (ns == 0L) {
                return "0s";
            } else {
                String sign = ns < 0L ? "-" : "";
                ns = Math.abs(ns);
                if (ns < TimeUnit.MICROSECONDS.toNanos(1L)) {
                    return sign + ns + "ns";
                } else {
                    long seconds;
                    if (ns < TimeUnit.MILLISECONDS.toNanos(1L)) {
                        seconds = TimeUnit.NANOSECONDS.toMicros(ns);
                        return sign + seconds + getFraction(ns, TimeUnit.MICROSECONDS.toNanos(1L)) + "µs";
                    } else if (ns < TimeUnit.SECONDS.toNanos(1L)) {
                        seconds = TimeUnit.NANOSECONDS.toMillis(ns);
                        return sign + seconds + getFraction(ns, TimeUnit.MILLISECONDS.toNanos(1L)) + "ms";
                    } else {
                        seconds = TimeUnit.NANOSECONDS.toSeconds(ns);
                        String result = seconds % 60L + getFraction(ns, TimeUnit.SECONDS.toNanos(1L)) + "s";
                        long minutes = seconds / 60L;
                        if (minutes > 0L) {
                            result = minutes % 60L + "m" + result;
                        }

                        long hours = minutes / 60L;
                        if (hours > 0L) {
                            result = hours + "h" + result;
                        }

                        return sign + result;
                    }
                }
            }
        }

        private static @NotNull String getFraction(long value, long precision) {
            long fraction = value - precision * (value / precision);
            if (fraction == 0L) {
                return "";
            } else {
                String width = String.valueOf((int)Math.log10((double)precision));


                return StringUtil.trimTrailing(String.format(".%0" + width + "d", fraction), '0');
            }
        }
    }

    private static final class DlvTimeValueRenderer implements DlvValueRenderer {
        private static final BigInteger NANOS_MASK = BigInteger.valueOf(1073741823L);
        private static final LocalDateTime MONOTONIC_TIME_ORIGIN;
        private static final LocalDateTime NON_MONOTONIC_TIME_ORIGIN;
        private final DlvXValue myValue;

        private DlvTimeValueRenderer(@NotNull DlvXValue value) {


            super();
            this.myValue = value;
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            return this.computeValue(value.getVariable()).thenAsync((v) -> v != null ? value.getTypePresentationAsync(node).then((t) -> new DlvRegularValuePresentation(v, t)) : DlvValueRenderers.DEFAULT.getPresentation(node, value));
        }

        private @NotNull Promise<@Nullable String> computeValue(@NotNull DlvApi.@NotNull Variable var) {


            DlvApi.Variable[] children = var.children;
            DlvApi.Variable wallVar = DlvUtil.getByName(children, "wall");
            DlvApi.Variable extVar = DlvUtil.getByName(children, "ext");
            DlvApi.Variable locVar = DlvUtil.getByName(children, "loc");
            Promise<String> var10000;
            if (wallVar != null && extVar != null && locVar != null) {
                label53: {
                    try {
                        BigInteger wall = new BigInteger(wallVar.value);
                        long ext = Long.parseLong(extVar.value);
                        long nanos = wall.and(NANOS_MASK).longValue();
                        if (hasMonotonic(wall)) {
                            long seconds = wall.clearBit(63).shiftRight(30).longValue();
                            ZonedDateTime time = ZonedDateTime.of(MONOTONIC_TIME_ORIGIN, DlvValueRenderers.UTC).plusSeconds(seconds).plusNanos(nanos).withZoneSameInstant(ZoneId.systemDefault());
                            String nanoFormat = getNanoFormat(time.getNano());
                            var10000 = Promises.resolvedPromise(time.format(DateTimeFormatter.ofPattern("uuuu-MM-dd HH:mm:ss" + nanoFormat + " ZZZ 'm=" + formatMonotonicTime(ext) + "'")));
                            break label53;
                        }

                        var10000 = this.getZoneId(locVar).thenAsync((zoneId) -> {
                            if (zoneId == null) {
                                return Promises.resolvedPromise(null);
                            } else {
                                ZonedDateTime time = ZonedDateTime.of(NON_MONOTONIC_TIME_ORIGIN, DlvValueRenderers.UTC).plusSeconds(ext).plusNanos(nanos).withZoneSameInstant((ZoneId) zoneId);
                                String nanoFormat = getNanoFormat(time.getNano());
                                return Promises.resolvedPromise(time.format(DateTimeFormatter.ofPattern("uuuu-MM-dd HH:mm:ss" + nanoFormat + " ZZZ")));
                            }
                        });
                    } catch (DateTimeException | NumberFormatException var15) {
                        var10000 = Promises.resolvedPromise(null);

                        return var10000;
                    }



                    return var10000;
                }


            } else {
                var10000 = Promises.resolvedPromise(null);


            }
            return var10000;
        }

        private static @NotNull String getNanoFormat(int nanos) {
            if (nanos == 0) {
                return "";
            } else {
                int digits;
                for(digits = 9; nanos > 0 && nanos % 10 == 0; nanos /= 10) {
                    --digits;
                }


                return "." + StringUtil.repeat("S", digits);
            }
        }

        private static boolean hasMonotonic(@NotNull BigInteger wall) {


            return wall.testBit(63);
        }

        private static @NotNull String formatMonotonicTime(long ext) {
            long absNanos = Math.abs(ext);
            long seconds = absNanos / TimeUnit.SECONDS.toNanos(1L);
            long nanos = absNanos - seconds * TimeUnit.SECONDS.toNanos(1L);


            return String.format("%+d.%09d", (long)Long.signum(ext) * seconds, nanos);
        }

        private Promise<ZoneId> getZoneId(@NotNull DlvApi.@NotNull Variable locationPointer) {


            if (locationPointer.children.length != 1) {
                Promise<ZoneId> var10000;

                var10000 = Promises.resolvedPromise(null);


                return var10000;
            } else {
                Promise<ZoneId> promise;
                DlvApi.Variable location = locationPointer.children[0];
                if (BigInteger.ZERO.equals(location.addr)) {
                    promise = Promises.resolvedPromise(DlvValueRenderers.UTC);

                    return promise;
                } else {
                    promise = DlvValueRenderers.loadChildrenIfNeeded(this.myValue, location).then((loaded) -> {
                        DlvApi.Variable nameField = DlvUtil.getByName(loaded.children, "name");
                        if (nameField == null) {
                            return null;
                        } else {
                            String name = nameField.value;
                            ZoneId var10000;
                            ZoneId var5;
                            switch (nameField.value) {
                                case "Asia/Qostanay":
                                    var5 = DlvValueRenderers.tryGetZoneId(name, 6);
                                    var10000 = var5;
                                    break;
                                case "Asia/Qyzylorda":
                                    var5 = DlvValueRenderers.tryGetZoneId(name, 5);
                                    var10000 = var5;
                                    break;
                                case "Factory":
                                    var5 = ZoneId.of(ZoneOffset.ofHours(0).getId());
                                    var10000 = var5;
                                    break;
                                case "EST":
                                    var5 = ZoneId.of(ZoneOffset.ofHours(-5).getId());
                                    var10000 = var5;
                                    break;
                                case "HST":
                                    var5 = ZoneId.of(ZoneOffset.ofHours(-10).getId());
                                    var10000 = var5;
                                    break;
                                case "MST":
                                    var5 = ZoneId.of(ZoneOffset.ofHours(-7).getId());
                                    var10000 = var5;
                                    break;
                                case "ROC":
                                    var5 = ZoneId.of(ZoneOffset.ofHours(8).getId());
                                    var10000 = var5;
                                    break;
                                case "Local":
                                    var5 = ZoneId.systemDefault();
                                    var10000 = var5;
                                    break;
                                default:
                                    try {
                                        var5 = ZoneId.of(name);
                                    } catch (DateTimeException var7) {
                                        return null;
                                    }
                                    var10000 = var5;
                            }
                            return var10000;
                        }
                    });
                    return promise;
                }
            }
        }

        static {
            MONOTONIC_TIME_ORIGIN = LocalDateTime.of(1885, Month.JANUARY, 1, 0, 0);
            NON_MONOTONIC_TIME_ORIGIN = LocalDateTime.of(1, Month.JANUARY, 1, 0, 0);
        }
    }

    private static class DlvIpValueRenderer implements DlvValueRenderer {
        private static final Pattern ZERO_RUN = Pattern.compile("(0:){2,}");

        private DlvIpValueRenderer() {
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            return value.getTypePresentationAsync(node).then((type) -> new DlvRegularValuePresentation(formatValidAddress(value.getVariable()), type));
        }

        private static @NotNull String formatValidAddress(@NotNull DlvApi.@NotNull Variable var) {


            byte[] bytes = DlvUtil.readBytes(var);
            if (bytes == null) {
                return "";
            } else if (bytes.length == 0) {
                return "<nil>";
            } else {
                String result = formatValidAddress(bytes);
                return result != null ? result : formatInvalidAddress(bytes);
            }
        }

        private static @Nullable String formatValidAddress(byte @NotNull [] bytes) {


            if (bytes.length != 4 && bytes.length != 16) {
                return null;
            } else {
                try {
                    String address = InetAddress.getByAddress(bytes).getHostAddress();
                    TextRange range = findLongestZeroRun(address);
                    if (range == null) {
                        return address;
                    } else {
                        return range.getStartOffset() == 0 ? ":" + range.replace(address, ":") : range.replace(address, ":");
                    }
                } catch (UnknownHostException var3) {
                    return null;
                }
            }
        }

        private static @Nullable TextRange findLongestZeroRun(@NotNull String s) {

            Matcher matcher = ZERO_RUN.matcher(s + ":");
            TextRange longestRun = null;

            while(true) {
                TextRange run;
                do {
                    int start;
                    int end;
                    do {
                        if (!matcher.find()) {
                            return longestRun != null ? longestRun.intersection(TextRange.allOf(s)) : null;
                        }

                        start = matcher.start();
                        end = matcher.end();
                        if (start <= 0 || s.charAt(matcher.start() - 1) == ':') {
                            break;
                        }

                        start += 2;
                    } while(end - start == 2);

                    run = TextRange.create(start, end);
                } while(longestRun != null && run.getLength() <= longestRun.getLength());

                longestRun = run;
            }
        }

        private static @NotNull String formatInvalidAddress(byte[] bytes) {

            return "?" + StringUtil.toHexString(bytes);
        }
    }

    private static class DlvIpMaskValueRenderer implements DlvValueRenderer {
        private DlvIpMaskValueRenderer() {
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, final @NotNull DlvXValue value) {


            return Promises.resolvedPromise(new XValuePresentation() {
                public void renderValue(@NotNull XValuePresentation.@NotNull XValueTextRenderer renderer) {

                    byte[] bytes = DlvUtil.readBytes(value.getVariable());
                    if (bytes != null) {
                        if (bytes.length == 0) {
                            renderer.renderValue("<nil>");
                        } else {
                            renderer.renderNumericValue(StringUtil.toHexString(bytes));
                            int maskLength = DlvIpMaskValueRenderer.getSimpleMaskLength(bytes);
                            if (maskLength != -1) {
                                renderer.renderComment(" /" + maskLength);
                            }
                        }

                    }
                }
            });
        }

        private static int getSimpleMaskLength(byte[] bytes) {
            int result = 0;

            for(int i = 0; i < bytes.length; ++i) {
                byte b = bytes[i];
                if (b != -1) {
                    while((b & 128) != 0) {
                        ++result;
                        b = (byte)(b << 1);
                    }

                    if (b != 0) {
                        return -1;
                    }

                    for(int j = i + 1; j < bytes.length; ++j) {
                        if (bytes[j] != 0) {
                            return -1;
                        }
                    }

                    return result;
                }

                result += 8;
            }

            return result;
        }
    }

    private static class DlvIpNetValueRenderer implements DlvValueRenderer {
        private DlvIpNetValueRenderer() {
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            return value.getTypePresentationAsync(node).then((type) -> new DlvRegularValuePresentation(formatMask(value.getVariable()), type));
        }

        private static @NotNull String formatMask(@NotNull DlvApi.@NotNull Variable var) {


            DlvApi.Variable[] children = var.children;
            DlvApi.Variable ipField = DlvUtil.getByName(children, "IP");
            DlvApi.Variable maskField = DlvUtil.getByName(children, "Mask");
            if (ipField != null && maskField != null) {
                byte[] ipBytes = DlvUtil.readBytes(ipField);
                byte[] maskBytes = DlvUtil.readBytes(maskField);
                if (ipBytes != null && maskBytes != null) {
                    if (ipBytes.length == 0 && maskBytes.length == 0) {
                        return "<nil>";
                    } else {
                        String ip = DlvValueRenderers.DlvIpValueRenderer.formatValidAddress(ipBytes);
                        if (ip == null || ip.contains(":") && maskBytes.length != 16) {
                            return "";
                        } else {
                            if (!ip.contains(":") && maskBytes.length == 16) {
                                maskBytes = new byte[]{maskBytes[12], maskBytes[13], maskBytes[14], maskBytes[15]};
                            }

                            int maskLength = DlvValueRenderers.DlvIpMaskValueRenderer.getSimpleMaskLength(maskBytes);
                            String mask = maskLength != -1 ? String.valueOf(maskLength) : StringUtil.toHexString(maskBytes);


                            return ip + "/" + mask;
                        }
                    }
                } else {
                    return "";
                }
            } else {
                return "";
            }
        }
    }

    private static final class DlvHttpRequestRenderer implements DlvValueRenderer {
        private DlvHttpRequestRenderer() {
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            String method = getStringValueByName(value.getVariable().children, "Method");
            Promise<XValuePresentation> var10000;
            if (StringUtil.isEmpty(method)) {
                var10000 = DlvValueRenderers.DEFAULT.getPresentation(node, value);


                return var10000;
            } else {
                DlvApi.Variable urlPointerVar = DlvUtil.getByName(value.getVariable().children, "URL");
                DlvApi.Variable urlVar = urlPointerVar != null && urlPointerVar.isPtr() ? urlPointerVar.children[0] : null;
                if (urlVar == null) {
                    var10000 = DlvValueRenderers.DEFAULT.getPresentation(node, value);


                    return var10000;
                } else {
                    var10000 = value.getTypePresentationAsync(node).thenAsync((type) -> DlvValueRenderers.loadChildrenIfNeeded(value, urlVar).then((loaded) -> {
                        String path = getStringValueByName(loaded.children, "Path");
                        String host = getStringValueByName(loaded.children, "Host");
                        String scheme = getStringValueByName(loaded.children, "Scheme");
                        String presentation;
                        if (StringUtil.isNotEmpty(scheme) && StringUtil.isNotEmpty(host)) {
                            presentation = String.format("%s %s://%s%s", method, scheme, host, path);
                        } else {
                            presentation = StringUtil.isEmpty(path) ? method : String.format("%s %s", method, path);
                        }

                        return new DlvRegularValuePresentation(presentation, type);
                    }));

                    return var10000;
                }
            }
        }

        private static @Nullable String getStringValueByName(DlvApi.Variable[] vars, @NotNull @NonNls String name) {


            DlvApi.Variable var = DlvUtil.getByName(vars, name);
            return var != null && var.isString() ? var.value : null;
        }
    }

    private static final class DlvStringerValueRenderer implements DlvValueRenderer {
        private static final Map<XDebugSession, Map<String, Ref<GoFmtSprintfEvaluator>>> ourEvaluatorCache = new WeakHashMap<>();
        private final Project myProject;
        private final DlvValueRenderer myDefaultRenderer;
        private final DlvXValue myValue;

        private DlvStringerValueRenderer(@NotNull Project project, @NotNull DlvValueRenderer defaultRenderer, @NotNull DlvXValue dlvXValue) {

            super();
            this.myProject = project;
            this.myDefaultRenderer = defaultRenderer;
            this.myValue = dlvXValue;
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            DlvApi.Variable var = value.getVariable();


            return this.getFmtEvaluatorAsync(node).thenAsync((evaluator) -> {
                if (node.isObsolete()) {
                    return Promises.rejectedPromise();
                } else if (evaluator == null) {
                    return this.myDefaultRenderer.getPresentation(node, value);
                } else {
                    int maxAccessorDepth = evaluator.getMaxDynamicArgumentAccessorsDepth();
                    if (maxAccessorDepth != 0 && (maxAccessorDepth != 1 || var.len != 0L && var.children.length <= 0)) {
                        DlvRequest.DlvLoadConfig config = (new DlvRequest.DlvLoadConfig(JetDevSettings.getInstance())).setMaxArrayValues(0).setMaxVariableRecurse(Math.min(maxAccessorDepth, 3));
                        return value.getTypePresentationAsync(node).thenAsync((type) -> this.myValue.getProcess().send(new DlvRequest.Eval(DlvXValue.evaluationExpression(var), this.myValue.myFrameId, this.myValue.myGoroutineId, config)).thenAsync((valueWithChildren) -> evaluator.eval(this.myValue, valueWithChildren)).then((result) -> result != null ? new DlvRegularValuePresentation(result, type) : null));
                    } else {
                        return value.getTypePresentationAsync(node).thenAsync((type) -> evaluator.eval(this.myValue, var).thenAsync((result) -> result != null ? Promises.resolvedPromise(new DlvRegularValuePresentation(result, type)) : this.myDefaultRenderer.getPresentation(node, value)));
                    }
                }
            });
        }

        private @NotNull Promise<GoFmtSprintfEvaluator> getFmtEvaluatorAsync(@NotNull XValueNode node) {

            AsyncPromise<GoFmtSprintfEvaluator> result = new AsyncPromise<>();
            ApplicationManager.getApplication().executeOnPooledThread(() -> {
                result.setResult(this.getFmtEvaluator(node));
            });

            return result;
        }

        private @Nullable GoFmtSprintfEvaluator getFmtEvaluator(@NotNull XValueNode node) {

            if (node.isObsolete()) {
                return null;
            } else {
                synchronized(ourEvaluatorCache) {
                    XDebugSession session = this.myValue.getProcess().getSession();
                    String type = this.myValue.getVariable().type;
                    Map<String, Ref<GoFmtSprintfEvaluator>> evaluators = ourEvaluatorCache.get(session);
                    Ref<GoFmtSprintfEvaluator> evaluator = evaluators != null ? evaluators.get(type) : null;
                    if (evaluator == null) {
                        evaluator = Ref.create(ReadAction.compute(this::getFmtEvaluatorInner));
                        evaluators = ourEvaluatorCache.computeIfAbsent(session, (it) -> new HashMap<>());
                        evaluators.put(type, evaluator);
                    }

                    return evaluator.get();
                }
            }
        }

        @Nullable
        private GoFmtSprintfEvaluator getFmtEvaluatorInner() {
            GoMethodDeclaration stringMethod = this.findStringMethod();
            GoBlock block = stringMethod != null ? stringMethod.getBlock() : null;
            List<PsiElement> returns = (SyntaxTraverser.psiTraverser(block).filter((it) -> it instanceof GoReturnStatement)).toList();
            GoReturnStatement returnStatement = ObjectUtils.tryCast(ContainerUtil.getOnlyItem(returns), GoReturnStatement.class);
            GoExpression returnExpression = ContainerUtil.getOnlyItem(returnStatement != null ? returnStatement.getExpressionList() : null);
            if (returnExpression == null) {
                return null;
            } else {
                String stringMethodName = stringMethod != null ? stringMethod.getName() : null;
                boolean printDiagnostics = "debugString".equals(stringMethodName) || "DebugString".equals(stringMethodName);
                Value<?> value = returnExpression.getValue();
                if (value != null) {
                    return new GoFmtSprintfEvaluator("%s", Collections.singletonList(new GoFmtSprintfArgumentStatic(value)), printDiagnostics);
                } else {
                    GoReceiver receiver = stringMethod != null ? stringMethod.getReceiver() : null;
                    String receiverName = receiver != null ? receiver.getName() : null;
                    if (receiverName != null && !"_".equals(receiverName)) {
                        if (returnExpression instanceof GoReferenceExpression) {
                            return new GoFmtSprintfEvaluator("%s", Collections.singletonList(new GoFmtSprintfArgumentDynamic(returnExpression.getProject(), receiverName, returnExpression.getText(), false, false)), printDiagnostics);
                        } else {
                            if (returnExpression instanceof GoCallExpr call) {
                                if (GoStdlibUtil.isStdLibCall(call, "fmt", "Sprintf")) {
                                    List<GoExpression> arguments = call.getArgumentList().getExpressionList();
                                    GoExpression formatArg = ContainerUtil.getFirstItem(arguments);
                                    String format = formatArg != null ? GoFmtStringUtil.getStringValue(formatArg) : null;
                                    if (format == null) {
                                        return null;
                                    }

                                    List<GoFmtSprintfArgument> args = getFmtArguments(receiverName, arguments.subList(1, arguments.size()));
                                    return args != null ? new GoFmtSprintfEvaluator(format, args, printDiagnostics) : null;
                                }
                            }

                            return null;
                        }
                    } else {
                        return null;
                    }
                }
            }
        }

        private static @Nullable List<GoFmtSprintfArgument> getFmtArguments(@NotNull String receiverName, @NotNull List<GoExpression> arguments) {


            List<GoFmtSprintfArgument> args = new ArrayList<>();

            for (GoExpression arg : arguments) {
                GoFmtSprintfArgument fmtArg = null;
                Value<?> value = arg.getValue();
                if (value != null) {
                    fmtArg = new GoFmtSprintfArgumentStatic(value);
                } else if (arg instanceof GoReferenceExpression) {
                    GoType type = arg.getGoType(null);
                    fmtArg = new GoFmtSprintfArgumentDynamic(arg.getProject(), receiverName, arg.getText(), false, type instanceof GoPointerType);
                } else if (arg instanceof GoUnaryExpr unaryExpr) {
                    if (unaryExpr.getMul() != null && unaryExpr.getExpression() instanceof GoReferenceExpression) {
                        fmtArg = new GoFmtSprintfArgumentDynamic(arg.getProject(), receiverName, unaryExpr.getExpression().getText(), true, false);
                    } else if (unaryExpr.getBitAnd() != null && unaryExpr.getExpression() instanceof GoReferenceExpression) {
                        fmtArg = new GoFmtSprintfArgumentDynamic(arg.getProject(), receiverName, unaryExpr.getExpression().getText(), false, true);
                    }
                } else if (arg instanceof GoCallExpr call) {
                    GoExpression callArgument = ContainerUtil.getOnlyItem(call.getArgumentList().getExpressionList());
                    if (callArgument != null) {
                        GoReferenceExpression callExpression = ObjectUtils.tryCast(call.getExpression(), GoReferenceExpression.class);
                        GoTypeSpec typeConversionCall = ObjectUtils.tryCast(callExpression != null ? callExpression.resolve() : null, GoTypeSpec.class);
                        if (typeConversionCall != null && GoPsiImplUtil.isBuiltinFile(typeConversionCall.getContainingFile())) {
                            GoType type = callArgument.getGoType(null);
                            if (type != null && GoTypeUtil.identical(type.getUnderlyingType(callArgument), typeConversionCall.getSpecType(), true, arg)) {
                                fmtArg = new GoFmtSprintfArgumentDynamic(arg.getProject(), receiverName, callArgument.getText(), false, type instanceof GoPointerType);
                            }
                        }
                    }
                }

                if (fmtArg == null) {
                    return null;
                }

                args.add(fmtArg);
            }

            return args;
        }

        private @Nullable GoMethodDeclaration findStringMethod() {
            Module module = DlvUtil.getModule(this.myValue.getProcess().getSession());
            if (!DumbService.getInstance(this.myProject).isDumb() && module != null) {
                DlvNamedType dlvNamedType = DlvNamedType.parse(this.myValue.getVariable().type);
                if (dlvNamedType == null) {
                    return null;
                } else {
                    GlobalSearchScope scope = dlvNamedType.isFromMainPackage() ? this.myValue.getProcess().getMainPackageScope() : GoPathResolveScope.create(this.myProject, module, (PsiElement)null);
                    if (scope == null) {
                        return null;
                    } else {
                        boolean vendoring = GoVendoringUtil.isVendoringEnabled(module);
                        Set<String> pkgNames = this.myValue.getProcess().getSymbolPrettier().getPackageNames(dlvNamedType.getDlvPackagePath());
                        Iterator<String> var6 = pkgNames.iterator();

                        label67:
                        while(var6.hasNext()) {
                            String pkgName = var6.next();
                            Collection<GoMethodDeclaration> methods = GoMethodIndex.find(getNameForMethodIndex(pkgName, dlvNamedType), this.myProject, scope, (IdFilter)null);
                            String methodNames = Registry.stringValue("go.debugger.presentation.methods");
                            Iterator<String> var10 = StringUtil.split(methodNames, ",").iterator();

                            while(true) {
                                String methodName;
                                do {
                                    if (!var10.hasNext()) {
                                        continue label67;
                                    }

                                    methodName = var10.next();
                                    methodName = methodName.trim();
                                } while(methodName.isEmpty());

                                for (GoMethodDeclaration method : methods) {
                                    if (methodName.equals(method.getName())) {
                                        GoSignature signature = method.getSignature();
                                        if (signature != null) {
                                            int paramsCount = signature.getParameters().getParameterDeclarationList().size();
                                            if (paramsCount == 0) {
                                                GoType resultType = signature.getResultType();
                                                if (GoTypeUtil.isString(resultType, method) && (dlvNamedType.isFromMainPackage() || dlvNamedType.getPackagePath().equals("command-line-arguments") || dlvNamedType.getPackagePath().equals(method.getContainingFile().getImportPath(vendoring)))) {
                                                    return method;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        return null;
                    }
                }
            } else {
                return null;
            }
        }

        private static @NotNull String getNameForMethodIndex(@NotNull String packageName, @NotNull DlvNamedType dlvNamedType) {


            return StringUtil.isEmpty(packageName) ? dlvNamedType.getTypeName() : packageName + "." + dlvNamedType.getTypeName();
        }
    }

    static class DefaultSliceOrArrayPresentation extends DlvValueRenderers.DlvRegularValuePresentation {
        private DefaultSliceOrArrayPresentation(@NotNull DlvApi.@NotNull Variable var, @Nullable String type) {
            super("len:" + var.len + (var.isSlice() ? ", cap:" + var.cap : ""), type);
        }
    }

    private static class DlvRegularValuePresentation extends XRegularValuePresentation {
        private DlvRegularValuePresentation(@NotNull String value, @Nullable String type) {


            super(value, type, StringUtil.isEmpty(value) && StringUtil.isEmpty(type) ? "" : " = ");
        }
    }

    private abstract static class IntegerValueRenderer implements DlvValueRendererSelectableInUi {
        private final @Nls String myName;

        protected IntegerValueRenderer(@NotNull @Nls String name) {

            super();
            this.myName = name;
        }

        public @NotNull @Nls String getName() {

            return this.myName;
        }

        public boolean isApplicable(@Nullable DlvXValue value) {
            return value != null && value.getVariable().isInteger();
        }

        public @NotNull Promise<@Nullable XValuePresentation> getPresentation(@NotNull XValueNode node, @NotNull DlvXValue value) {


            String valuePresentation = this.formatNumericValue(value.getVariable());

            return value.getTypePresentationAsync(node).then((typePresentation) -> {
                return new XNumericValuePresentation(valuePresentation) {
                    public @Nullable String getType() {
                        return typePresentation;
                    }

                    public @NotNull String getSeparator() {


                        return StringUtil.isEmpty(typePresentation) && StringUtil.isEmpty(valuePresentation) ? "" : super.getSeparator();
                    }
                };
            });
        }

        abstract @NotNull String formatNumericValue(@NotNull DlvApi.@NotNull Variable var1);
    }
}