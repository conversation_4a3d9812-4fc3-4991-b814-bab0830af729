package com.sentinel.nocalhost.configuration.go.dlv.dump;

import com.goide.dlv.dump.DumpedThread;
import com.intellij.execution.ui.ConsoleView;
import com.intellij.execution.ui.ConsoleViewContentType;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.ui.ColoredTextContainer;
import com.intellij.ui.SimpleTextAttributes;
import com.intellij.util.containers.ContainerUtil;
import com.sentinel.nocalhost.configuration.go.dlv.DlvDebugProcess;
import com.sentinel.nocalhost.configuration.go.dlv.DlvSuspendContext;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import it.unimi.dsi.fastutil.ints.IntSet;
import it.unimi.dsi.fastutil.longs.Long2ObjectMap;
import it.unimi.dsi.fastutil.longs.Long2ObjectOpenHashMap;
import it.unimi.dsi.fastutil.longs.LongOpenHashSet;
import it.unimi.dsi.fastutil.longs.LongSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import kotlin.Lazy;
import kotlin.LazyKt;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import com.goide.dlv.dump.DlvFilteredThreads;

/* loaded from: DlvThreadDump.class */
public final class DlvThreadDump {
    private final String myUnknownFunctionName;
    private final DlvDebugProcess myProcess;
    private final String myId;

    @Nls
    private final String myThreadNamePrefix;
    private final DlvStackIndex myStackIndex;
    private final List<GoThread> myThreads;
    private final Long2ObjectMap<String> myDetails;
    private volatile boolean myIncomplete;
    private volatile boolean myMergeStacks;
    private volatile String myExpectedStackContent;
    private volatile LongSet myHiddenThreadIds;
    private volatile IntSet myHiddenStackIds;
    private final Object myHiddenSetsLock;
    private final Lazy<Integer> myUniqueStackCount;

    /* loaded from: DlvThreadDump$GoStackOwner.class */
    private interface GoStackOwner {
        int getStackId();
    }

    public DlvThreadDump(@NotNull DlvDebugProcess process, @NotNull String unknownFunctionName, @NotNull String dumpId, @Nls @NotNull String threadNamePrefix, @Nullable DlvThreadDump baseDump) {

        this.myUnknownFunctionName = unknownFunctionName;
        this.myId = dumpId;
        this.myThreadNamePrefix = threadNamePrefix;
        this.myProcess = process;
        this.myStackIndex = new DlvStackIndex(baseDump != null ? baseDump.myStackIndex : null);
        this.myThreads = new ArrayList<>();
        this.myDetails = new Long2ObjectOpenHashMap<>();
        this.myHiddenThreadIds = new LongOpenHashSet();
        this.myHiddenStackIds = new IntOpenHashSet();
        this.myHiddenSetsLock = new Object();
        this.myUniqueStackCount = LazyKt.lazy(() -> {
            IntOpenHashSet intOpenHashSet = new IntOpenHashSet();
            for (GoThread t : this.myThreads) {
                intOpenHashSet.add(t.getStackId());
            }
            return intOpenHashSet.size();
        });
    }

    @NotNull
    public String getId() {
        String str = this.myId;

        return str;
    }

    public void setMergeStacks(boolean mergeStacks) {
        this.myMergeStacks = mergeStacks;
    }

    public void setExpectedStackContent(@Nullable String s) {
        this.myExpectedStackContent = s;
    }

    public void setIncomplete() {
        this.myIncomplete = true;
    }

    public boolean isIncomplete() {
        return this.myIncomplete;
    }

    public int getThreadCount() {
        return this.myThreads.size();
    }

    public void sort() {
        this.myThreads.sort(Comparator.comparingLong(t -> t.myId));
    }

    public void resetHiddenThreads() {
        synchronized (this.myHiddenSetsLock) {
            this.myHiddenThreadIds = new LongOpenHashSet();
            this.myHiddenStackIds = new IntOpenHashSet();
        }
    }

    public void hideThread(@NotNull DumpedThread thread, boolean hideSimilarThreads) {

        if (hideSimilarThreads || (thread instanceof MergedStack)) {
            int stackId = ((GoStackOwner) thread).getStackId();
            synchronized (this.myHiddenSetsLock) {
                IntOpenHashSet intOpenHashSet = new IntOpenHashSet(this.myHiddenStackIds);
                intOpenHashSet.add(stackId);
                this.myHiddenStackIds = intOpenHashSet;
            }
            return;
        }
        long threadId = ((GoThread) thread).myId;
        synchronized (this.myHiddenSetsLock) {
            LongOpenHashSet longOpenHashSet = new LongOpenHashSet(this.myHiddenThreadIds);
            longOpenHashSet.add(threadId);
            this.myHiddenThreadIds = longOpenHashSet;
        }
    }

    public boolean hasHiddenThreads() {
        boolean z;
        synchronized (this.myHiddenSetsLock) {
            z = !this.myHiddenThreadIds.isEmpty() || !this.myHiddenStackIds.isEmpty();
        }
        return z;
    }

    private boolean isHidden(@NotNull GoThread thread, LongSet hiddenThreadIds, IntSet hiddenStackIds) {

        return hiddenThreadIds.contains(thread.myId) || hiddenStackIds.contains(thread.myStackId);
    }

    private void addThread(long threadId, @NotNull List<DlvApi.Location> stackFrames, int asyncFrameIndex, int topUserFrameIndex) {

        DlvStack stack = new DlvStack(this.myStackIndex, stackFrames, asyncFrameIndex);
        int stackId = this.myStackIndex.addStackIfAbsent(stack);
        this.myThreads.add(new GoThread(threadId, stackId, topUserFrameIndex));
    }

    public void addThread(@NotNull DlvApi.Goroutine goroutine, @NotNull List<DlvApi.Location> stackFrames) {

        int topUserFrameIndex = Math.max(0, stackFrames.indexOf(goroutine.userCurrentLoc));
        if (goroutine.goStatementLoc != null) {
            stackFrames.add(goroutine.goStatementLoc);
            addThread(goroutine.id, stackFrames, stackFrames.size() - 1, topUserFrameIndex);
        } else {
            addThread(goroutine.id, stackFrames, -1, topUserFrameIndex);
        }
        String details = DlvSuspendContext.getLabelsString(goroutine);
        if (details != null) {
            this.myDetails.put(goroutine.id, details);
        }
    }

    public int getSimilarThreadIndex(@NotNull List<? extends DumpedThread> threads, @Nullable DumpedThread thread) {

        if (!(thread instanceof GoStackOwner)) {
            return 0;
        }
        int stackId = ((GoStackOwner) thread).getStackId();
        int firstIndexWithSameStack = -1;
        for (int i = 0; i < threads.size(); i++) {
            DumpedThread t = threads.get(i);
            if (t == thread) {
                return i;
            }
            if (firstIndexWithSameStack == -1 && (t instanceof GoStackOwner) && ((GoStackOwner) t).getStackId() == stackId) {
                firstIndexWithSameStack = i;
            }
        }
        if (firstIndexWithSameStack != -1) {
            return firstIndexWithSameStack;
        }
        return 0;
    }

    @NotNull
    public DlvFilteredThreads getFilteredThreads(@NotNull ProgressIndicator indicator) {
        LongSet hiddenThreadIds;
        IntSet hiddenStackIds;

        synchronized (this.myHiddenSetsLock) {
            hiddenThreadIds = this.myHiddenThreadIds;
            hiddenStackIds = this.myHiddenStackIds;
        }
        List<DumpedThread> result = new ArrayList<>();
        List<DumpedThread> hiddenThreads = new ArrayList<>();
        if (this.myMergeStacks) {
            Int2ObjectOpenHashMap<MergedStack> int2ObjectOpenHashMap = new Int2ObjectOpenHashMap<>();
            result = new ArrayList<>();
            for (GoThread thread : this.myThreads) {
                if (isHidden(thread, hiddenThreadIds, hiddenStackIds)) {
                    hiddenThreads.add(thread);
                } else {
                    MergedStack mergedStack = int2ObjectOpenHashMap.get(thread.myStackId);
                    if (mergedStack == null) {
                        MergedStack mergedStack2 = new MergedStack(this, thread);
                        int2ObjectOpenHashMap.put(thread.myStackId, mergedStack2);
                        result.add(mergedStack2);
                    } else {
                        mergedStack.myCount++;
                    }
                }
            }
        } else if (hasHiddenThreads()) {
            for (GoThread thread2 : this.myThreads) {
                if (isHidden(thread2, hiddenThreadIds, hiddenStackIds)) {
                    hiddenThreads.add(thread2);
                } else {
                    result.add(thread2);
                }
            }
        } else {
            result = new ArrayList<>(this.myThreads);
        }
        int matchedByFilterCount = 0;
        boolean filterApplied = false;
        if (this.myExpectedStackContent != null) {
            filterApplied = true;
            List<DumpedThread> filtered = new ArrayList<>();
            boolean ignoreCase = this.myExpectedStackContent.equals(StringUtil.toLowerCase(this.myExpectedStackContent));
            DlvStack.ContentMatcher matcher = new DlvStack.ContentMatcher(this.myStackIndex, this.myUnknownFunctionName, this.myExpectedStackContent, ignoreCase);
            for (DumpedThread thread3 : result) {
                indicator.checkCanceled();
                String details = getDetails(thread3);
                if (details != null) {
                    if ((ignoreCase ? StringUtil.toLowerCase(details) : details).contains(this.myExpectedStackContent)) {
                        filtered.add(thread3);
                        matchedByFilterCount += thread3 instanceof MergedStack ? ((MergedStack) thread3).myCount : 1;
                    }
                }
                int stackId = ((GoStackOwner) thread3).getStackId();
                if (matcher.matches(stackId)) {
                    filtered.add(thread3);
                    matchedByFilterCount += thread3 instanceof MergedStack ? ((MergedStack) thread3).myCount : 1;
                }
            }
            result = filtered;
        }
        return new DlvFilteredThreads(result, hiddenThreads, getThreadCount(), this.myUniqueStackCount.getValue(), filterApplied, matchedByFilterCount);
    }

    @NotNull
    public DlvFilteredThreads getAllThreads() {
        return new DlvFilteredThreads(ContainerUtil.map(this.myThreads, it -> new GoThread(it.myId, it.getStackId(), it.myTopUserFrameIndex)), Collections.emptyList(), getThreadCount(), this.myUniqueStackCount.getValue(), false, getThreadCount());
    }

    @Nullable
    private String getDetails(DumpedThread thread) {
        if (thread instanceof GoThread) {
            return  this.myDetails.get(((GoThread) thread).myId);
        }
        if ((thread instanceof MergedStack) && ((MergedStack) thread).myCount == 1) {
            return  this.myDetails.get(((MergedStack) thread).myFirstThread.myId);
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* loaded from: DlvThreadDump$GoThread.class */
    public class GoThread implements DumpedThread, GoStackOwner {
        private final long myId;
        private final int myStackId;
        private final int myTopUserFrameIndex;

        private GoThread(long id, int stackId, int topUserFrameIndex) {
            this.myId = id;
            this.myStackId = stackId;
            this.myTopUserFrameIndex = topUserFrameIndex;
        }

        @Override // com.goide.dlv.dump.DlvThreadDump.GoStackOwner
        public int getStackId() {
            return this.myStackId;
        }

        @Override // com.goide.dlv.dump.DumpedThread
        public void print(@NotNull StringBuilder buffer) {
            buffer.append(DlvThreadDump.this.myThreadNamePrefix).append(" ").append(this.myId);
            String details = DlvThreadDump.this.myDetails.get(this.myId);
            if (details != null) {
                buffer.append(" ").append(details);
            }
            buffer.append("\n");
            DlvStack stack = (DlvStack) Objects.requireNonNull(DlvThreadDump.this.myStackIndex.getStack(this.myStackId));
            stack.print(DlvThreadDump.this.myStackIndex, DlvThreadDump.this.myUnknownFunctionName, buffer);
        }

        @Override // com.goide.dlv.dump.DumpedThread
        public void renderTitle(@NotNull ColoredTextContainer renderer, boolean selected, boolean hasFocus, boolean hidden) {
            DlvStack stack = Objects.requireNonNull(DlvThreadDump.this.myStackIndex.getStack(this.myStackId));
            DlvThreadDump.append(renderer, DlvThreadDump.this.myThreadNamePrefix, hidden);
            DlvThreadDump.append(renderer, " ", hidden);
            DlvThreadDump.append(renderer, String.valueOf(this.myId), hidden);
            DlvThreadDump.append(renderer, " ", hidden);
            String details = DlvThreadDump.this.myDetails.get(this.myId);
            if (details != null) {
                DlvThreadDump.append(renderer, " ", hidden);
                DlvThreadDump.append(renderer, details, hidden);
                DlvThreadDump.append(renderer, ", ", hidden);
            }
            stack.renderFrame(DlvThreadDump.this.myStackIndex, renderer, this.myTopUserFrameIndex, DlvThreadDump.this.myUnknownFunctionName, hidden);
        }

        @Override // com.goide.dlv.dump.DumpedThread
        public void renderStack(@NotNull ConsoleView console) {
            String details = DlvThreadDump.this.myDetails.get(this.myId);
            if (details != null) {
                console.print(details, ConsoleViewContentType.LOG_VERBOSE_OUTPUT);
                console.print("\n", ConsoleViewContentType.NORMAL_OUTPUT);
            }
            DlvStack stack = Objects.requireNonNull(DlvThreadDump.this.myStackIndex.getStack(this.myStackId));
            stack.renderStack(DlvThreadDump.this.myProcess, DlvThreadDump.this.myStackIndex, console, DlvThreadDump.this.myUnknownFunctionName);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* loaded from: DlvThreadDump$MergedStack.class */
    public class MergedStack implements DumpedThread, GoStackOwner {
        private final GoThread myFirstThread;
        private int myCount;
        final /* synthetic */ DlvThreadDump this$0;

        private MergedStack(@NotNull DlvThreadDump dlvThreadDump, GoThread firstItem) {
            this.this$0 = dlvThreadDump;
            this.myFirstThread = firstItem;
            this.myCount = 1;
        }

        @Override // com.goide.dlv.dump.DumpedThread
        public boolean isMergesSeveralThreads() {
            return this.myCount > 1;
        }

        @Override // com.goide.dlv.dump.DlvThreadDump.GoStackOwner
        public int getStackId() {
            return this.myFirstThread.myStackId;
        }

        @Override // com.goide.dlv.dump.DumpedThread
        public void renderTitle(@NotNull ColoredTextContainer renderer, boolean selected, boolean hasFocus, boolean hidden) {
            if (this.myCount == 1) {
                this.myFirstThread.renderTitle(renderer, selected, hasFocus, hidden);
                return;
            }
            DlvStack stack = Objects.requireNonNull(this.this$0.myStackIndex.getStack(this.myFirstThread.myStackId));
            stack.renderFrame(this.this$0.myStackIndex, renderer, this.myFirstThread.myTopUserFrameIndex, this.this$0.myUnknownFunctionName, hidden);
            DlvThreadDump.append(renderer, " (" + this.myCount + ")", hidden);
        }

        @Override // com.goide.dlv.dump.DumpedThread
        public void renderStack(@NotNull ConsoleView console) {
            if (this.myCount == 1) {
                this.myFirstThread.renderStack(console);
            } else {
                DlvStack stack = Objects.requireNonNull(this.this$0.myStackIndex.getStack(this.myFirstThread.myStackId));
                stack.renderStack(this.this$0.myProcess, this.this$0.myStackIndex, console, this.this$0.myUnknownFunctionName);
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    public static void append(@NotNull ColoredTextContainer container, @Nls @NotNull String text, boolean hidden) {

        container.append(text, hidden ? SimpleTextAttributes.GRAYED_ATTRIBUTES : SimpleTextAttributes.REGULAR_ATTRIBUTES);
    }

    @NotNull
    public UiFilterSettings getUiFilterSettings() {
        UiFilterSettings uiFilterSettings;
        synchronized (this.myHiddenSetsLock) {
            uiFilterSettings = new UiFilterSettings(this.myMergeStacks, this.myExpectedStackContent, new LongOpenHashSet(this.myHiddenThreadIds), new IntOpenHashSet(this.myHiddenStackIds));
        }

        return uiFilterSettings;
    }

    public void applyFilter(@NotNull UiFilterSettings filter) {

        synchronized (this.myHiddenSetsLock) {
            this.myMergeStacks = filter.myMergeStacks;
            this.myExpectedStackContent = filter.myExpectedStackContent;
            this.myHiddenThreadIds = new LongOpenHashSet(filter.myHiddenThreadIds);
            this.myHiddenStackIds = new IntOpenHashSet(filter.myHiddenStackIds);
        }
    }

    /* loaded from: DlvThreadDump$UiFilterSettings.class */
    public static final class UiFilterSettings {
        private final boolean myMergeStacks;
        private final String myExpectedStackContent;
        private final LongSet myHiddenThreadIds;
        private final IntSet myHiddenStackIds;

        UiFilterSettings(boolean mergeStacks, @Nullable String expectedStackContent, @NotNull LongSet hiddenThreadIds, @NotNull IntSet hiddenStackIds) {
            this.myMergeStacks = mergeStacks;
            this.myExpectedStackContent = expectedStackContent;
            this.myHiddenThreadIds = hiddenThreadIds;
            this.myHiddenStackIds = hiddenStackIds;
        }

        public boolean isMergeStacks() {
            return this.myMergeStacks;
        }

        @Nullable
        public String getExpectedStackContent() {
            return this.myExpectedStackContent;
        }
    }
}
