package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.dlv.DlvStackFuncName;
import com.goide.i18n.GoBundle;
import com.intellij.icons.AllIcons;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.util.NlsSafe;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.util.ObjectUtils;
import com.intellij.util.concurrency.annotations.RequiresBackgroundThread;
import com.intellij.util.containers.ContainerUtil;
import com.intellij.xdebugger.frame.XExecutionStack;
import com.intellij.xdebugger.frame.XStackFrame;
import com.intellij.xdebugger.frame.XSuspendContext;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.PromiseManager;
import org.jetbrains.concurrency.Promises;

public class DlvSuspendContext extends XSuspendContext {
    private final @NotNull DlvDebugProcess myProcess;
    private final @Nullable DlvSuspendContext.DlvExecutionStack myActiveStack;
    private final boolean myShowThreads;

    private DlvSuspendContext(@NotNull DlvDebugProcess process, @Nullable DlvSuspendContext.DlvExecutionStack activeStack, boolean showThreads) {
        super();
        this.myProcess = process;
        this.myActiveStack = activeStack;
        this.myShowThreads = showThreads;
    }

    @RequiresBackgroundThread
    static @NotNull Promise<DlvSuspendContext> create(@NotNull DlvDebugProcess process, @Nullable DlvApi.@Nullable Thread currentThread, @Nullable String err) {


        ApplicationManager.getApplication().assertIsNonDispatchThread();
        DlvSuspendContext.DlvExecutionStack activeStack = currentThread != null ? new DlvSuspendContext.DlvThreadExecutionStack(process, currentThread, threadLocation(currentThread), true, err) : null;


        return Promises.resolvedPromise(new DlvSuspendContext(process, activeStack, true));
    }

    @RequiresBackgroundThread
    static @NotNull Promise<DlvSuspendContext> create(@NotNull DlvDebugProcess process, @Nullable DlvApi.@Nullable Goroutine currentGoroutine, @Nullable String err) {


        ApplicationManager.getApplication().assertIsNonDispatchThread();
        DlvSuspendContext.DlvExecutionStack activeStack = currentGoroutine != null ? new DlvSuspendContext.DlvGoroutineExecutionStack(process, currentGoroutine, true, err) : null;

        return Promises.resolvedPromise(new DlvSuspendContext(process, activeStack, false));
    }

    public @Nullable XExecutionStack getActiveExecutionStack() {
        return this.myActiveStack;
    }

    public void computeExecutionStacks(XSuspendContext.XExecutionStackContainer container) {
        if (this.myShowThreads) {
            this.myProcess.send(new DlvRequest.State(true)).thenAsync((state) -> this.handleThreads(state, container));
        } else {
            List<DlvSuspendContext.DlvExecutionStack> result = new ArrayList<>();
            this.myProcess.send(new DlvRequest.ListGoroutines(0, getGoroutinePackSize())).thenAsync((out) -> this.handleGoroutines(out, result, container));
        }

    }

    private @NotNull Promise<?> handleThreads(@NotNull DlvApi.@NotNull DebuggerState state, @NotNull XSuspendContext.@NotNull XExecutionStackContainer container) {


        Promise<?> var10000;
        if (!container.isObsolete()) {
            List<DlvExecutionStack> stacks = ContainerUtil.map(state.threads, (it) -> this.myActiveStack != null && it.id == this.myActiveStack.myId ? this.myActiveStack : new DlvThreadExecutionStack(this.myProcess, it, threadLocation(it), false, (String) null));
            container.addExecutionStack(ContainerUtil.sorted(stacks, Comparator.comparingLong(DlvExecutionStack::getId)), true);

        }
        var10000 = Promises.resolvedPromise();
        return var10000;
    }

    private @NotNull Promise<?> handleGoroutines(@NotNull DlvApi.@NotNull ListGoroutinesOut goroutinesOut, @NotNull List<DlvSuspendContext.DlvExecutionStack> result, @NotNull XSuspendContext.@NotNull XExecutionStackContainer container) {


        Promise<?> var10000;
        if (container.isObsolete()) {
            var10000 = Promises.resolvedPromise();

            return var10000;
        } else {
            result.addAll(ContainerUtil.map(goroutinesOut.Goroutines, (it) -> this.myActiveStack != null && it.id == this.myActiveStack.myId ? this.myActiveStack : new DlvGoroutineExecutionStack(this.myProcess, it, false, (String)null)));
            if (goroutinesOut.Nextg == -1) {
                container.addExecutionStack(ContainerUtil.sorted(result, Comparator.comparingLong(DlvSuspendContext.DlvExecutionStack::getId)), true);
                var10000 = Promises.resolvedPromise();

                return var10000;
            } else if (goroutinesOut.Nextg > JetDevSettings.getInstance().goroutinesLimit) {
                container.addExecutionStack(ContainerUtil.sorted(result, Comparator.comparingLong(DlvSuspendContext.DlvExecutionStack::getId)), true);
                String message = GoBundle.message("go.debugger.only.first.goroutines.are.shown", JetDevSettings.getInstance().goroutinesLimit);
                DlvDebugProcess.showNotification(this.myProcess.getSession().getProject(), NotificationType.WARNING, message);
                var10000 = Promises.resolvedPromise();


                return var10000;
            } else {
                var10000 = this.myProcess.send(new DlvRequest.ListGoroutines(goroutinesOut.Nextg, getGoroutinePackSize())).thenAsync((out) -> this.handleGoroutines(out, result, container));

                return var10000;
            }
        }
    }

    private static int getGoroutinePackSize() {
        return Math.min(10000, JetDevSettings.getInstance().goroutinesLimit);
    }

    public static @Nullable @NlsSafe String getLabelsString(@NotNull DlvApi.@NotNull Goroutine goroutine) {

        Map<String, String> labels = goroutine.labels;
        return labels != null && !labels.isEmpty() ? StringUtil.join(ContainerUtil.map(labels.entrySet(), (it) -> {
            String var10000 = it.getKey();
            return var10000 + ": " + it.getValue();
        }), ", ") : null;
    }

    @NotNull
    private static DlvApi.@NotNull Location threadLocation(@NotNull DlvApi.@NotNull Thread thread) {


        return new DlvApi.Location(thread.pc, thread.file, thread.line, thread.function);
    }

    static @NotNull DlvStackFuncName getStackFuncName(@NotNull DlvDebugProcess process, @NotNull DlvApi.@NotNull Location loc) {


        DlvApi.Function function = loc.function;
        String dlvFuncName = function != null ? function.name : null;
        if (dlvFuncName == null) {
            return new DlvStackFuncName(loc.name(), "");
        } else {


            return ReadAction.compute(() -> process.getSymbolPrettier().prettifyFunc(dlvFuncName));
        }
    }

    public abstract static class DlvExecutionStack extends XExecutionStack {
        private final DlvDebugProcess myProcess;
        private final boolean myIsCurrent;
        private final DlvStackFrame myTopFrame;
        private final DlvApi.Location myGoStatementLocation;
        private final long myStackGoroutineId;
        private final long myId;
        private final String myErr;
        volatile Promise<List<XStackFrame>> cachedFrames;
        private static final PromiseManager<DlvSuspendContext.DlvExecutionStack, List<XStackFrame>> CALL_FRAMES_LOADER = new PromiseManager<>(DlvSuspendContext.DlvExecutionStack.class) {
            public @NotNull Promise<List<XStackFrame>> load(@NotNull DlvSuspendContext.DlvExecutionStack stack) {
                return stack.myProcess.getStacktrace(stack.myStackGoroutineId).then((frames) -> {
                    List<XStackFrame> result;
                    if (frames == null) {
                        result = new ArrayList<>(1);
                        result.add(stack.myTopFrame);
                    } else {
                        result = frames;
                        DlvStackFrame topFrame = ObjectUtils.tryCast(ContainerUtil.getFirstItem(frames), DlvStackFrame.class);
                        boolean reuseTopFrame = topFrame != null && topFrame.getGoroutineId() == stack.myTopFrame.getGoroutineId()
                                && topFrame.getFrameId() == stack.myTopFrame.getFrameId() && topFrame.getLocation().equals(stack.myTopFrame.getLocation());
                        if (reuseTopFrame) {
                            frames.set(0, stack.myTopFrame);
                        } else if (topFrame != null && !topFrame.hasErr() && stack.myErr != null) {
                            topFrame.setErr(stack.myErr);
                        }
                    }

                    DlvApi.Location goStatementLocation = stack.myGoStatementLocation;
                    if (goStatementLocation != null) {
                        DlvStackFuncName stackFuncName = DlvSuspendContext.getStackFuncName(stack.myProcess, goStatementLocation);
                        result.add(new DlvStackFrame(stack.myProcess, goStatementLocation, stackFuncName, stack.myStackGoroutineId, result.size(), null, true));
                    }

                    return result;
                });
            }
        };

        protected DlvExecutionStack(@NotNull DlvDebugProcess process, @NotNull @Nls String name, @Nullable @Nls String details, long id, long stackGoroutineId, boolean isCurrent, @NotNull DlvApi.@NotNull Location topFrameLocation, @NotNull DlvStackFuncName topFuncName, @Nullable DlvApi.@Nullable Location goStatementLocation, @Nullable String err) {


            super(name + " " + id + (details != null ? " " + details + ", " : " ") + topFuncName.getFuncName(), isCurrent ? AllIcons.Debugger.ThreadCurrent : AllIcons.Debugger.ThreadSuspended);
            this.myProcess = process;
            this.myIsCurrent = isCurrent;
            this.myStackGoroutineId = stackGoroutineId;
            this.myTopFrame = new DlvStackFrame(this.myProcess, topFrameLocation, topFuncName, this.myStackGoroutineId, 0, err, false);
            this.myGoStatementLocation = goStatementLocation;
            this.myId = id;
            this.myErr = err;
        }

        public @Nullable XStackFrame getTopFrame() {
            return this.myTopFrame;
        }

        public void computeStackFrames(int firstFrameIndex, @NotNull XExecutionStack.@NotNull XStackFrameContainer container) {

            if (!container.isObsolete()) {
                DlvDebugProcess.runInBackground(() -> {
                    CALL_FRAMES_LOADER.get(this).onSuccess((frames) -> {
                        if (!container.isObsolete()) {
                            container.addStackFrames(frames, true);
                        }

                    });
                });
            }
        }

        public long getId() {
            return this.myId;
        }

        @NotNull
        abstract DlvRequest.@NotNull DlvObjRequest<DlvApi.DebuggerState> createSwitchCommand();

        boolean isCurrent() {
            return this.myIsCurrent;
        }
    }

    private static final class DlvThreadExecutionStack extends DlvSuspendContext.DlvExecutionStack {

        private DlvThreadExecutionStack(@NotNull DlvDebugProcess process, @NotNull DlvApi.@NotNull Thread thread, @NotNull DlvApi.@NotNull Location threadLocation, boolean isCurrent, @Nullable String err) {


            super(process, GoBundle.message("go.debugger.thread"), null, thread.id, -1L, isCurrent, threadLocation, DlvSuspendContext.getStackFuncName(process, DlvSuspendContext.threadLocation(thread)), (DlvApi.Location)null, err);
        }

        @NotNull
        DlvRequest.@NotNull DlvObjRequest<DlvApi.DebuggerState> createSwitchCommand() {
            return new DlvRequest.SwitchThreadCommand((int)this.getId());
        }
    }

    private static final class DlvGoroutineExecutionStack extends DlvSuspendContext.DlvExecutionStack {
        private DlvGoroutineExecutionStack(@NotNull DlvDebugProcess process, @NotNull DlvApi.@NotNull Goroutine goroutine, boolean isCurrent, @Nullable String err) {

            super(process, GoBundle.message("go.debugger.goroutine"), DlvSuspendContext.getLabelsString(goroutine), goroutine.id, goroutine.id, isCurrent, goroutine.currentLoc, DlvSuspendContext.getStackFuncName(process, goroutine.currentLoc), goroutine.goStatementLoc, err);
        }

        @NotNull
        DlvRequest.@NotNull DlvObjRequest<DlvApi.DebuggerState> createSwitchCommand() {
            return new DlvRequest.SwitchGoroutineCommand(this.getId());
        }
    }
}
