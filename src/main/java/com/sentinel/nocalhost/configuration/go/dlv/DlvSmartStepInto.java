package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.i18n.GoBundle;
import com.intellij.openapi.util.Pair;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.util.containers.ContainerUtil;
import java.math.BigInteger;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvRequest;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;

/* JADX INFO: Access modifiers changed from: package-private */
/* loaded from: DlvSmartStepInto.class */
public class DlvSmartStepInto {
    private final DlvDebugProcess myProcess;


    /* JADX INFO: Access modifiers changed from: package-private */
    public DlvSmartStepInto(@NotNull DlvDebugProcess process) {

        this.myProcess = process;
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public Promise<Void> smartStepIntoFunctionValue(long goroutineId, int maxLine, @NotNull String functionExpression) {

        return this.myProcess.send(new DlvRequest.Eval(functionExpression, 0, goroutineId)).thenAsync(var -> {
            if (!var.isFunction()) {
                return this.myProcess.consumeStateWithError(GoBundle.message("go.debugger.smart.step.into.failed", new Object[]{GoBundle.message("go.debugger.expression.is.not.a.function", new Object[]{functionExpression})}));
            }
            BigInteger base = var.base;
            if (isNilAddress(base)) {
                return this.myProcess.consumeStateWithError(GoBundle.message("go.debugger.smart.step.into.failed", new Object[]{GoBundle.message("go.debugger.expression.is.nil", new Object[]{functionExpression})}));
            }
            return smartStepInto(goroutineId, maxLine, Collections.emptySet(), base);
        });
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public Promise<Void> smartStepIntoFunction(long goroutineId, int maxLine, @NotNull String packagePath, @NotNull String functionName) {

        return smartStepInto(goroutineId, maxLine, Set.of(getLocation(packagePath, null, functionName)), null);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public Promise<Void> smartStepIntoMethod(long goroutineId, int maxLine, @NotNull String packagePath, @NotNull String receiverType, @NotNull String functionName) {

        return smartStepInto(goroutineId, maxLine, Set.of(getLocation(packagePath, receiverType, functionName)), null);
    }

    /* JADX INFO: Access modifiers changed from: package-private */
    @NotNull
    public Promise<Void> smartStepIntoInterfaceMethod(long goroutineId, int maxLine, @NotNull Set<String> interfaceValues, @NotNull String functionName) {

        return eval(goroutineId, interfaceValues).thenAsync(vars -> {
            List<Pair<String, String>> types = vars.stream().map(DlvSmartStepInto::getImplementationType).filter(Objects::nonNull).toList();
            if (types.size() != interfaceValues.size()) {
                return this.myProcess.consumeStateWithError(GoBundle.message("go.debugger.smart.step.into.failed", GoBundle.message("go.debugger.cannot.retrieve.interface.value.type", new Object[0])));
            }
            Set<String> locations = types.stream().map(it -> getLocation(it.first,  it.second, functionName)).collect(Collectors.toSet());
            return smartStepInto(goroutineId, maxLine, locations, null);
        });
    }

    private static boolean isNilAddress(@Nullable BigInteger address) {
        return address == null || address.equals(BigInteger.ZERO);
    }

    @Nullable
    private static Pair<String, String> getImplementationType(@NotNull DlvApi.Variable var) {
        if (var.isInterface() && var.children.length > 0 && "data".equals(var.children[0].name)) {
            DlvApi.Variable data = var.children[0];
            String type = data.type;
            int packageEnd = type.lastIndexOf(46);
            String implementationPackage = StringUtil.trimStart(type.substring(0, packageEnd), "*");
            String implementationType = type.substring(packageEnd + 1);
            if (type.startsWith("*")) {
                implementationType = "*" + implementationType;
            }
            return Pair.create(implementationPackage, implementationType);
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    @NotNull
    public static String getLocation(@NotNull String packagePath, @Nullable String receiverType, @NotNull String functionName) {
        String str;
        if (receiverType != null) {
            if (receiverType.startsWith("*")) {
                str = packagePath + ".(" + receiverType + ")." + functionName;
            } else {
                str = packagePath + "." + receiverType + "." + functionName;
            }
            return str;
        }
        return packagePath + "." + functionName;
    }

    @NotNull
    private Promise<List<DlvApi.Variable>> eval(long goroutineId, @NotNull Collection<String> expressions) {
        return Promises.collectResults(ContainerUtil.map(expressions, it -> this.myProcess.send(new DlvRequest.Eval(it, 0, goroutineId))));
    }

    @NotNull
    private Promise<Void> smartStepInto(long goroutineId, int maxLine, @NotNull Set<String> locations, @Nullable BigInteger entryPointAddress) {
        return this.myProcess.send(new DlvRequest.Stacktrace((int) goroutineId)).thenAsync(initialStack -> {
            DlvApi.Location topEntry = ContainerUtil.getFirstItem(initialStack);
            if (topEntry == null) {
                return this.myProcess.consumeStateWithError(GoBundle.message("go.debugger.smart.step.into.failed", GoBundle.message("go.debugger.cannot.get.current.stack")));
            }
            SmartStepIntoContext context = new SmartStepIntoContext(this, goroutineId, initialStack, topEntry, locations, entryPointAddress, maxLine);
            return this.myProcess.step().thenAsync(s -> handleStepIn(s, context));
        });
    }

    @NotNull
    private Promise<Void> handleStepIn(@NotNull DlvApi.DebuggerState state, @NotNull SmartStepIntoContext context) {

        return context.isSteppedIn(state).thenAsync(steppedIn -> handleStepIn(state, context, steppedIn));
    }

    @NotNull
    private Promise<Void> handleStepIn(@NotNull DlvApi.DebuggerState inState, @NotNull SmartStepIntoContext context, boolean steppedIn) {
        if (steppedIn) {
            return context.isInsideSelectedCall(inState).thenAsync(isInside -> isInside ? finishStepInto(inState) : stepOutAndRepeat(context));
        }
        return tryStepIn(inState, context);
    }

    @NotNull
    private Promise<Void> tryStepIn(@NotNull DlvApi.DebuggerState state, @NotNull SmartStepIntoContext context) {
        if (context.canStepIn(state)) {
            return this.myProcess.step().thenAsync(inState -> handleStepIn(inState, context));
        }
        return finishStepInto(state);
    }

    @NotNull
    private Promise<Void> stepOutAndRepeat(@NotNull SmartStepIntoContext context) {
        return this.myProcess.send(new DlvRequest.Command(DlvApi.STEP_OUT)).thenAsync(outState -> tryStepIn(outState, context));
    }

    @NotNull
    private Promise<Void> finishStepInto(@NotNull DlvApi.DebuggerState s) {

        return this.myProcess.consumeDebuggerState(s);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* loaded from: DlvSmartStepInto$SmartStepIntoContext.class */
    public static final class SmartStepIntoContext {
        private final long myGoroutineId;
        private final List<DlvApi.Location> myInitialStack;
        private final DlvApi.Location myTopStackEntry;
        private final Set<String> myDesiredLocations;
        private final BigInteger myEntryPointAddress;
        private final int myMaxLine;
        private final DlvSmartStepInto dlvSmartStepInto;

        private SmartStepIntoContext(DlvSmartStepInto dlvSmartStepInto, long goroutineId, @NotNull List<DlvApi.Location> initialStack, @NotNull DlvApi.Location topStackEntry, @Nullable Set<String> desiredLocations, BigInteger entryPointAddress, int zeroBasedMaxLine) {

            this.dlvSmartStepInto = dlvSmartStepInto;
            this.myGoroutineId = goroutineId;
            this.myInitialStack = initialStack;
            this.myTopStackEntry = topStackEntry;
            this.myDesiredLocations = desiredLocations;
            this.myEntryPointAddress = entryPointAddress;
            this.myMaxLine = zeroBasedMaxLine + 1;
        }

        @NotNull
        Promise<Boolean> isSteppedIn(@NotNull DlvApi.DebuggerState s) {

            String currentName = DlvUtil.currentFunctionName(s);
            return !this.myTopStackEntry.name().equals(currentName) ? Promises.resolvedPromise(true) : stackHasGrown();
        }

        boolean canStepIn(@NotNull DlvApi.DebuggerState s) {

            String currentName = DlvUtil.currentFunctionName(s);
            DlvApi.Goroutine goroutine = s.currentGoroutine;
            int line = goroutine != null ? goroutine.currentLoc.line : Integer.MAX_VALUE;
            return this.myTopStackEntry.name().equals(currentName) && line < this.myMaxLine;
        }

        @NotNull
        Promise<Boolean> isInsideSelectedCall(@NotNull DlvApi.DebuggerState s) {

            if (this.myEntryPointAddress == null) {
                assert this.myDesiredLocations != null;
                return !this.myDesiredLocations.contains(DlvUtil.currentFunctionName(s)) ? Promises.resolvedPromise(false) : stackHasGrown();
            }

            return this.dlvSmartStepInto.myProcess.send(new DlvRequest.Stacktrace((int) this.myGoroutineId)).thenAsync(stack -> {
                boolean stackGrown = stack.size() > this.myInitialStack.size();
                if (stackGrown) {
                    boolean topFrameChecked = false;
                    for (DlvApi.Location frame : stack) {
                        if (topFrameChecked && !DlvUtil.isAutogenerated(frame)) {
                            break;
                        }
                        DlvApi.Function func = frame.function;
                        if (func != null && this.myEntryPointAddress.equals(func.value)) {
                            return Promises.resolvedPromise(true);
                        }
                        topFrameChecked = true;
                    }
                }
                return Promises.resolvedPromise(false);
            });
        }

        @NotNull
        private Promise<Boolean> stackHasGrown() {
            return this.dlvSmartStepInto.myProcess.send(new DlvRequest.Stacktrace((int) this.myGoroutineId)).thenAsync(stack -> Promises.resolvedPromise(stack.size() > this.myInitialStack.size()));
        }
    }
}
