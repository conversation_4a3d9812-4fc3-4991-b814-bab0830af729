package com.sentinel.nocalhost.configuration.go.dlv;

import com.goide.i18n.GoBundle;
import com.goide.inspections.fmtstring.parser.GoFmtStringParser;
import com.goide.inspections.fmtstring.parser.tokens.GoFmtStringBadPlaceholder;
import com.goide.inspections.fmtstring.parser.tokens.GoFmtStringEscapedPercent;
import com.goide.inspections.fmtstring.parser.tokens.GoFmtStringPlaceholder;
import com.goide.inspections.fmtstring.parser.tokens.GoFmtStringTextParsed;
import com.goide.inspections.fmtstring.parser.tokens.GoFmtStringToken;
import com.goide.util.Value;
import com.intellij.openapi.util.text.StringUtil;
import com.intellij.util.ObjectUtils;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

import com.sentinel.nocalhost.configuration.go.dlv.protocol.DlvApi;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jetbrains.concurrency.Promise;
import org.jetbrains.concurrency.Promises;

class GoFmtSprintfEvaluator {
    private final String myFormat;
    private final List<GoFmtSprintfArgument> myArgs;
    private final boolean myPrintDiagnostics;

    GoFmtSprintfEvaluator(@NotNull String format, @NotNull List<GoFmtSprintfArgument> args, boolean printDiagnostics) {
        super();
        this.myFormat = format;
        this.myArgs = args;
        this.myPrintDiagnostics = printDiagnostics;
    }

    @NotNull Promise<@Nullable String> eval(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver) {


        List<GoFmtStringToken> tokens = GoFmtStringParser.parse(this.myFormat, true);
        List<Promise<Optional<String>>> promises = new ArrayList<>();
        int index = 0;
        Iterator<GoFmtStringToken> var6 = tokens.iterator();

        while(true) {
            while(var6.hasNext()) {
                GoFmtStringToken token = var6.next();
                if (token instanceof GoFmtStringEscapedPercent) {
                    promises.add(Promises.resolvedPromise(Optional.of("%")));
                } else if (!(token instanceof GoFmtStringTextParsed) && !(token instanceof GoFmtStringBadPlaceholder)) {
                    if (token instanceof GoFmtStringPlaceholder placeholder) {
                        Promise<String> formattedPromise = Promises.resolvedPromise(null);
                        char verb = placeholder.getVerb();
                        boolean supportedVerb = true;
                        if (placeholder.getWidth() == null && placeholder.getPrecision() == null && placeholder.getVerbIndex() == null) {
                            switch (verb) {
                                case 'F':
                                case 'f':
                                    formattedPromise = this.formatFloat(dlvXValue, receiver, index);
                                    break;
                                case 'X':
                                case 'x':
                                    formattedPromise = this.formatHex(dlvXValue, receiver, index, verb == 'X');
                                    break;
                                case 'b':
                                    formattedPromise = this.formatBinary(dlvXValue, receiver, index);
                                    break;
                                case 'd':
                                    formattedPromise = this.formatDecimal(dlvXValue, receiver, index);
                                    break;
                                case 'p':
                                    formattedPromise = this.formatPointer(receiver, index);
                                    break;
                                case 'q':
                                    formattedPromise = this.formatQuotedString(dlvXValue, receiver, index);
                                    break;
                                case 's':
                                    formattedPromise = this.formatString(dlvXValue, receiver, index);
                                    break;
                                case 't':
                                    formattedPromise = this.formatBool(dlvXValue, receiver, index);
                                    break;
                                default:
                                    supportedVerb = false;
                            }
                        }

                        boolean finalSupportedVerb = supportedVerb;
                        promises.add(formattedPromise.then((formatted) -> {
                            if (formatted != null) {
                                return Optional.of(formatted);
                            } else if (this.myPrintDiagnostics) {
                                String var10000;
                                if (placeholder.getWidth() == null && placeholder.getPrecision() == null && placeholder.getVerbIndex() == null) {
                                    if (!finalSupportedVerb) {
                                        var10000 = placeholder.getText();
                                        return Optional.of("%!" + var10000 + GoBundle.message("go.debugger.fmt.sprintf.unsupported.verb", new Object[0]));
                                    } else {
                                        return Optional.of("%!" + verb + GoBundle.message("go.debugger.fmt.sprintf.cannot.format.value", new Object[0]));
                                    }
                                } else {
                                    var10000 = placeholder.getText();
                                    return Optional.of("%!" + var10000 + GoBundle.message("go.debugger.fmt.sprintf.unsupported.format"));
                                }
                            } else {
                                return Optional.empty();
                            }
                        }));
                        ++index;
                    }
                } else {
                    promises.add(Promises.resolvedPromise(Optional.of(token.getText())));
                }
            }

            return Promises.collectResults(promises).then((values) -> {
                StringBuilder result = new StringBuilder();

                for (Optional<String> s : values) {
                    if (s.isEmpty()) {
                        return null;
                    }

                    result.append(s.get());
                }

                return result.toString();
            });
        }
    }

    private @NotNull Promise<String> formatFloat(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver, int index) {


        return this.getValue(dlvXValue, receiver, index).then((value) -> {
            Double d = value != null ? value.getDouble() : null;
            return d != null ? String.format("%f", d) : null;
        });
    }

    private @NotNull Promise<String> formatQuotedString(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver, int index) {


        return this.getValue(dlvXValue, receiver, index).then((value) -> {
            if (value == null) {
                return null;
            } else {
                String s = value.getString();
                if (s != null) {
                    return "\"" + StringUtil.escapeStringCharacters(s) + "\"";
                } else {
                    byte[] bytes = value.getBytes();
                    return bytes != null ? "\"" + StringUtil.escapeStringCharacters(new String(bytes, StandardCharsets.UTF_8)) + "\"" : null;
                }
            }
        });
    }

    private @NotNull Promise<String> formatString(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver, int index) {


        return this.getValue(dlvXValue, receiver, index).then((value) -> {
            if (value == null) {
                return null;
            } else {
                String s = value.getString();
                if (s != null) {
                    return s;
                } else {
                    byte[] bytes = value.getBytes();
                    return bytes != null ? new String(bytes, StandardCharsets.UTF_8) : null;
                }
            }
        });
    }

    private @NotNull Promise<String> formatPointer(@NotNull DlvApi.@NotNull Variable receiver, int index) {


        GoFmtSprintfArgumentDynamic arg = ObjectUtils.tryCast(this.getArgument(index), GoFmtSprintfArgumentDynamic.class);
        Promise<String> var10000;
        if (arg != null) {
            BigInteger address = arg.getValueAddress(receiver);
            var10000 = Promises.resolvedPromise(address != null ? "0x" + address.toString(16) : null);

        } else {
            var10000 = Promises.resolvedPromise(null);

        }
        return var10000;
    }

    private @NotNull Promise<String> formatHex(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver, int index, boolean upperCase) {


        return this.getValue(dlvXValue, receiver, index).then((value) -> {
            if (value == null) {
                return null;
            } else {
                Long val = value.getInteger();
                if (val != null) {
                    return formatHex(val, 0, upperCase);
                } else {
                    byte[] bytes = value.getBytes();
                    if (bytes == null) {
                        String valStr = value.getString();
                        return valStr != null ? formatCharsAsHex(valStr, upperCase) : null;
                    } else {
                        StringBuilder result = new StringBuilder();

                        for (byte b : bytes) {
                            result.append(formatHex((long) Byte.toUnsignedInt(b), 2, upperCase));
                        }

                        return result.toString();
                    }
                }
            }
        });
    }

    private static @NotNull String formatCharsAsHex(@NotNull String s, boolean upperCase) {

        StringBuilder result = new StringBuilder();
        String format = upperCase ? "%02X" : "%02x";

        for(int i = 0; i < s.length(); ++i) {
            result.append(String.format(format, (int) s.charAt(i)));
        }

        return result.toString();
    }

    private static @Nullable String formatHex(@Nullable Long val, int zeroPaddingWidth, boolean upperCase) {
        if (val == null) {
            return null;
        } else {
            String padding = zeroPaddingWidth > 0 ? "0" + zeroPaddingWidth : "";
            String conversion = upperCase ? "X" : "x";
            String format = "%" + padding + conversion;
            return val < 0L ? "-" + String.format(format, Math.abs(val)) : String.format(format, val);
        }
    }

    private @NotNull Promise<String> formatDecimal(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver, int index) {


        return this.getValue(dlvXValue, receiver, index).then((value) -> formatDecimal(value != null ? value.getInteger() : null));
    }

    private static @Nullable String formatDecimal(@Nullable Long val) {
        return val != null ? String.format("%d", val) : null;
    }

    private @NotNull Promise<String> formatBinary(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver, int index) {


        return this.getValue(dlvXValue, receiver, index).then((value) -> formatBinary(value != null ? value.getInteger() : null));
    }

    private static @Nullable String formatBinary(@Nullable Long val) {
        if (val == null) {
            return null;
        } else {
            return val < 0L ? "-" + Long.toBinaryString(Math.abs(val)) : Long.toBinaryString(val);
        }
    }

    private @NotNull Promise<String> formatBool(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver, int index) {


        return this.getValue(dlvXValue, receiver, index).then((value) -> {
            Boolean bool = value != null ? value.getBool() : null;
            return bool == null ? null : bool.toString();
        });
    }

    private @NotNull Promise<Value> getValue(@NotNull DlvXValue dlvXValue, @NotNull DlvApi.@NotNull Variable receiver, int index) {

        GoFmtSprintfArgument arg = this.getArgument(index);
        return arg != null ? arg.getValue(dlvXValue, receiver) : Promises.resolvedPromise(null);
    }

    private @Nullable GoFmtSprintfArgument getArgument(int index) {
        return index < this.myArgs.size() ? this.myArgs.get(index) : null;
    }

    int getMaxDynamicArgumentAccessorsDepth() {
        int result = 0;

        for (GoFmtSprintfArgument arg : this.myArgs) {
            if (arg instanceof GoFmtSprintfArgumentDynamic) {
                result = Math.max(result, ((GoFmtSprintfArgumentDynamic) arg).getAccessorsDepth());
            }
        }

        return result;
    }
}