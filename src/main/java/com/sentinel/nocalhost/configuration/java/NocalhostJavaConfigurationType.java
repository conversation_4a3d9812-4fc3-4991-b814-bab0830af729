package com.sentinel.nocalhost.configuration.java;

import com.intellij.execution.configurations.ConfigurationType;
import icons.NocalhostIcons;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;

public class NocalhostJavaConfigurationType implements ConfigurationType {
    @Override
    @NotNull
    @Nls(capitalization = Nls.Capitalization.Title)
    public String getDisplayName() {
        return "JetDev Java";
    }

    @Override
    @Nls(capitalization = Nls.Capitalization.Sentence)
    public String getConfigurationTypeDescription() {
        return "Run and debug java application with JetDev";
    }

    @Override
    public Icon getIcon() {
        return NocalhostIcons.Java;
    }

    @Override
    @NotNull
    @NonNls
    public String getId() {
        return "NocalhostJavaConfigurationType";
    }

    @Override
    public NocalhostJavaConfigurationFactory[] getConfigurationFactories() {
        return new NocalhostJavaConfigurationFactory[]{new NocalhostJavaConfigurationFactory(this)};
    }
}
