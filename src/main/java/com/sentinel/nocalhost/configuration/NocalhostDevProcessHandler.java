package com.sentinel.nocalhost.configuration;

import com.intellij.execution.ExecutionException;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.execution.executors.DefaultDebugExecutor;
import com.intellij.execution.process.KillableProcessHandler;
import com.intellij.execution.process.ProcessAdapter;
import com.intellij.execution.process.ProcessEvent;
import com.intellij.execution.runners.ExecutionEnvironment;
import com.intellij.execution.ui.ConsoleView;
import com.intellij.execution.ui.ConsoleViewContentType;
import com.intellij.execution.ui.RunContentDescriptor;
import com.intellij.execution.ui.RunContentManager;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.util.io.BaseOutputReader;
import com.sentinel.nocalhost.task.ExecutionTask;
import com.sentinel.nocalhost.utils.ErrorUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.IOException;
import java.io.OutputStream;
import java.text.MessageFormat;

public class NocalhostDevProcessHandler extends KillableProcessHandler {

    private final ExecutionEnvironment executionEnvironment;
    public final int appId;
    public final String action;

    public NocalhostDevProcessHandler(
            @NotNull GeneralCommandLine commandLine,
            @NotNull ExecutionEnvironment environment,
            NocalhostProfileState state, int appId
    ) throws ExecutionException {
        super(commandLine);
        this.executionEnvironment = environment;
        this.action = environment.getExecutor().getId();
        this.appId = appId;
        this.addProcessListener(new ProcessAdapter() {
            @Override
            public void startNotified(@NotNull ProcessEvent event) {
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    try {
                        state.startup();
                    } catch (Exception ex) {
                        ErrorUtil.dealWith(environment.getProject(), "NocalhostProfileState#startup", ex.getMessage(), ex);
                    }
                });
            }

            @Override
            public void processTerminated(@NotNull ProcessEvent event) {
                if (StringUtils.isEmpty(action) && ExecutionTask.kDebug.equals(action)) {
                    state.destroy();
                }
            }
        });
    }

    @Override
    protected void doDestroyProcess() {
        if (StringUtils.isEmpty(action) && ExecutionTask.kDebug.equals(action)) {
            sendCtrlC();
            notifyProcessTerminated(0);
        } else {
            super.doDestroyProcess();
        }
    }

    @Override
    protected void notifyProcessTerminated(int exitCode) {
        print(MessageFormat.format("\nProcess finished with exit code {0}.", Math.max(exitCode, 0)));
        super.notifyProcessTerminated(0);
    }

    private void sendCtrlC() {
        try (OutputStream outputStream = this.getProcess().getOutputStream()) {
            outputStream.write(3);
            outputStream.flush();
        } catch (IOException ignored) {
        }
    }

    private void print(@NotNull String message) {
        if (!executionEnvironment.getProject().isDisposed()) {
            ApplicationManager.getApplication().invokeLater(() -> {
                ConsoleView console = getConsoleView();
                if (console != null) console.print(message, ConsoleViewContentType.SYSTEM_OUTPUT);
            });
        }
    }

    @Nullable
    private ConsoleView getConsoleView() {
        if (executionEnvironment.getExecutor() instanceof DefaultDebugExecutor) {
            if (null != executionEnvironment.getContentToReuse()) {
                return (ConsoleView) executionEnvironment.getContentToReuse().getExecutionConsole();
            }
        }
        RunContentDescriptor contentDescriptor = RunContentManager
                .getInstance(executionEnvironment.getProject())
                .findContentDescriptor(executionEnvironment.getExecutor(), this);
        ConsoleView console = null;
        if (contentDescriptor != null && contentDescriptor.getExecutionConsole() instanceof ConsoleView) {
            console = (ConsoleView) contentDescriptor.getExecutionConsole();
        }
        return console;
    }

    @Override
    protected @NotNull BaseOutputReader.Options readerOptions() {
        return new BaseOutputReader.Options() {
            @Override
            public boolean splitToLines() {
                return false;
            }
        };
    }
}
