package com.sentinel.nocalhost.configuration.python;

import com.intellij.execution.configurations.ConfigurationType;
import icons.NocalhostIcons;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;

public class NocalhostPythonConfigurationType implements ConfigurationType {
    @Override
    @NotNull
    @Nls(capitalization = Nls.Capitalization.Title)
    public String getDisplayName() {
        return "JetDev Python";
    }

    @Override
    @Nls(capitalization = Nls.Capitalization.Sentence)
    public String getConfigurationTypeDescription() {
        return "Run and debug Python application with JetDev";
    }

    @Override
    public Icon getIcon() {
        return NocalhostIcons.Python;
    }

    @Override
    @NotNull
    @NonNls
    public String getId() {
        return "NocalhostPythonConfigurationType";
    }

    @Override
    public NocalhostPythonConfigurationFactory[] getConfigurationFactories() {
        return new NocalhostPythonConfigurationFactory[]{new NocalhostPythonConfigurationFactory(this)};
    }
}
