package com.sentinel.nocalhost.configuration.python;

import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.intellij.execution.ExecutionException;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.execution.process.ProcessHandler;
import com.intellij.execution.runners.ExecutionEnvironment;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.jetbrains.python.debugger.remote.PyRemoteDebugCommandLineState;
import com.sentinel.nocalhost.commands.data.ServiceContainer;
import com.sentinel.nocalhost.configuration.HotReload;
import com.sentinel.nocalhost.configuration.NocalhostRunnerContext;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostContextManager;
import com.sentinel.nocalhost.service.NocalhostIdeHubApIService;
import com.sentinel.nocalhost.topic.NocalhostOutputAppendNotifier;
import com.sentinel.nocalhost.utils.NhctlUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public class NocalhostPythonProfileState extends PyRemoteDebugCommandLineState {
    private static final Logger LOG = Logger.getInstance(NocalhostPythonProfileState.class);
    private final List<Disposable> disposables = Lists.newArrayList();
    private final NocalhostIdeHubApIService hubApIService = ApplicationManager.getApplication().getService(NocalhostIdeHubApIService.class);
    private final AtomicReference<NocalhostRunnerContext> refContext = new AtomicReference<>(null);

    public NocalhostPythonProfileState(@NotNull Project project, @NotNull ExecutionEnvironment env) {
        super(project, env);
    }

    protected @NotNull ProcessHandler startProcess() {
        return new NocalhostPythonDevProcessHandler(getEnvironment(), this);
    }

    public void prepare() throws ExecutionException {
        var project = getEnvironment().getProject();
        var context = NocalhostContextManager.getInstance(project).getContext();
        if (context == null) {
            throw new ExecutionException(Constants.USER_APP_NOT_SUCCESSFUL);
        }

        hubApIService.isUserAppStarted(project, context);

        hubApIService.isProjectPathMatched(project, context);

        var devConfig = NhctlUtil.getDevConfig(context);
        var containers = devConfig.getContainers();
        var container = containers.isEmpty() ? null : containers.get(0);

        for (ServiceContainer c : containers) {
            if (StringUtils.equals(context.getName(), c.getName())) {
                container = c;
                break;
            }
        }
        NocalhostRunnerContext.Command command = getCommand(container);

        refContext.set(new NocalhostRunnerContext(
                null,
                container.getDev().getShell(),
                command,
                context,
                container
        ));
    }

    private NocalhostRunnerContext.Command getCommand(ServiceContainer container) throws ExecutionException {
        if (container == null) {
            throw new ExecutionException("Service container config not found.");
        }

        NocalhostRunnerContext.Command command = new NocalhostRunnerContext.Command(resolveRunCommand(container), resolveDebugCommand(container));
        if (!StringUtils.isNotEmpty(command.getDebug())) {
            throw new ExecutionException("Failed to resolve debug command.");
        }

        String port = resolveDebugPort(container);
        if (!StringUtils.isNotEmpty(port)) {
            throw new ExecutionException("Remote debug port is not configured.");
        }
        return command;
    }

    private String resolveRunCommand(ServiceContainer container) {
        if (container == null
                || container.getDev() == null
                || container.getDev().getCommand() == null
                || container.getDev().getCommand().getRun() == null) {
            return null;
        }
        return String.join(" ", container.getDev().getCommand().getRun());
    }

    private String resolveDebugCommand(ServiceContainer container) {
        if (container == null
                || container.getDev() == null
                || container.getDev().getCommand() == null
                || container.getDev().getCommand().getDebug() == null) {
            return null;
        }
        return String.join(" ", container.getDev().getCommand().getDebug());
    }

    public @Nullable NocalhostRunnerContext getRunnerContext() {
        return refContext.get();
    }

    public void startup() throws ExecutionException, InterruptedException {
        NocalhostRunnerContext dev = refContext.get();
        if (dev == null) {
            throw new ExecutionException("Please call NocalhostPythonProfileState#prepare() before NocalhostPythonProfileState#startup().");
        }

        hubApIService.isUserAppStarted(getEnvironment().getProject(), dev.getContext());

        List<String> lines = hubApIService.getCommandLine(dev.getContext(), getEnvironment());

        createTunnel(dev.getContainer());
        Thread.sleep(2000);
        createClient(lines);
        createReload(dev.getContainer());
    }

    public void destroy() {
        disposables.forEach(Disposable::dispose);
        disposables.clear();
    }

    private String resolveDebugPort(ServiceContainer serviceContainer) {
        if (serviceContainer == null
                || serviceContainer.getDev() == null
                || serviceContainer.getDev().getDebug() == null) {
            return null;
        }
        return serviceContainer.getDev().getDebug().getRemoteDebugPort();
    }

    private void createTunnel(ServiceContainer container) throws ExecutionException {
        var port = resolveDebugPort(container);
        var project = getEnvironment().getProject();
        var context = NocalhostContextManager.getInstance(project).getContext();

        if (StringUtils.isEmpty(port)) {
            throw new ExecutionException("Failed to resolve remote debug port.");
        }

        assert context != null;
        var cmd = new GeneralCommandLine(Lists.newArrayList(
                NhctlUtil.binaryPath(), "ssh", "reverse",
                "--pod", // todo: NhctlUtil.getDevPodName(project, context),
                "--local", port,
                "--remote", port,
                "--sshport", "50022",
                "--namespace", context.getNamespace(),
                "--kubeconfig" // todo: context.getKubeConfigPath().toString()
        )).withRedirectErrorStream(true);

        var bus = project
                .getMessageBus()
                .syncPublisher(NocalhostOutputAppendNotifier.NOCALHOST_OUTPUT_APPEND_NOTIFIER_TOPIC);
        bus.action(withNewLine("[cmd] " + cmd.getCommandLineString()));

        var process = cmd.createProcess();
        disposables.add(process::destroy);
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            var reader = new InputStreamReader(process.getInputStream(), Charsets.UTF_8);
            try (var br = new BufferedReader(reader)) {
                String line;
                while ((line = br.readLine()) != null) {
                    bus.action(withNewLine(line));
                }
                var code = process.waitFor();
                if (code != 0) {
                    bus.action(withNewLine("[ssh] Process finished with exit code " + code));
                }
            } catch (Exception ex) {
                LOG.error(ex);
            }
        });
    }

    private void createClient(List<String> lines) throws ExecutionException {
        var cmd = new GeneralCommandLine(lines).withRedirectErrorStream(true);
        var bus = getEnvironment()
                .getProject()
                .getMessageBus()
                .syncPublisher(NocalhostOutputAppendNotifier.NOCALHOST_OUTPUT_APPEND_NOTIFIER_TOPIC);
        bus.action(withNewLine("[cmd] " + cmd.getCommandLineString()));

        var process = cmd.createProcess();
        disposables.add(() -> {
            var output = process.getOutputStream();
            try {
                output.write(3);
                output.flush();
            } catch (IOException ex) {
                LOG.warn("[exec] Fail to send ctrl+c to remote process", ex);
            }
        });
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            var reader = new InputStreamReader(process.getInputStream(), Charsets.UTF_8);
            try (var br = new BufferedReader(reader)) {
                String line;
                while ((line = br.readLine()) != null) {
                    bus.action(withNewLine(line));
                }
                var code = process.waitFor();
                if (code != 0) {
                    bus.action(withNewLine("[exec] Process finished with exit code " + code));
                }
            } catch (Exception ex) {
                LOG.error(ex);
            }
        });
    }

    private void createReload(ServiceContainer container) throws ExecutionException {
        if (container.getDev().isHotReload()) {
            disposables.add(new HotReload(getEnvironment()).withExec());
        }
    }

    private @NotNull String withNewLine(String text) {
        return text + System.lineSeparator();
    }


}
