package com.sentinel.nocalhost.configuration;

import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.intellij.execution.ExecutionException;
import com.intellij.execution.ExecutionManager;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.execution.runners.ExecutionEnvironment;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.roots.ProjectFileIndex;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.openapi.vfs.newvfs.BulkFileListener;
import com.intellij.openapi.vfs.newvfs.events.VFileEvent;
import com.intellij.util.messages.MessageBusConnection;
import com.sentinel.nocalhost.commands.data.NhctlSyncStatus;
import com.sentinel.nocalhost.service.NocalhostContextManager;
import com.sentinel.nocalhost.topic.NocalhostOutputAppendNotifier;
import com.sentinel.nocalhost.utils.DataUtils;
import com.sentinel.nocalhost.utils.NhctlUtil;
import org.jetbrains.annotations.NotNull;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class HotReload implements Disposable {
    private static final Logger LOG = Logger.getInstance(HotReload.class);

    private Process process;
    private final MessageBusConnection connection;
    private final ExecutionEnvironment environment;
    private final AtomicBoolean hasFileChanged = new AtomicBoolean(false);

    public HotReload(@NotNull ExecutionEnvironment environment) {
        this.environment = environment;
        connection = environment.getProject().getMessageBus().connect();
        connection.subscribe(VirtualFileManager.VFS_CHANGES, new BulkFileListener() {
            public void after(@NotNull List<? extends VFileEvent> events) {
                var project = environment.getProject();
                for (VFileEvent event: events) {
                    if (event.getFile() == null || inIdea(event.getFile())) {
                        continue;
                    }
                    if (ProjectFileIndex.getInstance(project).isInContent(event.getFile())) {
                        echo("[event]" + event + System.lineSeparator());
                        hasFileChanged.compareAndSet(false, true);
                    }
                }
            }
        });
    }

    private boolean inIdea(VirtualFile file) {
        for (Path part : Paths.get(file.getPresentableUrl())) {
            if (part.toString().equals(".idea")) {
                return true;
            }
        }
        return false;
    }

    public void dispose() {
        if (process != null) {
            var output = process.getOutputStream();
            try {
                output.write(3);
                output.flush();
            } catch (IOException ex) {
                LOG.warn("[hot-reload] Fail to send ctrl+c to remote process", ex);
            } finally {
                process.destroy();
            }
        }
        connection.dispose();
    }

    private void echo(@NotNull String text) {
        environment
                .getProject()
                .getMessageBus()
                .syncPublisher(NocalhostOutputAppendNotifier.NOCALHOST_OUTPUT_APPEND_NOTIFIER_TOPIC)
                .action(text + System.lineSeparator());
    }

    public HotReload withExec() throws ExecutionException {
        var ctx = NocalhostContextManager.getInstance(environment.getProject()).getContext();
        assert ctx != null;
        var cmd = new GeneralCommandLine(Lists.newArrayList(
                NhctlUtil.binaryPath(),
                "cicd",
                "sync-status",
                "--wait",
                "--app",
                String.valueOf(ctx.getId()),
                "--cluster",
                ctx.getCluster(),
                "--namespace",
                ctx.getNamespace()
        )).withRedirectErrorStream(true);

        echo("[cmd] " + cmd.getCommandLineString());
        process = cmd.createProcess();

        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            var reader = new InputStreamReader(process.getInputStream(), Charsets.UTF_8);
            try (var br = new BufferedReader(reader)) {
                String line;
                while ((line = br.readLine()) != null) {
                    echo(line);
                    if (hasFileChanged.get()) {
                        var parsed = DataUtils.GSON.fromJson(line, NhctlSyncStatus.class);
                        if ("idle".equals(parsed.getStatus())) {
                            ExecutionManager.getInstance(environment.getProject()).restartRunProfile(environment);
                            break;
                        }
                    }
                }
                var code = process.waitFor();
                if (code != 0) {
                    echo("[hot-reload] Process finished with exit code " + code);
                }
            } catch (Exception ex) {
                LOG.error(ex);
            }
        });
        return this;
    }
}
