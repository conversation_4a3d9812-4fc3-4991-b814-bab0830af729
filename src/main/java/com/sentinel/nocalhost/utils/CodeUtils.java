package com.sentinel.nocalhost.utils;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.ScrollType;
import com.intellij.openapi.editor.markup.HighlighterLayer;
import com.intellij.openapi.editor.markup.HighlighterTargetArea;
import com.intellij.openapi.editor.markup.TextAttributes;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.popup.Balloon;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.ui.JBColor;
import com.intellij.ui.awt.RelativePoint;
import com.intellij.ui.components.JBLabel;
import com.intellij.util.ui.JBUI;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import com.sentinel.nocalhost.constants.Constants;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.data.MutableDataSet;
import org.apache.commons.lang3.StringUtils;

import javax.swing.*;
import javax.swing.text.html.HTMLEditorKit;
import javax.swing.text.html.StyleSheet;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;

public class CodeUtils {

    public static String getDiff(String originalCode, String optimizedCode) {
        // 分行处理原始代码和优化后的代码
        List<String> originalLines = originalCode.lines().toList();
        List<String> optimizedLines = optimizedCode.lines().toList();

        // 使用 ArrayList 来保存最终代码
        List<String> resultCode = new ArrayList<>();

        int originalSize = originalLines.size();
        int optimizedSize = optimizedLines.size();

        // 动态规划数组，记录最优路径
        int[][] dp = new int[originalSize + 1][optimizedSize + 1];

        // 填充DP表
        for (int i = originalSize - 1; i >= 0; i--) {
            for (int j = optimizedSize - 1; j >= 0; j--) {
                if (originalLines.get(i).equals(optimizedLines.get(j))) {
                    dp[i][j] = dp[i + 1][j + 1] + 1;
                } else {
                    dp[i][j] = Math.max(dp[i + 1][j], dp[i][j + 1]);
                }
            }
        }

        int originalIndex = 0;
        int optimizedIndex = 0;

        while (originalIndex < originalSize && optimizedIndex < optimizedSize) {
            if (originalLines.get(originalIndex).equals(optimizedLines.get(optimizedIndex))) {
                resultCode.add(originalLines.get(originalIndex));
                originalIndex++;
                optimizedIndex++;
            } else if (dp[originalIndex + 1][optimizedIndex] >= dp[originalIndex][optimizedIndex + 1]) {
                resultCode.add(Constants.SUGGESTION_DELETE_CODE_PREFIX + originalLines.get(originalIndex));
                originalIndex++;
            } else {
                resultCode.add(Constants.SUGGESTION_ADD_CODE_PREFIX + optimizedLines.get(optimizedIndex));
                optimizedIndex++;
            }
        }

        while (originalIndex < originalSize) {
            resultCode.add(Constants.SUGGESTION_DELETE_CODE_PREFIX + originalLines.get(originalIndex));
            originalIndex++;
        }

        while (optimizedIndex < optimizedSize) {
            resultCode.add(Constants.SUGGESTION_ADD_CODE_PREFIX + optimizedLines.get(optimizedIndex));
            optimizedIndex++;
        }

        List<String> finalCode = getFinalCode(resultCode);

        // 输出最终合并后的代码
        return String.join("\n", finalCode);
    }

    private static List<String> getFinalCode(List<String> resultCode) {
        List<String> finalCode = new ArrayList<>();
        boolean isNewBlock = false;
        boolean isDelBlock = false;

        for (String line : resultCode) {
            if (line.startsWith(Constants.SUGGESTION_ADD_CODE_PREFIX)) {
                if (!isNewBlock) {
                    isNewBlock = true;
                }
                finalCode.add(line);
            } else if (line.startsWith(Constants.SUGGESTION_DELETE_CODE_PREFIX)) {
                if (!isDelBlock) {
                    isDelBlock = true;
                }
                finalCode.add(line);
            } else {
                if (isNewBlock) {
                    isNewBlock = false;
                }
                if (isDelBlock) {
                    isDelBlock = false;
                }
                finalCode.add(line);
            }
        }
        return finalCode;
    }


    public static Editor openSuggestionFile(Project project, CodeReviewSuggestionInfo suggestion) {
        Editor fileEditor = null;
        VirtualFile file = LocalFileSystem.getInstance().findFileByPath(project.getBasePath() + "/" + suggestion.getFile_path());
        if (file != null) {
            FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
            fileEditorManager.openFile(file, true);
            fileEditor = fileEditorManager.getSelectedTextEditor();
            if (fileEditor != null) {
                if (!"done".equals(suggestion.getStatus())) {
                    lightSourceCode(suggestion, fileEditor);
                }
            }
        }
        return fileEditor;
    }

    private static void lightSourceCode(CodeReviewSuggestionInfo suggestion, Editor fileEditor) {
        SwingUtilities.invokeLater(()->{
            int startOffset = fileEditor.getDocument().getText().indexOf(suggestion.getOriginal_code_snippet());
            if (startOffset > -1) {
                int endOffset = startOffset + suggestion.getOriginal_code_snippet().length();
                highlighterCode(fileEditor, startOffset, endOffset, new JBColor(new Color(56, 85, 112), new Color(64, 87, 108)));
            }
        });
    }

    public static void highlighterCode(Editor fileEditor, int startOffset, int suggestionEndOffset, JBColor color) {
        SwingUtilities.invokeLater(()->{
            fileEditor.getCaretModel().moveToOffset(startOffset);
            fileEditor.getScrollingModel().scrollToCaret(ScrollType.CENTER);
            TextAttributes highlightAttributes = new TextAttributes();
            highlightAttributes.setBackgroundColor(color);
            fileEditor.getMarkupModel().addRangeHighlighter(
                    startOffset,
                    suggestionEndOffset,
                    HighlighterLayer.SELECTION - 1,
                    highlightAttributes,
                    HighlighterTargetArea.EXACT_RANGE
            );
        });
    }

    public static String convertMarkdownToHtml(String markdown) {
        MutableDataSet options = new MutableDataSet();
        Parser parser = Parser.builder(options).build();
        HtmlRenderer renderer = HtmlRenderer.builder(options)
                .softBreak("<br/>")
                .build();

        return renderer.render(parser.parse(markdown));
    }

    public static void setStyledText(JEditorPane editorPane, String markdown) {
        String html = convertMarkdownToHtml(markdown);

        HTMLEditorKit kit = new HTMLEditorKit();
        StyleSheet styleSheet = kit.getStyleSheet();

        // Base styles - more compact, no background
        styleSheet.addRule("body {"
                + "font-family: " + JBUI.Fonts.label().getFamily() + ", -apple-system, sans-serif;"
                + "font-size: " + (JBUI.Fonts.label().getSize() - 1) + "px;"
                + "line-height: 1.2;"
                + "color: " + colorToHex(JBColor.foreground()) + ";"
                + "margin: 4px;"
                + "padding: 0;"
                + "}");

        // Code highlighting - smaller font, no background
        styleSheet.addRule("code {"
                + "font-family: 'JetBrains Mono', monospace;"
                + "color: #CC7832;"  // Orange for code
                + "font-size: " + (JBUI.Fonts.label().getSize() - 2) + "px;"
                + "padding: 0;"
                + "margin: 0;"
                + "}");

        // List styles - improved alignment
        styleSheet.addRule("ul, ol {"
                + "margin-top: 2px;"
                + "margin-bottom: 2px;"
                + "padding-left: 20px;"
                + "list-style-position: outside;"
                + "}");

        styleSheet.addRule("li {"
                + "margin: 2px 0;"
                + "padding: 0;"
                + "}");

        // Paragraph styles - more compact
        styleSheet.addRule("p {"
                + "margin: 2px 0;"
                + "padding: 0;"
                + "}");

        // Heading styles - reduced margins
        styleSheet.addRule("h1, h2, h3, h4, h5, h6 {"
                + "margin: 6px 0;"
                + "padding: 0;"
                + "line-height: 1.2;"
                + "}");

        // Method/class name highlighting
        styleSheet.addRule(".method {"
                + "color: #FFC66D;"  // Yellow for methods
                + "}");

        // Configure editor pane
        editorPane.setContentType("text/html");
        editorPane.setEditorKit(kit);
        editorPane.setText(html);
        editorPane.setEditable(false);
        editorPane.putClientProperty(JEditorPane.HONOR_DISPLAY_PROPERTIES, Boolean.TRUE);
        editorPane.setBackground(null); // Remove background

        // Optimize display properties
        editorPane.putClientProperty("JEditorPane.honorDisplayProperties", Boolean.TRUE);
        editorPane.putClientProperty("html.disable", Boolean.FALSE);
    }

    private static String colorToHex(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }

    public static JPanel getEditorHeader(String language, String code) {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBorder(BorderFactory.createEmptyBorder());
        headerPanel.setPreferredSize(new Dimension(-1, 30));
        JBLabel label = new JBLabel();
        label.setText(" " + ("Empty".equals(language) ? "" : language));
        label.setFont(new Font("Microsoft YaHei", Font.BOLD, 14));
        headerPanel.add(label, BorderLayout.WEST);
        if (StringUtils.isNotEmpty(code)) {
            JLabel jLabel = CodeUtils.getJLabel(code);
            headerPanel.add(jLabel, BorderLayout.EAST);
        }
        return headerPanel;
    }

    public static JLabel getJLabel(String code) {
        JLabel copyLabel = new JLabel();
        copyLabel.setIcon(AllIcons.Actions.Copy);
        copyLabel.setToolTipText("Copy");
        copyLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 0, 8));
        copyLabel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                StringSelection stringSelection = new StringSelection(code);
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stringSelection, null);
                Balloon balloon = JBPopupFactory.getInstance()
                        .createHtmlTextBalloonBuilder("复制成功",
                                AllIcons.General.InspectionsOK, JBColor.PanelBackground, null)
                        .setTitle(null)
                        .setFadeoutTime(3000)
                        .createBalloon();
                balloon.show(RelativePoint.getCenterOf(copyLabel), Balloon.Position.below);
            }
        });
        return copyLabel;
    }

}
