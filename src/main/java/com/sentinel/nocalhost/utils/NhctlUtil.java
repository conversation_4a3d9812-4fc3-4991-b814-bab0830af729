package com.sentinel.nocalhost.utils;

import com.intellij.execution.ExecutionException;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.util.SystemInfo;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.commands.data.NhctlConfigOptions;
import com.sentinel.nocalhost.commands.data.NhctlRawConfig;

import java.io.IOException;
import java.net.ServerSocket;
import java.nio.file.Path;
import java.nio.file.Paths;

public final class NhctlUtil {
    private static final Path NOCALHOST_BIN_DIR = Paths.get(System.getProperty("user.home"), ".nh", "bin");

    public static String binaryDir() {
        return NOCALHOST_BIN_DIR.toString();
    }

    public static String getName() {



        if (SystemInfo.isWindows) {
            return "nhctl.exe";
        } else {
            return "nhctl";
        }
    }

    public static String binaryPath() {
        return NOCALHOST_BIN_DIR.resolve(getName()).toAbsolutePath().toString();
    }


    public static NhctlRawConfig getDevConfig(UserApp context) throws ExecutionException {
        try {
            var opts = new NhctlConfigOptions(String.valueOf(context.getId()), context.getCluster(), context.getNamespace());
            String output =  ApplicationManager
                    .getApplication()
                    .getService(NhctlCommand.class).getConfig(opts);
            return DataUtils.fromYaml(output, NhctlRawConfig.class);
        } catch (Exception ex) {
            throw new ExecutionException(ex);
        }
    }

    public static int findAvailablePort() {
        try (ServerSocket socket = new ServerSocket(0)) {
            socket.setReuseAddress(true);
            return socket.getLocalPort();
        } catch (IOException e) {
            throw new RuntimeException("No available port found", e);
        }
    }

}
