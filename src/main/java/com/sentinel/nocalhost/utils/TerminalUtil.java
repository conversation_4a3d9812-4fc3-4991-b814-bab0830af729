package com.sentinel.nocalhost.utils;

import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.openapi.project.Project;
import org.jetbrains.plugins.terminal.ShellTerminalWidget;
import org.jetbrains.plugins.terminal.TerminalToolWindowManager;

public final class TerminalUtil {
    public static void openTerminal(Project project, String title, GeneralCommandLine commandLine) {
        try {
            String basePath = project.getBasePath();
            if (basePath == null) {
                throw new IllegalStateException("Project base path is null. Cannot open terminal.");
            }
            TerminalToolWindowManager terminalToolWindowManager = TerminalToolWindowManager.getInstance(project);
            if (terminalToolWindowManager == null) {
                throw new IllegalStateException("terminalToolWindowManager is null. Cannot open terminal.");
            }
            ShellTerminalWidget terminalWidget = terminalToolWindowManager.createLocalShellWidget(basePath, title);
            terminalWidget.executeCommand(commandLine.getCommandLineString());
        } catch (Exception e) {
            ErrorUtil.dealWith(project, "Terminal open error",
                    "Error occurs while opening terminal", e);
        }
    }
}
