package com.sentinel.nocalhost.utils;

import com.google.common.base.Charsets;
import com.google.common.collect.Lists;
import com.google.common.io.CharStreams;
import com.google.gson.Gson;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.util.EnvironmentUtil;
import com.sentinel.nocalhost.exception.NocalhostExecuteCmdException;

import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.lang.reflect.Type;

public class DataUtils {
    public static final Gson GSON = new Gson();

    public static <T> T fromYaml(String yaml, Type typeOfT) throws Exception {
        GeneralCommandLine commandLine = new GeneralCommandLine(
                Lists.newArrayList(NhctlUtil.binaryPath(), "yaml", "to-json")
        ).withEnvironment(EnvironmentUtil.getEnvironmentMap()).withRedirectErrorStream(true);
        Process process = commandLine.createProcess();

        PrintWriter out = new PrintWriter(process.getOutputStream(), false, Charsets.UTF_8);
        out.write(yaml);
        out.flush();
        out.close();

        String output = CharStreams.toString(new InputStreamReader(process.getInputStream(),
                Charsets.UTF_8));
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new NocalhostExecuteCmdException(commandLine.getCommandLineString(), exitCode,
                    output);
        }
        return GSON.fromJson(output, typeOfT);
    }

}
