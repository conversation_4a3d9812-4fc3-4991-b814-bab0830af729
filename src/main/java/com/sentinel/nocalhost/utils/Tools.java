package com.sentinel.nocalhost.utils;

import com.intellij.openapi.application.ApplicationInfo;

public class Tools {

    public static String getIdeaName() {
        String ide = "";
        ApplicationInfo applicationInfo = ApplicationInfo.getInstance();
        if (applicationInfo.getFullApplicationName().toLowerCase().contains("idea")) {
            ide = "idea-" + applicationInfo.getBuild().asString();
        }
        if (applicationInfo.getFullApplicationName().toLowerCase().contains("goland")) {
            ide = "goland-" + applicationInfo.getBuild().asString();
        }
        return ide;
    }

}
