package com.sentinel.nocalhost.utils;

import com.sentinel.nocalhost.exception.NocalhostNotifier;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import org.apache.commons.lang3.StringUtils;

public final class NhctlOutputUtil {
    private static final String MESSAGE_TAG_WARNING = "[WARNING]";
    private static final String MESSAGE_TAG_INFO = "[INFO]";
    private static final String MESSAGE_TAG_ERROR = "[ERROR]";

    public static void showMessageByCommandOutput(Project project, String line) {
        ApplicationManager.getApplication().invokeLater(() -> {
            if (StringUtils.startsWith(line, MESSAGE_TAG_WARNING)) {
                String message = line.substring(MESSAGE_TAG_WARNING.length()).trim();
                Messages.showMessageDialog(message, "Nhctl Command Warning", Messages.getWarningIcon());
            }
            if (StringUtils.startsWith(line, MESSAGE_TAG_INFO)) {
                String message = line.substring(MESSAGE_TAG_INFO.length()).trim();
                NocalhostNotifier.getInstance(project).notifySuccess(message, "");
            }
            if (StringUtils.startsWith(line, MESSAGE_TAG_ERROR)) {
                String message = line.substring(MESSAGE_TAG_ERROR.length()).trim();
                NocalhostNotifier.getInstance(project).notifyError(message, "");
            }
        });
    }
}
