package com.sentinel.nocalhost.executors;

import com.intellij.execution.executors.DefaultRunExecutor;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;

public class BackGroundRunExecutor extends DefaultRunExecutor {
    public <NAME_EMAIL> String EXECUTOR_ID = "Run-Background";

    @Override
    public @NotNull @NonNls String getId() {
        return EXECUTOR_ID;
    }

    public static com.intellij.execution.Executor getRunExecutorInstance() {
        return new BackGroundRunExecutor();
    }
}
