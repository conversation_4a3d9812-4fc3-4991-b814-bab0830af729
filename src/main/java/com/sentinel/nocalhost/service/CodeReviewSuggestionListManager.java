package com.sentinel.nocalhost.service;

import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public class CodeReviewSuggestionListManager {
    private final AtomicReference<List<CodeReviewSuggestionInfo>> ref = new AtomicReference<>(new ArrayList<>());
    public static CodeReviewSuggestionListManager getInstance(@NotNull Project project) {
        if (!project.isDisposed()) {
            return project.getService(CodeReviewSuggestionListManager.class);
        }
        return new CodeReviewSuggestionListManager();
    }

    public @Nullable List<CodeReviewSuggestionInfo> getContext() {
        return ref.get();
    }

    public void set(List<CodeReviewSuggestionInfo> suggestionInfos) {
        ref.set(suggestionInfos);
    }

    public List<CodeReviewSuggestionInfo> get() {
        return ref.get();
    }

}
