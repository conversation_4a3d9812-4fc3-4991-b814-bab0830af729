package com.sentinel.nocalhost.service;

import com.intellij.openapi.project.Project;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

public class NocalhostAppQueryManager {
    private String appQuery = StringUtils.EMPTY;

    public static NocalhostAppQueryManager getInstance(@NotNull Project project) {
        if (!project.isDisposed()) {
            return project.getService(NocalhostAppQueryManager.class);
        }
        return new NocalhostAppQueryManager();
    }


    public synchronized void setAppQuery(String appQuery) {
        this.appQuery = appQuery;
    }

    public void del() {
        appQuery = StringUtils.EMPTY;
    }

    public String get() {
        return this.appQuery;
    }
}
