package com.sentinel.nocalhost.service;

import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.UserApp;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.atomic.AtomicReference;

public class NocalhostContextManager {
    private final AtomicReference<UserApp> ref = new AtomicReference<>(null);

    public static NocalhostContextManager getInstance(@NotNull Project project) {
        if (!project.isDisposed()) {
            return project.getService(NocalhostContextManager.class);
        }
        return new NocalhostContextManager();
    }

    public UserApp getContext() {
        return ref.get();
    }

    public void setContext(UserApp userApp) {
        ref.set(userApp);
    }

}
