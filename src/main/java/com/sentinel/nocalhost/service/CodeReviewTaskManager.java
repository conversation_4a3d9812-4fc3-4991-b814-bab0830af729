package com.sentinel.nocalhost.service;

import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.CodeReviewTaskInfo;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.atomic.AtomicReference;

public class CodeReviewTaskManager {
    private final AtomicReference<CodeReviewTaskInfo> ref = new AtomicReference<>();
    public static CodeReviewTaskManager getInstance(@NotNull Project project) {
        if (!project.isDisposed()) {
            return project.getService(CodeReviewTaskManager.class);
        }
        return new CodeReviewTaskManager();
    }

    public CodeReviewTaskInfo getContext() {
        return ref.get();
    }

    public void set(CodeReviewTaskInfo codeReviewTaskInfo) {
        ref.set(codeReviewTaskInfo);
    }

    public CodeReviewTaskInfo get() {
        return ref.get();
    }

}
