package com.sentinel.nocalhost.service;

import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.RunningApp;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.api.data.UserProject;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public class NocalhostAppListManager {
    private final AtomicReference<List<UserProject>> ref = new AtomicReference<>(new ArrayList<>());

    private final AtomicReference<List<RunningApp>> runningApps = new AtomicReference<>(new ArrayList<>());

    public static NocalhostAppListManager getInstance(@NotNull Project project) {
        if (!project.isDisposed()) {
            return project.getService(NocalhostAppListManager.class);
        }
        return new NocalhostAppListManager();
    }

    public @Nullable List<UserProject> getContext() {
        return ref.get();
    }

    public void set(List<UserProject> projectList) {
        for (UserProject userProject : ref.get()) {
            for (UserApp app : userProject.getApps()) {
                for (UserProject project : projectList) {
                    for (UserApp projectApp : project.getApps()) {
                        if (app.getId() == projectApp.getId()) {
                            projectApp.setSyncStatus(app.getSyncStatus());
                        }
                    }
                }
            }
        }
        ref.set(projectList);
    }

    public List<UserProject> get() {
        return ref.get();
    }

    public UserApp getRunningApp(int appId) {
        if (runningApps.get() == null) {
            return null;
        }
        for (RunningApp runningApp : runningApps.get()) {
            if (runningApp.getUserApp().getId() == appId) {
                if (runningApp.getRunningTime() + 2 * 60 * 1000L > new Date().getTime()) {
                    return runningApp.getUserApp();
                } else {
                    runningApps.get().remove(runningApp);
                    return null;
                }
            }
        }
        return null;
    }

    public List<RunningApp> getRunningAppList() {
        List<RunningApp> apps = runningApps.get();
        if (null != apps && !apps.isEmpty()) {
            List<RunningApp> validApps = apps.stream().filter(app -> app.getRunningTime() + 2 * 60 * 1000L < new Date().getTime()).toList();
            apps.removeAll(validApps);
            runningApps.set(apps);
        }
        return apps;
    }

    public void addRunningApp(UserApp userApp) {
        RunningApp runningApp = new RunningApp();
        runningApp.setUserApp(userApp);
        runningApps.get().add(runningApp);
    }

    public void setRunningApps(List<RunningApp> userApps) {
        runningApps.set(userApps);
    }

    public void delRunningAppById(int appId) {
        for (RunningApp runningApp : runningApps.get()) {
            if (runningApp.getUserApp().getId() == appId) {
                runningApps.get().remove(runningApp);
                break;
            }
        }
    }


}
