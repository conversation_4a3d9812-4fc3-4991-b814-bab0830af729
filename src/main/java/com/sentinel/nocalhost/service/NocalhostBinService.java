package com.sentinel.nocalhost.service;

import com.github.zafarkhaja.semver.Version;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.IdeHubAPI;
import com.sentinel.nocalhost.api.data.Nhctl;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.exception.NocalhostUnsupportedOperatingSystemException;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.utils.NhctlUtil;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.util.SystemInfo;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.FileLock;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NocalhostBinService {
    private static final Pattern NHCTL_VERSION_PATTERN = Pattern.compile("Version:\\s*v?(\\d+\\.\\d+\\.\\d+)\n");

    private final IdeHubAPI ideHubAPI = ApplicationManager.getApplication().getService(IdeHubAPI.class);

    private final NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);

    private final File nocalhostBin;

    public NocalhostBinService() {
        nocalhostBin = new File(NhctlUtil.binaryPath());
    }

    public void checkBin(Project project) {
        boolean shouldDownload = false;
        if (nocalhostBin.exists()) {
            if (!nocalhostBin.canExecute()) {
                boolean success = nocalhostBin.setExecutable(true);
                if (!success) {
                    Messages.showErrorDialog(
                            "Failed to set executable permission for " + nocalhostBin.getAbsolutePath(),
                            "JetDev Command Tool Set Executable Permission Error"
                    );
                }
            }
            try {
                nhctlCommand.version();
            } catch (Exception e) {
                shouldDownload = true;
            }
        } else {
            shouldDownload = true;
        }
        if (shouldDownload) {
            Nhctl nhctl = ideHubAPI.queryNhctlSetting(project);
            if (null != nhctl && StringUtils.isNotEmpty(nhctl.getVersion())) {
                String downloadUrl = "http://" + nhctl.getLink() + "/releases/" + nhctl.getVersion() + "/" + getDownloadFilename();
                tryDownload(project, downloadUrl, "Download JetDev Command Tool");
            } else {
                Messages.showErrorDialog(Constants.JET_DEV_COMMAND_TOOL_VERSION_NOT_CONFIG, "Download JetDev Command Tool Error");
            }
        }
    }

    public void checkVersion(Project project) {
        try {
            Nhctl nhctl = ideHubAPI.queryNhctlSetting(project);
            if (null == nhctl) {
                Messages.showErrorDialog(StringUtils.EMPTY, Constants.JET_DEV_COMMAND_TOOL_VERSION_NOT_CONFIG);
                return;
            }
            Matcher matcher = NHCTL_VERSION_PATTERN.matcher(nhctlCommand.version());
            if (matcher.find()) {
                Version currentVersion = Version.valueOf(matcher.group(1));
                Version requiredVersion = Version.valueOf(nhctl.getVersion());
                if (currentVersion.compareTo(requiredVersion) < 0) {
                    String downloadUrl = "http://" + nhctl.getLink() + "/releases/" + nhctl.getVersion() + "/" + getDownloadFilename();
                    tryDownload(project, downloadUrl, "Upgrade JetDev Command Tool");
                }
            }
        } catch (Exception e) {
            Messages.showErrorDialog(e.getMessage(), "Get JetDev Command Tool Version Error");
        }
    }

    private void tryDownload(Project project, String downloadUrl, String title) {
        Exception downloadException;
        try {
            downloadJetDevCommandTool(project, downloadUrl, title);
        } catch (Exception e) {
            downloadException = e;
            Exception finalDownloadException = downloadException;
            ApplicationManager.getApplication().invokeLater(() ->
                    Messages.showErrorDialog(finalDownloadException.getMessage(), "Download JetDev Command Tool Error"));
        }
    }

    private void downloadJetDevCommandTool(Project project, String downloadUrl, String title) throws Exception {
        ProgressManager.getInstance().run(new Task.WithResult<Void, Exception>(project, title, true) {
            @Override
            protected Void compute(@NotNull ProgressIndicator indicator) throws Exception {
                indicator.setIndeterminate(false);
                try {
                    startDownload(downloadUrl, Paths.get(NhctlUtil.binaryDir()), indicator);
                } catch (Throwable t) {
                    throw new Exception(t.getMessage());
                }
                boolean success = nocalhostBin.setExecutable(true);
                if (!success) {
                    throw new Exception("Failed to set executable permission for " + nocalhostBin.getAbsolutePath());
                }
                try {
                    nhctlCommand.reviewStop();
                    Thread.sleep(15000L);
                    nhctlCommand.reviewInit();
                } catch (Exception e) {
                    System.out.println("停止/安装CodeReview服务失败");
                }
                Notifications.showNotification(project, Constants.NHCTL_NOTIFICATION_GROUP, title + " Success",
                        StringUtils.EMPTY, NotificationType.INFORMATION);
                return null;
            }
        });
    }

    private void startDownload(final String downloadUrl, final Path binDir, final ProgressIndicator indicator) throws IOException {
        final String downloadFilename = getDownloadFilename();
        final HttpUrl url = HttpUrl.parse(downloadUrl);
        if (url == null) {
            return;
        }

        indicator.setText(String.format("Downloading %s", downloadFilename));
        indicator.setText2(String.format("from " + url.host()));

        Files.createDirectories(binDir);

        Request request = new Request.Builder().url(url).build();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        Path downloadingPath = binDir.resolve(getDownloadingTempFilename());

        FileOutputStream fos = null;
        FileLock fl = null;
        Response response = null;
        try {
            fos = new FileOutputStream(downloadingPath.toFile());
            fl = fos.getChannel().tryLock();
            if (fl == null) {
                return;
            }

            response = client.newCall(request).execute();

            if (!response.isSuccessful()) {
                throw new IOException("Failed to download file: " + response);
            }

            assert response.body() != null;
            InputStream inputStream = response.body().byteStream();
            byte[] buffer = new byte[1024 * 1024];
            int len;
            long totalBytesRead = 0;
            long contentLength = response.body().contentLength();

            while ((len = inputStream.read(buffer)) != -1) {
                fos.write(buffer, 0, len);
                totalBytesRead += len;

                if (contentLength != -1) {
                    double fraction = (double) totalBytesRead / contentLength;
                    indicator.setFraction(Math.min(fraction, 1.0));
                } else {
                    indicator.setIndeterminate(true); // Indeterminate progress
                }
            }

        } finally {
            try {
                if (fl != null) {
                    fl.release();
                }
                if (fos != null) {
                    fos.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException ignore) {
            }
        }

    var destPath = Paths.get(NhctlUtil.binaryPath());
        if (SystemInfo.isWindows) {
            if (Files.exists(destPath)) {
                var tempPath = Paths.get(NhctlUtil.binaryPath() + "." + System.currentTimeMillis());
                Files.move(destPath, tempPath, StandardCopyOption.REPLACE_EXISTING);
                tempPath.toFile().deleteOnExit();
            }
            Files.move(downloadingPath, destPath);
            return;
        }
        Files.deleteIfExists(destPath);
        Files.move(downloadingPath, destPath);
    }

    private String getDownloadingTempFilename() {
        return NhctlUtil.getName() + ".downloading";
    }

    private static String getDownloadFilename() {
        if (SystemInfo.isMac) {
           return "nhctl-darwin-" + arch();
        }
        if (SystemInfo.isLinux) {
            return "nhctl-linux-" + arch();
        }
        if (SystemInfo.isWindows) {
            return "nhctl-windows-amd64.exe";
        }
        throw new NocalhostUnsupportedOperatingSystemException(SystemInfo.OS_NAME);
    }

    private static String arch() {
        return switch (SystemInfo.OS_ARCH) {
            case "aarch64", "arm64" -> "arm64";
            default -> "amd64";
        };
    }

}
