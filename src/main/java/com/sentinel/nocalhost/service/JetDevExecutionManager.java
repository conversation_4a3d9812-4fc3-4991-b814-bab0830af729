package com.sentinel.nocalhost.service;


import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.configuration.NocalhostDevProcessHandler;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public class JetDevExecutionManager {
    private final AtomicReference<List<NocalhostDevProcessHandler>> ref = new AtomicReference<>(new ArrayList<>());

    public static JetDevExecutionManager getInstance(@NotNull Project project) {
        if (!project.isDisposed()) {
            return project.getService(JetDevExecutionManager.class);
        }
        return new JetDevExecutionManager();
    }

    public synchronized void add(NocalhostDevProcessHandler processHandler) {
        // 直接在handlers上操作，避免创建新列表
        ref.get().removeIf(jetDevProcessHandler -> jetDevProcessHandler.isProcessTerminated()
                || jetDevProcessHandler.isProcessTerminating());
        ref.get().add(processHandler);
    }

    public synchronized void set(List<NocalhostDevProcessHandler> processHandlers) {
        ref.set(processHandlers);
    }

    public synchronized List<NocalhostDevProcessHandler> get() {
        return ref.get();
    }

}
