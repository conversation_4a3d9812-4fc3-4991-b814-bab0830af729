package com.sentinel.nocalhost.service;


import com.google.common.collect.Lists;
import com.intellij.execution.ExecutionException;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.execution.executors.DefaultDebugExecutor;
import com.intellij.execution.runners.ExecutionEnvironment;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.terminal.TerminalTitle;
import com.intellij.terminal.ui.TerminalWidget;
import com.sentinel.nocalhost.Config;
import com.sentinel.nocalhost.api.IdeHubAPI;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.api.data.UserAppConfigs;
import com.sentinel.nocalhost.api.data.UserAppConfigsRequest;
import com.sentinel.nocalhost.api.data.UserProject;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;
import com.sentinel.nocalhost.utils.NhctlUtil;
import com.sentinel.nocalhost.utils.PathsUtil;
import com.sentinel.nocalhost.utils.TerminalUtil;
import com.sentinel.nocalhost.utils.Tools;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.plugins.terminal.TerminalToolWindowManager;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class NocalhostIdeHubApIService {
    private final IdeHubAPI ideHubAPI = ApplicationManager.getApplication().getService(IdeHubAPI.class);


    public void isProjectPathMatched(Project project, UserApp context) throws ExecutionException {
        UserAppConfigs userAppConfigs = getUserAppConfigs(project, context);

        if (null == userAppConfigs) {
            throw new ExecutionException(Constants.SET_LOCAL_DIR);
        }

        if (StringUtils.isNotEmpty(userAppConfigs.getConfig().getLocalDir())) {
            try {
                Path localDirPath = Paths.get(userAppConfigs.getConfig().getLocalDir()).toRealPath();
                Path basePathPath = Paths.get(Objects.requireNonNull(project.getBasePath())).toRealPath();

                if (!localDirPath.equals(basePathPath)) {
                    throw new ExecutionException(Constants.LOCAL_DIR_NOT_MATCH + " localDirPath: " + localDirPath + " basePathPath: " + basePathPath);
                }
            } catch (IOException e) {
                throw new ExecutionException("Error resolving paths: " + e.getMessage(), e);
            }
        }
    }

    public void openRemoteTerminal(Project project, UserApp context) {

        ApplicationManager.getApplication().invokeLater(()-> {
            boolean exists = false;
            TerminalToolWindowManager terminalToolWindowManager =  TerminalToolWindowManager.getInstance(project);
            Set<TerminalWidget> terminalWidgets = terminalToolWindowManager.getTerminalWidgets();
            List<TerminalWidget> terminalWidgetList = new ArrayList<>(terminalWidgets);
            for (TerminalWidget widget : terminalWidgetList) {
                TerminalTitle terminalTitle = widget.getTerminalTitle();
                assert terminalTitle.getDefaultTitle() != null;
                if (terminalTitle.getDefaultTitle().contains(context.getName())) {
                    exists = true;
                    break;
                }
            }

            if (!exists) {
                TerminalUtil.openTerminal(
                        project,
                        String.format(
                                "%s %s",
                                context.getName(),
                                "Dev Terminal"
                        ),
                        new GeneralCommandLine(Lists.newArrayList(
                                PathsUtil.backslash(NhctlUtil.binaryPath()),
                                "cicd",
                                "terminal",
                                "--app", String.valueOf(context.getId()),
                                "--cluster", context.getCluster(),
                                "--namespace", context.getNamespace(),
                                "--client=jetDev",
                                "--ide=" + Tools.getIdeaName(),
                                "--pluginVersion="+ Config.getProperty("report_version")
                        ))
                );
            }

        });

    }


    public List<String> getCommandLine(UserApp context, ExecutionEnvironment environment) {
        List<String> commandLine;
        if (isDebugExecutor(environment)) {
            commandLine = Lists.newArrayList(
                    NhctlUtil.binaryPath(), "cicd", "exec",
                    "--debug-app", "--app", String.valueOf(context.getId()),
                    "--cluster", context.getCluster(),
                    "--namespace", context.getNamespace(),
                    "--client=jetDev",
                    "--ide=" + Tools.getIdeaName(),
                    "--pluginVersion="+ Config.getProperty("report_version")
            );
        } else {
            commandLine = Lists.newArrayList(
                    NhctlUtil.binaryPath(), "cicd", "exec",
                    "--run-app", "--app", String.valueOf(context.getId()),
                    "--cluster", context.getCluster(),
                    "--namespace", context.getNamespace(),
                    "--client=jetDev",
                    "--ide=" + Tools.getIdeaName(),
                    "--pluginVersion="+ Config.getProperty("report_version")
            );
        }
        return commandLine;
    }

    private boolean isDebugExecutor(ExecutionEnvironment environment) {
        return StringUtils.equals(DefaultDebugExecutor.EXECUTOR_ID, environment.getExecutor().getId());
    }

    public void isUserAppStarted(Project project, UserApp context) throws ExecutionException {
        List<UserProject> userProjectList = ideHubAPI.queryUserProjectAppList(project);

        if (null == userProjectList) {
            ApplicationManager.getApplication().getMessageBus()
                    .syncPublisher(NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC)
                    .action(Constants.HUMAN_OPERATE, null);
            throw new ExecutionException(Constants.USER_APP_NOT_EXISTS);
        }

        Optional<UserApp> optionalUserApp = userProjectList.stream()
                .flatMap(userProject -> userProject.getApps().stream())
                .filter(app -> app.getId() == context.getId())
                .findFirst();
        if (optionalUserApp.isEmpty()) {
            ApplicationManager.getApplication().getMessageBus()
                    .syncPublisher(NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC)
                    .action(Constants.HUMAN_OPERATE, null);
            throw new ExecutionException(Constants.USER_APP_NOT_EXISTS);
        }

        if (!Constants.DEBUG_STATUS_SUCCESSFUL.equals(optionalUserApp.get().getStatus())) {
            throw new ExecutionException(Constants.USER_APP_NOT_SUCCESSFUL);
        }
    }

    private UserAppConfigs getUserAppConfigs(Project project, UserApp userApp) {
        UserAppConfigsRequest appConfigsRequest = new UserAppConfigsRequest();
        appConfigsRequest.setAppId(userApp.getId());
        appConfigsRequest.setAppName(userApp.getName());
        appConfigsRequest.setCluster(userApp.getCluster());
        appConfigsRequest.setNamespace(userApp.getNamespace());
        return ideHubAPI.queryUserAppConfigs(project, appConfigsRequest);
    }

}
