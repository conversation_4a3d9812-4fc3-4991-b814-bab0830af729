package com.sentinel.nocalhost.constants;

public class Constants {

    public static final String DEBUG_STATUS_PENDING = "DEBUG_STATUS_PENDING";
    public static final String DEBUG_STATUS_DEPLOYING = "DEBUG_STATUS_DEPLOYING";
    public static final String DEBUG_STATUS_SUCCESSFUL = "DEBUG_STATUS_SUCCESSFUL";
    public static final String DEBUG_STATUS_FAILED = "DEBUG_STATUS_FAILED";

    public static final String LOGIN_NOTIFICATION_GROUP = "Login Notifications";
    public static final String NHCTL_NOTIFICATION_GROUP = "Nhctl Notifications";
    public static final String AUTH_NOTIFICATION_GROUP = "Auth Notifications";
    public static final String API_NOTIFICATION_GROUP = "API Notifications";
    public static final String REVIEW_NOTIFICATION_GROUP = "Review Notifications";

    public static final String LOGIN_Failed = "登录失败";
    public static final String LOGIN_SUCCESS = "登录成功";
    public static final String LOGIN_EXPIRE = "登录失效";
    public static final String USER_NO_AUTH = "权限不足";
    public static final String API_ERROR = "JetDev API调用异常";
    public static final String TRAFFIC_MARK_SET_SUCCESS = "流量标记设置成功";

    public static final String LOCAL_DIR_SET_SUCCESS = "本地代码根目录配置成功";
    public static final String SET_LOCAL_DIR = "请先配置本地代码根目录";
    public static final String LOCAL_DIR_NOT_MATCH = "当前工作目录与服务配置代码根目录不一致";
    public static final String JET_DEV_COMMAND_TOOL_VERSION_NOT_CONFIG = "JetDev Command Tool版本配置为空，请联系管理员配置";


    public static final String LOGIN_EXPIRE_CONTENT = "请重新登录";

    public static final String APP_REFRESH = "刷新服务列表";

    public static final String AUTH_URL_PROPERTY = "cicd_url";
    public static final String TOKEN_PARAMETER = "token";
    public static final String TOKEN_IS_EMPTY = "Token Is Empty";

    public static final String GET_TRAFFIC_MARK_ERROR = "查询流量标记异常";
    public static final String SET_TRAFFIC_MARK_ERROR = "流量标记修改失败，请重试";
    public static final String HUMAN_OPERATE = "human_operate";

    public static final String USER_APP_NOT_EXISTS = "服务已经不存在";
    public static final String USER_APP_NOT_SUCCESSFUL = "服务未部署";

    public static final String SUGGESTION_ADD_CODE_PREFIX = "+|+|+|+";
    public static final String SUGGESTION_DELETE_CODE_PREFIX = "-|-|-|-";

    public static final String NOT_FOUND_SOURCE_CODE_TITLE = "未找到原始代码块，无法自动替换";
    public static final String NOT_FOUND_SOURCE_CODE_CONTENT = "优化后代码块已复制到粘贴板，请自动替换";

    public static final String CODE_FILE_SUFFIX_JAVA = ".java";
    public static final String CODE_FILE_SUFFIX_GO = ".go";
    public static final String CODE_FILE_SUFFIX_PYTHON = ".py";
}
