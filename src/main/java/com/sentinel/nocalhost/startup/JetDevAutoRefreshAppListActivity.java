package com.sentinel.nocalhost.startup;

import com.intellij.ide.ui.UISettingsListener;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;
import com.sentinel.nocalhost.ui.listener.ThemeChangeListener;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.ui.toolwindow.ToolWindowIconUpdater;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;


public class JetDevAutoRefreshAppListActivity implements ProjectActivity {
    private final NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
    private final CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);

    @Nullable
    @Override
    public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {

        NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);

        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            while (!project.isDisposed()) {
                try {
                    if (StringUtils.isNotEmpty(settings.getUserToken())) {
                        ApplicationManager.getApplication().getMessageBus().syncPublisher(
                                NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC).action("Scheduled", null);
                    }
                } catch (Exception ignored) {

                } finally {
                    try {
                        Thread.sleep(6000L);
                    } catch (InterruptedException ignored1) {
                    }
                }
            }
        });

        // 注释：暂时禁用健康检查和自动重启功能
        // ApplicationManager.getApplication().executeOnPooledThread(() -> {
        //     while (!project.isDisposed()) {
        //         try {
        //             Thread.sleep(60000L);
        //         } catch (InterruptedException ignored) {
        //         }
        //         try {
        //             if (StringUtils.isNotEmpty(settings.getUserToken())) {
        //                 if (codeReviewAPI.queryReviewServerStatus(project)) {
        //                     try {
        //                         nhctlCommand.reviewStop();
        //                         Thread.sleep(15000L);
        //                         nhctlCommand.reviewStart();
        //                         Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
        //                                 "CodeReview服务启动中，请等候...", StringUtils.EMPTY, NotificationType.INFORMATION);
        //                     } catch (Exception e) {
        //                         System.out.println("启动CodeReview服务失败");
        //                     }
        //                 }
        //             }
        //         } catch (Exception ignored) {
        //
        //         }
        //     }
        // });

        ApplicationManager.getApplication().invokeLater(() -> {
            ApplicationManager.getApplication().getMessageBus().connect()
                    .subscribe(UISettingsListener.TOPIC, new ThemeChangeListener(project));
            ToolWindowIconUpdater.updateToolWindowIcons(project);
        });

        return null;
    }

}
