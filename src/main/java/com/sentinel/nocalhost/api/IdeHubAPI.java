package com.sentinel.nocalhost.api;

import com.alibaba.fastjson.JSON;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.Config;
import com.sentinel.nocalhost.api.data.*;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostAppListManager;
import com.sentinel.nocalhost.service.NocalhostAppQueryManager;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.topic.RefreshToolWindowNotifier;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.utils.ErrorUtil;
import okhttp3.*;
import okio.ByteString;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class IdeHubAPI {
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(2, TimeUnit.SECONDS)
            .writeTimeout(2, TimeUnit.SECONDS)
            .readTimeout(2, TimeUnit.SECONDS)
            .hostnameVerifier((hostName, session) -> true)
            .retryOnConnectionFailure(true)
            .build();

    public List<UserProject> queryUserProjectAppList(Project project) {
        try {
            String appName = NocalhostAppQueryManager.getInstance(project).get();
            String url = Config.getProperty("cicd_url") + "/b/api/v1/idehub/user-apps";
            if (StringUtils.isNotEmpty(appName)) {
                url = url + "?name=" + appName;
            }
            // 构建HttpRequest
            Response response = getHttpResponse(url);
            // 获取响应体
            String responseBody = response.body() != null ? response.body().string() : "";
            // 获取响应状态码
            if (dealErrorCode(project, response.code(), responseBody)) return null;

            QueryUserAppResponse apiResponse = JSON.parseObject(responseBody, QueryUserAppResponse.class);

            List<UserProject> projectList = apiResponse.getData();
            projectList = projectList.stream()
                    .filter(userProject -> null != userProject.getApps() && !userProject.getApps().isEmpty()).toList();

            updateFakerRunning(project, projectList);

            NocalhostAppListManager.getInstance(project).set(projectList);
            return projectList;
        } catch (Exception ex) {
            // ErrorUtil.dealWith(project, "查询服务列表异常", "查询服务列表异常", ex);
        }
        return null;
    }

    private static void updateFakerRunning(Project project, List<UserProject> projectList) {
        List<RunningApp> runningApps = NocalhostAppListManager.getInstance(project).getRunningAppList();
        if (null != runningApps && !runningApps.isEmpty()) {

            List<RunningApp> notRunningApps = new ArrayList<>();

            for (UserProject userProject : projectList) {
                for (UserApp app : userProject.getApps()) {
                    for (RunningApp runningApp : runningApps) {
                        if (app.getId() == runningApp.getUserApp().getId()) {
                            if (!Constants.DEBUG_STATUS_PENDING.equals(app.getStatus())
                                    && !Constants.DEBUG_STATUS_DEPLOYING.equals(app.getStatus())) {
                                notRunningApps.add(runningApp);
                            }
                        }
                    }
                }
            }
            runningApps.removeAll(notRunningApps);
            NocalhostAppListManager.getInstance(project).setRunningApps(runningApps);
        }

    }

    public UserAppConfigs queryUserAppConfigs(Project project, UserAppConfigsRequest appConfigsRequest) {
        try {
            String url = Config.getProperty("cicd_url") + "/b/api/v1/idehub/user-configs/app";
            url += "?appId=" + appConfigsRequest.getAppId();
            url += "&cluster=" + appConfigsRequest.getCluster();
            url += "&namespace=" + appConfigsRequest.getNamespace();
            url += "&appName=" + appConfigsRequest.getAppName();
            // 构建HttpRequest
            Response response = getHttpResponse(url);
            // 获取响应体
            String responseBody = response.body() != null ? response.body().string() : "";
            // 获取响应状态码
            if (dealErrorCode(project, response.code(), responseBody)) return null;
            UserAppConfigsResponse appConfigsResponse = JSON.parseObject(responseBody, UserAppConfigsResponse.class);
            return appConfigsResponse.getData();
        } catch (Exception ex) {
            ErrorUtil.dealWith(project, "查询服务配置异常", "查询服务配置异常", ex);
        }
        return null;
    }


    public void updateUserAppConfigs(Project project, UserAppConfigs userAppConfigs) {
        try {
            String url = Config.getProperty("cicd_url") + "/b/api/v1/idehub/user-configs/app";
            NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
            String userToken = settings.getUserToken();

            // 构建请求体
            MediaType JSONMediaType = MediaType.parse("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(ByteString.encodeString(JSON.toJSONString(userAppConfigs),
                    java.nio.charset.StandardCharsets.UTF_8), JSONMediaType);

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", userToken)
                    .post(body)
                    .build();

            // 发送请求并处理响应
            Response response = client.newCall(request).execute();

            // 获取响应状态码
            assert response.body() != null;
            String responseBody = response.body().string();
            if (!dealErrorCode(project, response.code(), responseBody)) {
                Notifications.showNotification(project, Constants.API_NOTIFICATION_GROUP
                        , Constants.LOCAL_DIR_SET_SUCCESS, StringUtils.EMPTY, NotificationType.INFORMATION);
            }
        } catch (Exception ex) {
            ErrorUtil.dealWith(project, "更新服务配置异常", "更新服务配置异常", ex);
        }
    }

    public Nhctl queryNhctlSetting(Project project) {
        try {
            // 构建HttpRequest
            Response response = getHttpResponse(Config.getProperty("cicd_url")
                    + "/b/api/v1/idehub/nhctl/setting?pluginType=1&version=" + Config.getProperty("version"));
            // 获取响应体
            String responseBody = response.body() != null ? response.body().string() : "";
            // 获取响应状态码
            if (dealErrorCode(project, response.code(), responseBody)) return null;
            QueryNhctlSettingResponse settingResponse = JSON.parseObject(responseBody, QueryNhctlSettingResponse.class);
            return settingResponse.getData().getNhctl();
        } catch (Exception e) {
            ErrorUtil.dealWith(project, "查询Nhctl配置异常", "查询Nhctl配置异常", e);
        }
        return null;
    }

    private Response getHttpResponse(String url) throws IOException {
        NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", settings.getUserToken())
                .get()
                .build();

        // 发送请求并处理响应
        return client.newCall(request).execute();
    }

    public static boolean dealErrorCode(Project project, int statusCode, String data) {

        if (statusCode == 200) {

            return false;

        } else if (statusCode == 401) {
            NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
            settings.cleanUserToken();

            ApplicationManager.getApplication().invokeLater(
                    () -> Notifications.showNotification(
                            project,
                            Constants.LOGIN_NOTIFICATION_GROUP,
                            Constants.LOGIN_EXPIRE,
                            Constants.LOGIN_EXPIRE_CONTENT,
                            NotificationType.ERROR
                    ));

            ApplicationManager.getApplication().invokeLater(
                    () -> ApplicationManager.getApplication().getMessageBus()
                            .syncPublisher(RefreshToolWindowNotifier.REFRESH_TOOL_WINDOW_TOPIC).refresh());
            return true;

        } else if (statusCode == 403) {

            ApplicationManager.getApplication().invokeLater(
                    () -> Notifications.showNotification(
                            project,
                            Constants.AUTH_NOTIFICATION_GROUP,
                            Constants.USER_NO_AUTH,
                            StringUtils.EMPTY,
                            NotificationType.ERROR
                    ));

            return true;

        } else if (statusCode == 400) {
            try {
                IdeHubResponseHeader responseHeader = JSON.parseObject(data, IdeHubResponseHeader.class);
                ApplicationManager.getApplication().invokeLater(
                        () -> Notifications.showNotification(
                                project,
                                Constants.API_NOTIFICATION_GROUP,
                                responseHeader.getMsg(),
                                StringUtils.EMPTY,
                                NotificationType.ERROR
                        ));
            } catch (Exception e) {
                originMessage(project, data);
            }
            return true;

        } else {
            originMessage(project, data);
            return true;
        }
    }

    private static void originMessage(Project project, String data) {
        ApplicationManager.getApplication().invokeLater(
                () -> Notifications.showNotification(
                        project,
                        Constants.API_NOTIFICATION_GROUP,
                        Constants.API_ERROR,
                        data,
                        NotificationType.ERROR
                ));
    }

}
