package com.sentinel.nocalhost.api;

import com.alibaba.fastjson.JSON;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.Config;
import com.sentinel.nocalhost.api.data.*;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.CodeReviewSuggestionListManager;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.topic.RefreshToolWindowNotifier;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.utils.ErrorUtil;
import okhttp3.*;
import okio.ByteString;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class CodeReviewAPI {
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(6, TimeUnit.SECONDS)
            .writeTimeout(6, TimeUnit.SECONDS)
            .readTimeout(6, TimeUnit.SECONDS)
            .hostnameVerifier((hostName, session) -> true)
            .retryOnConnectionFailure(true)
            .build();

    public List<TrafficMark> queryTrafficMarks(Project project) {
        try {
            String url = Config.getProperty("intelliforge_url") + "/api/v1/use-jet-traffic-marks";
            NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + settings.getIntelliforgeToken())
                    .get()
                    .build();
            // 发送请求并处理响应
            Response response = client.newCall(request).execute();
            // 获取响应状态码
            String responseBody = response.body() != null ? response.body().string() : "";
            // 获取响应状态码
            if (dealErrorCode(project, response.code(), responseBody)) new ArrayList<>();
            TrafficMarkResponse trafficMarkResponse = JSON.parseObject(responseBody, TrafficMarkResponse.class);
            return trafficMarkResponse.getData().getList();
        } catch (Exception ignored) {

        }
        return new ArrayList<>();
    }

    public boolean queryReviewServerStatus(Project project) {
        try {
            String url = Config.getProperty("code_review_url") + "/probe/health";
            String responseBody = getResponseBody(url, project);
            CodeReviewStatus codeReviewStatus = JSON.parseObject(responseBody, CodeReviewStatus.class);
            if (codeReviewStatus.getMessage().toLowerCase(Locale.ROOT).contains("ok")) {
                return false;
            }
        } catch (Exception ignored) {

        }
        return true;
    }

    public int executeCodeReview(Project project) {
        try {
            long startTime = System.currentTimeMillis();
            String url = Config.getProperty("code_review_url") + "/review/reviews";
            NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);

            // 构建请求体
            MediaType JSONMediaType = MediaType.parse("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(ByteString.encodeString(JSON.toJSONString(new CodeReviewRequest(project.getBasePath(), settings.getSelectedModel())),
                    java.nio.charset.StandardCharsets.UTF_8), JSONMediaType);

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .header("Cicd-Authorization", settings.getUserToken())
                    .build();

            // 发送请求并处理响应
            Response response = client.newCall(request).execute();

            // 获取响应状态码
            assert response.body() != null;
            String responseBody = response.body().string();
            long endTime = System.currentTimeMillis();
            System.out.println("出发review请求耗时：" + (endTime - startTime) + "毫秒");
            if (!IdeHubAPI.dealErrorCode(project, response.code(), responseBody)) {
                CodeReviewResponse codeReviewResponse = JSON.parseObject(responseBody, CodeReviewResponse.class);
                if (codeReviewResponse.getCode() != 0) {
                    Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                            "执行Code Review异常", codeReviewResponse.getMsg(), NotificationType.ERROR);
                }
                return codeReviewResponse.getData().getReview_id();
            }
        } catch (Exception ex) {
            ErrorUtil.dealWith(project, "执行Code Review异常", "执行Code Review异常", ex);
        }
        return 0;
    }

    public void checkCodeReview(Project project, FeedbackCodeReviewRequest feedbackCodeReviewRequest) {
        try {
            String url = Config.getProperty("code_review_url") + "/review/feedback";
            NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);

            // 构建请求体
            MediaType JSONMediaType = MediaType.parse("application/json; charset=utf-8");
            RequestBody body = RequestBody.create(ByteString.encodeString(JSON.toJSONString(feedbackCodeReviewRequest),
                    java.nio.charset.StandardCharsets.UTF_8), JSONMediaType);

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .header("Cicd-Authorization", settings.getUserToken())
                    .build();

            // 发送请求并处理响应
            Response response = client.newCall(request).execute();

            // 获取响应状态码
            assert response.body() != null;
            String responseBody = response.body().string();

            CodeReviewFeedbackResponse codeReviewFeedbackResponse = JSON.parseObject(responseBody, CodeReviewFeedbackResponse.class);
            if (codeReviewFeedbackResponse.getCode() != 0) {
                Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                        "反馈异常", codeReviewFeedbackResponse.getMsg(), NotificationType.ERROR);
            }
        } catch (Exception ex) {
            ErrorUtil.dealWith(project, "反馈异常", "反馈异常", ex);
        }
    }

    public String queryReviewingTask(Project project) {
        try {
            // 构建HttpRequest
            NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
            String url = Config.getProperty("code_review_url") + "/review?model=" + settings.getSelectedModel() + "&repo_path=" + project.getBasePath();
            String responseBody = getResponseBody(url, project);
            QueryReviewingTaskResponse queryReviewingTaskResponse = JSON.parseObject(responseBody, QueryReviewingTaskResponse.class);

            if (queryReviewingTaskResponse.getCode() == 10000) {
                return "noChange";
            }
            if (queryReviewingTaskResponse.getCode() != 0) {
                Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                        "查询是否存在相同任务异常", queryReviewingTaskResponse.getMsg(), NotificationType.ERROR);
            }
            return queryReviewingTaskResponse.getData().getStatus();
        } catch (Exception e) {
            // ErrorUtil.dealWith(project, "查询是否存在相同任务异常", "查询是否存在相同任务异常", e);
        }
        return null;
    }

    public List<CodeReviewTaskInfo> queryCodeReviewTaskList(Project project) {
        try {
            long startTime = System.currentTimeMillis();
            String url = Config.getProperty("code_review_url") + "/review/reviews?repo_path=" + project.getBasePath();
            String responseBody = getResponseBody(url, project);
            long endTime = System.currentTimeMillis();
            System.out.println("查询历史记录耗时：" + (endTime - startTime));
            QueryCodeReviewTaskResponse queryCodeReviewTaskResponse = JSON.parseObject(responseBody, QueryCodeReviewTaskResponse.class);

            if (queryCodeReviewTaskResponse.getCode() != 0) {
                Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                        "查询历史记录异常", queryCodeReviewTaskResponse.getMsg(), NotificationType.ERROR);
            }
            CodeReviewTaskManager codeReviewTaskManager = CodeReviewTaskManager.getInstance(project);
            if (null != codeReviewTaskManager && null != codeReviewTaskManager.get()) {
                for (CodeReviewTaskInfo codeReviewTaskInfo : queryCodeReviewTaskResponse.getData()) {
                    if (codeReviewTaskInfo.getId() == codeReviewTaskManager.get().getId()) {
                        codeReviewTaskManager.get().setStatus(codeReviewTaskInfo.getStatus());
                        break;
                    }
                }
            }
            return queryCodeReviewTaskResponse.getData();
        } catch (Exception e) {
            // ErrorUtil.dealWith(project, "查询历史记录异常", "查询历史记录异常", e);
        }
        return new ArrayList<>();
    }

    public void querySuggestionList(Project project) {
        try {
            long startTime = System.currentTimeMillis();
            CodeReviewTaskManager codeReviewTaskManager = CodeReviewTaskManager.getInstance(project);
            if (null == codeReviewTaskManager || null == codeReviewTaskManager.get()) {
                return;
            }
            String url = Config.getProperty("code_review_url") + "/review/reviews/" + codeReviewTaskManager.get().getId() + "/issues";
            String responseBody = getResponseBody(url, project);
            long endTime = System.currentTimeMillis();
            System.out.println("查询建议列表耗时：" + (endTime - startTime));
            QueryCodeReviewSuggestionResponse queryCodeReviewSuggestionResponse = JSON.parseObject(responseBody, QueryCodeReviewSuggestionResponse.class);

            if (queryCodeReviewSuggestionResponse.getCode() != 0) {
                Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                        "查询建议列表异常", queryCodeReviewSuggestionResponse.getMsg(), NotificationType.ERROR);
            }

            CodeReviewSuggestionListManager codeReviewSuggestionListManager = CodeReviewSuggestionListManager.getInstance(project);
            if (null != codeReviewSuggestionListManager) {
                codeReviewSuggestionListManager.set(queryCodeReviewSuggestionResponse.getData());
            }
        } catch (Exception e) {
            // ErrorUtil.dealWith(project, "查询建议列表异常", "查询建议列表异常", e);
        }
    }

    public Map<String, String> queryRejectReasonList(Project project) {
        try {
            CodeReviewTaskManager codeReviewTaskManager = CodeReviewTaskManager.getInstance(project);
            if (null == codeReviewTaskManager || null == codeReviewTaskManager.get()) {
                return new HashMap<>();
            }
            String url = Config.getProperty("code_review_url") + "/review/reasons";
            String responseBody = getResponseBody(url, project);
            QueryRejectReasonListResponse queryRejectReasonListResponse = JSON.parseObject(responseBody, QueryRejectReasonListResponse.class);

            if (queryRejectReasonListResponse.getCode() != 0) {
                Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                        "查询拒绝原因列表异常", queryRejectReasonListResponse.getMsg(), NotificationType.ERROR);
            }
            queryRejectReasonListResponse.getData().put("OTHER", "其他");
            return queryRejectReasonListResponse.getData();
        } catch (IOException e) {
            // ErrorUtil.dealWith(project, "查询拒绝原因列表异常", "查询拒绝原因列表异常", e);
        }
        return new HashMap<>();
    }


    public List<String> queryReviewModelList(Project project) {
        try {
            String url = Config.getProperty("code_review_url") + "/model/list";
            String responseBody = getResponseBody(url, project);
            QueryReviewModelListResponse queryRejectReasonListResponse = JSON.parseObject(responseBody, QueryReviewModelListResponse.class);

            if (null != queryRejectReasonListResponse) {
                if (queryRejectReasonListResponse.getCode() != 0) {
                    Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                            "查询模型列表异常", queryRejectReasonListResponse.getMsg(), NotificationType.ERROR);
                    return new ArrayList<>();
                }
                return queryRejectReasonListResponse.getData();
            }
        } catch (Exception e) {
            ErrorUtil.dealWith(project, "查询模型列表异常", "查询模型列表异常", e);
        }
        return new ArrayList<>();
    }


    private String getResponseBody(String url, Project project) throws IOException {
        NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
        Request request = new Request.Builder()
                .url(url)
                .header("Cicd-Authorization", settings.getUserToken())
                .get()
                .build();
        // 发送请求并处理响应
        Response response = client.newCall(request).execute();
        // 获取响应状态码
        String responseBody = response.body() != null ? response.body().string() : "";
        // 获取响应状态码
        if (dealErrorCode(project, response.code(), responseBody)) {
            return "";
        }
        return responseBody;
    }


    public static boolean dealErrorCode(Project project, int statusCode, String data) {

        if (statusCode == 200) {

            return false;

        } else if (statusCode == 401) {
            NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
            settings.cleanUserToken();

            ApplicationManager.getApplication().invokeLater(
                    () -> Notifications.showNotification(
                            project,
                            Constants.LOGIN_NOTIFICATION_GROUP,
                            Constants.LOGIN_EXPIRE,
                            Constants.LOGIN_EXPIRE_CONTENT,
                            NotificationType.ERROR
                    ));

            ApplicationManager.getApplication().invokeLater(
                    () -> ApplicationManager.getApplication().getMessageBus()
                            .syncPublisher(RefreshToolWindowNotifier.REFRESH_TOOL_WINDOW_TOPIC).refresh());
            return true;

        } else if (statusCode == 403) {

            ApplicationManager.getApplication().invokeLater(
                    () -> Notifications.showNotification(
                            project,
                            Constants.AUTH_NOTIFICATION_GROUP,
                            Constants.USER_NO_AUTH,
                            StringUtils.EMPTY,
                            NotificationType.ERROR
                    ));

            return true;

        } else if (statusCode == 400) {
            try {
                IdeHubResponseHeader responseHeader = JSON.parseObject(data, IdeHubResponseHeader.class);
                ApplicationManager.getApplication().invokeLater(
                        () -> Notifications.showNotification(
                                project,
                                Constants.API_NOTIFICATION_GROUP,
                                responseHeader.getMsg(),
                                StringUtils.EMPTY,
                                NotificationType.ERROR
                        ));
            } catch (Exception e) {
                originMessage(project, data);
            }
            return true;

        }
        return true;
    }

    private static void originMessage(Project project, String data) {
        ApplicationManager.getApplication().invokeLater(
                () -> Notifications.showNotification(
                        project,
                        Constants.API_NOTIFICATION_GROUP,
                        Constants.API_ERROR,
                        data,
                        NotificationType.ERROR
                ));
    }

}
