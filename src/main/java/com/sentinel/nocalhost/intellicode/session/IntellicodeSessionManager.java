package com.sentinel.nocalhost.intellicode.session;

import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.intellicode.api.IntellicodeSSEListener;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Intellicode 会话管理器
 * 负责管理 SSE 会话的生命周期、状态持久化和事件历史
 */
@Service(Service.Level.PROJECT)
public final class IntellicodeSessionManager {
    
    private static final Logger LOG = Logger.getInstance(IntellicodeSessionManager.class);
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final Map<String, SessionInfo> activeSessions = new ConcurrentHashMap<>();
    private final List<SessionEvent> sessionHistory = new CopyOnWriteArrayList<>();
    private final Map<String, List<SessionEvent>> sessionEvents = new ConcurrentHashMap<>();
    
    private final Project project;
    
    public IntellicodeSessionManager(@NotNull Project project) {
        this.project = project;
        LOG.info("IntellicodeSessionManager initialized for project: " + project.getName());
    }
    
    public static IntellicodeSessionManager getInstance(@NotNull Project project) {
        return project.getService(IntellicodeSessionManager.class);
    }
    
    /**
     * 创建新的会话
     */
    public SessionInfo createSession(@NotNull List<String> reviewFiles) {
        String sessionId = generateSessionId();
        SessionInfo sessionInfo = new SessionInfo(
            sessionId,
            reviewFiles,
            SessionStatus.CREATED,
            LocalDateTime.now()
        );
        
        activeSessions.put(sessionId, sessionInfo);
        sessionEvents.put(sessionId, new CopyOnWriteArrayList<>());
        
        addSessionEvent(sessionId, SessionEventType.SESSION_CREATED, 
            "Session created with " + reviewFiles.size() + " files", null);
        
        LOG.info("Created new session: " + sessionId + " for " + reviewFiles.size() + " files");
        return sessionInfo;
    }
    
    /**
     * 启动会话
     */
    public void startSession(@NotNull String sessionId, @NotNull List<String> reviewFiles) {
        SessionInfo session = activeSessions.get(sessionId);
        if (session != null) {
            session.status = SessionStatus.ACTIVE;
            session.startTime = LocalDateTime.now();
            
            addSessionEvent(sessionId, SessionEventType.SESSION_STARTED, 
                "Session started with files: " + String.join(", ", reviewFiles), null);
            
            LOG.info("Session started: " + sessionId);
        }
    }
    
    /**
     * 结束会话
     */
    public void endSession(@NotNull String sessionId, boolean success) {
        SessionInfo session = activeSessions.get(sessionId);
        if (session != null) {
            session.status = success ? SessionStatus.COMPLETED : SessionStatus.FAILED;
            session.endTime = LocalDateTime.now();
            
            addSessionEvent(sessionId, SessionEventType.SESSION_ENDED, 
                "Session ended with status: " + (success ? "SUCCESS" : "FAILED"), null);
            
            LOG.info("Session ended: " + sessionId + ", success: " + success);
        }
    }
    
    /**
     * 记录工具开始事件
     */
    public void recordToolStart(@NotNull String sessionId, @NotNull String toolName, @NotNull String input) {
        addSessionEvent(sessionId, SessionEventType.TOOL_STARTED, 
            "Tool started: " + toolName, Map.of("tool_name", toolName, "input", input));
    }
    
    /**
     * 记录工具结束事件
     */
    public void recordToolEnd(@NotNull String sessionId, @NotNull String toolName, 
                             @NotNull String output, boolean success) {
        addSessionEvent(sessionId, SessionEventType.TOOL_ENDED, 
            "Tool ended: " + toolName + " (" + (success ? "SUCCESS" : "FAILED") + ")", 
            Map.of("tool_name", toolName, "output", output, "success", String.valueOf(success)));
    }
    
    /**
     * 记录助手消息事件
     */
    public void recordAssistantMessage(@NotNull String sessionId, @NotNull String message) {
        addSessionEvent(sessionId, SessionEventType.ASSISTANT_MESSAGE, 
            "Assistant message: " + (message.length() > 50 ? message.substring(0, 50) + "..." : message), 
            Map.of("message", message));
    }
    
    /**
     * 记录建议事件
     */
    public void recordSuggestions(@NotNull String sessionId, @NotNull List<intellicode.event.v1.EventOuterClass.Suggestion> suggestions) {
        addSessionEvent(sessionId, SessionEventType.SUGGESTIONS_RECEIVED, 
            "Received " + suggestions.size() + " suggestions", 
            Map.of("suggestion_count", String.valueOf(suggestions.size())));
    }
    
    /**
     * 记录错误事件
     */
    public void recordError(@NotNull String sessionId, @NotNull Throwable error) {
        SessionInfo session = activeSessions.get(sessionId);
        if (session != null) {
            session.status = SessionStatus.FAILED;
            session.lastError = error.getMessage();
        }
        
        addSessionEvent(sessionId, SessionEventType.ERROR_OCCURRED, 
            "Error: " + error.getMessage(), 
            Map.of("error_type", error.getClass().getSimpleName(), "error_message", error.getMessage()));
    }
    
    /**
     * 获取会话信息
     */
    @Nullable
    public SessionInfo getSession(@NotNull String sessionId) {
        return activeSessions.get(sessionId);
    }
    
    /**
     * 获取所有活跃会话
     */
    @NotNull
    public List<SessionInfo> getActiveSessions() {
        return new ArrayList<>(activeSessions.values());
    }
    
    /**
     * 获取会话事件历史
     */
    @NotNull
    public List<SessionEvent> getSessionEvents(@NotNull String sessionId) {
        return new ArrayList<>(sessionEvents.getOrDefault(sessionId, Collections.emptyList()));
    }
    
    /**
     * 获取全局事件历史
     */
    @NotNull
    public List<SessionEvent> getGlobalHistory() {
        return new ArrayList<>(sessionHistory);
    }
    
    /**
     * 清理已完成的会话
     */
    public void cleanupCompletedSessions() {
        List<String> toRemove = new ArrayList<>();
        
        for (Map.Entry<String, SessionInfo> entry : activeSessions.entrySet()) {
            SessionInfo session = entry.getValue();
            if (session.status == SessionStatus.COMPLETED || session.status == SessionStatus.FAILED) {
                // 保留最近1小时内的会话
                if (session.endTime != null && 
                    session.endTime.isBefore(LocalDateTime.now().minusHours(1))) {
                    toRemove.add(entry.getKey());
                }
            }
        }
        
        for (String sessionId : toRemove) {
            activeSessions.remove(sessionId);
            sessionEvents.remove(sessionId);
            LOG.debug("Cleaned up session: " + sessionId);
        }
        
        if (!toRemove.isEmpty()) {
            LOG.info("Cleaned up " + toRemove.size() + " completed sessions");
        }
    }
    
    /**
     * 添加会话事件
     */
    private void addSessionEvent(@NotNull String sessionId, @NotNull SessionEventType eventType, 
                                @NotNull String description, @Nullable Map<String, String> metadata) {
        SessionEvent event = new SessionEvent(
            sessionId,
            eventType,
            description,
            LocalDateTime.now(),
            metadata != null ? new HashMap<>(metadata) : new HashMap<>()
        );
        
        sessionHistory.add(event);
        sessionEvents.computeIfAbsent(sessionId, k -> new CopyOnWriteArrayList<>()).add(event);
        
        // 限制历史记录大小
        if (sessionHistory.size() > 1000) {
            sessionHistory.subList(0, 100).clear();
        }
    }
    
    /**
     * 生成会话 ID
     */
    private String generateSessionId() {
        return "session_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString(new Random().nextInt());
    }
    
    /**
     * 会话信息
     */
    public static class SessionInfo {
        public final String sessionId;
        public final List<String> reviewFiles;
        public SessionStatus status;
        public final LocalDateTime createdTime;
        public LocalDateTime startTime;
        public LocalDateTime endTime;
        public String lastError;
        
        public SessionInfo(@NotNull String sessionId, @NotNull List<String> reviewFiles, 
                          @NotNull SessionStatus status, @NotNull LocalDateTime createdTime) {
            this.sessionId = sessionId;
            this.reviewFiles = new ArrayList<>(reviewFiles);
            this.status = status;
            this.createdTime = createdTime;
        }
        
        public String getFormattedCreatedTime() {
            return createdTime.format(TIMESTAMP_FORMAT);
        }
        
        public String getFormattedStartTime() {
            return startTime != null ? startTime.format(TIMESTAMP_FORMAT) : "N/A";
        }
        
        public String getFormattedEndTime() {
            return endTime != null ? endTime.format(TIMESTAMP_FORMAT) : "N/A";
        }
        
        public long getDurationSeconds() {
            if (startTime == null) return 0;
            LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
            return java.time.Duration.between(startTime, end).getSeconds();
        }
    }
    
    /**
     * 会话状态枚举
     */
    public enum SessionStatus {
        CREATED,
        ACTIVE,
        COMPLETED,
        FAILED
    }
    
    /**
     * 会话事件
     */
    public static class SessionEvent {
        public final String sessionId;
        public final SessionEventType eventType;
        public final String description;
        public final LocalDateTime timestamp;
        public final Map<String, String> metadata;
        
        public SessionEvent(@NotNull String sessionId, @NotNull SessionEventType eventType, 
                           @NotNull String description, @NotNull LocalDateTime timestamp, 
                           @NotNull Map<String, String> metadata) {
            this.sessionId = sessionId;
            this.eventType = eventType;
            this.description = description;
            this.timestamp = timestamp;
            this.metadata = metadata;
        }
        
        public String getFormattedTimestamp() {
            return timestamp.format(TIMESTAMP_FORMAT);
        }
    }
    
    /**
     * 会话事件类型枚举
     */
    public enum SessionEventType {
        SESSION_CREATED,
        SESSION_STARTED,
        SESSION_ENDED,
        TOOL_STARTED,
        TOOL_ENDED,
        ASSISTANT_MESSAGE,
        SUGGESTIONS_RECEIVED,
        ERROR_OCCURRED
    }
}