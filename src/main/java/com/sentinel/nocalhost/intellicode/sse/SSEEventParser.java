package com.sentinel.nocalhost.intellicode.sse;

import com.google.protobuf.util.JsonFormat;
import com.intellij.openapi.diagnostic.Logger;
import intellicode.event.v1.EventOuterClass.*;
import com.sentinel.nocalhost.intellicode.api.IntellicodeSSEListener;
import com.sentinel.nocalhost.intellicode.api.IntellicodeException;

import java.util.ArrayList;
import java.util.List;

/**
 * SSE 事件解析器
 * 使用 protobuf JsonFormat 解析 SSE 事件数据并分发到相应的监听器方法
 */
public class SSEEventParser {
    
    private static final Logger LOG = Logger.getInstance(SSEEventParser.class);
    
    /**
     * 解析并分发 SSE 事件
     * 
     * @param eventType 事件类型
     * @param eventData 事件数据（JSON 格式）
     * @param listener 事件监听器
     * @throws IntellicodeException 解析异常
     */
    public void parseAndDispatch(String eventType, String eventData, IntellicodeSSEListener listener) 
            throws IntellicodeException {
        
        if (eventType == null || eventData == null || listener == null) {
            throw new IntellicodeException(
                "Invalid event parameters: eventType=" + eventType + ", eventData=" + eventData,
                IntellicodeException.ErrorType.SSE_ERROR
            );
        }
        
        
        try {
            switch (eventType) {
                case "session_start":
                    parseSessionStart(eventData, listener);
                    break;
                    
                case "session_end":
                    parseSessionEnd(eventData, listener);
                    break;
                    
                case "tool_start":
                    parseToolStart(eventData, listener);
                    break;
                    
                case "tool_end":
                    parseToolEnd(eventData, listener);
                    break;
                    
                case "assistant":
                    parseAssistantMessage(eventData, listener);
                    break;
                    
                case "suggestions":
                    parseSuggestions(eventData, listener);
                    break;
                    
                default:
                    LOG.warn("Unknown SSE event type: " + eventType);
                    break;
            }
        } catch (Exception e) {
            throw new IntellicodeException(
                "Failed to parse SSE event: " + e.getMessage(),
                IntellicodeException.ErrorType.SSE_ERROR,
                e
            );
        }
    }
    
    /**
     * 解析会话开始事件
     */
    private void parseSessionStart(String eventData, IntellicodeSSEListener listener) throws Exception {
        
        SessionStartData.Builder builder = SessionStartData.newBuilder();
        JsonFormat.parser().merge(eventData, builder);
        SessionStartData sessionStartData = builder.build();
        
        String sessionId = sessionStartData.getSessionId();
        List<String> reviewFiles = new ArrayList<>(sessionStartData.getReviewFilesList());
        
        listener.onSessionStart(sessionId, reviewFiles);
    }
    
    /**
     * 解析会话结束事件
     */
    private void parseSessionEnd(String eventData, IntellicodeSSEListener listener) throws Exception {
        
        SessionEndData.Builder builder = SessionEndData.newBuilder();
        JsonFormat.parser().merge(eventData, builder);
        SessionEndData sessionEndData = builder.build();
        
        String sessionId = sessionEndData.getSessionId();
        boolean success = sessionEndData.getSuccess();
        
        listener.onSessionEnd(sessionId, success);
    }
    
    /**
     * 解析工具开始事件
     */
    private void parseToolStart(String eventData, IntellicodeSSEListener listener) throws Exception {
        
        ToolStartData.Builder builder = ToolStartData.newBuilder();
        JsonFormat.parser().merge(eventData, builder);
        ToolStartData toolStartData = builder.build();
        
        String name = toolStartData.getName();
        String input = toolStartData.getInput();
        
        listener.onToolStart(name, input);
    }
    
    /**
     * 解析工具结束事件
     */
    private void parseToolEnd(String eventData, IntellicodeSSEListener listener) throws Exception {
        
        ToolEndData.Builder builder = ToolEndData.newBuilder();
        JsonFormat.parser().merge(eventData, builder);
        ToolEndData toolEndData = builder.build();
        
        String name = toolEndData.getName();
        String output = toolEndData.getOutput();
        ToolStatus status = toolEndData.getStatus();
        boolean success = status == ToolStatus.TOOL_STATUS_SUCCESS;
        
        
        listener.onToolEnd(name, output, success);
    }
    
    /**
     * 解析助手消息事件
     */
    private void parseAssistantMessage(String eventData, IntellicodeSSEListener listener) throws Exception {
        
        AssistantData.Builder builder = AssistantData.newBuilder();
        JsonFormat.parser().merge(eventData, builder);
        AssistantData assistantData = builder.build();
        
        String chunk = assistantData.getChunk();
        
        if (chunk != null && !chunk.isEmpty()) {
            listener.onAssistantMessage(chunk);
        }
    }
    
    /**
     * 解析建议事件
     */
    private void parseSuggestions(String eventData, IntellicodeSSEListener listener) throws Exception {
        
        SuggestionsData.Builder builder = SuggestionsData.newBuilder();
        JsonFormat.parser().merge(eventData, builder);
        SuggestionsData suggestionsData = builder.build();
        
        List<Suggestion> suggestions = new ArrayList<>(suggestionsData.getSuggestionsList());
        
        for (Suggestion suggestion : suggestions) {
        }
        
        listener.onSuggestions(suggestions);
    }
}