package com.sentinel.nocalhost.intellicode.sse;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.notification.NotificationType;
import com.sentinel.nocalhost.intellicode.api.IntellicodeSSEListener;
import com.sentinel.nocalhost.intellicode.api.IntellicodeException;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.topic.RefreshToolWindowNotifier;
import org.apache.commons.lang3.StringUtils;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * SSE 客户端实现
 * 负责建立和管理 SSE 连接，解析事件流
 */
public class IntellicodeSSEClient {
    
    private static final Logger LOG = Logger.getInstance(IntellicodeSSEClient.class);
    
    private static final int DEFAULT_CONNECT_TIMEOUT = 30;
    private static final int DEFAULT_READ_TIMEOUT = 300; // SSE 长连接
    private static final int DEFAULT_WRITE_TIMEOUT = 30;
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 5000;
    
    private final OkHttpClient httpClient;
    private final SSEEventParser eventParser;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicReference<Call> currentCall = new AtomicReference<>();
    
    public IntellicodeSSEClient() {
        this.httpClient = createHttpClient();
        this.eventParser = new SSEEventParser();
    }
    
    /**
     * 创建配置化的 HTTP 客户端
     */
    private OkHttpClient createHttpClient() {
        return new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .addInterceptor(new SSELoggingInterceptor())
            .build();
    }
    
    /**
     * 建立 SSE 连接并开始监听事件
     * 
     * @param url 服务端点 URL
     * @param authToken 认证令牌
     * @param requestBody 请求体
     * @param listener 事件监听器
     * @param project 项目实例，用于认证错误处理
     * @throws IntellicodeException 连接或解析异常
     */
    public void connect(String url, String authToken, String requestBody, IntellicodeSSEListener listener, Project project) 
            throws IntellicodeException {
        
        if (isConnected.get()) {
            throw new IntellicodeException(
                "SSE connection already established",
                IntellicodeException.ErrorType.NETWORK_ERROR
            );
        }
        
        Request request = new Request.Builder()
            .url(url)
            .post(RequestBody.create(requestBody, MediaType.get("application/json; charset=utf-8")))
            .addHeader("Accept", "text/event-stream")
            .addHeader("Cicd-Authorization", authToken)
            .addHeader("User-Agent", "JetDev-Plugin/1.0")
            .addHeader("Cache-Control", "no-cache")
            .build();
        
        connectWithRetry(request, listener, project, 0);
    }
    
    /**
     * 带重试机制的连接
     */
    private void connectWithRetry(Request request, IntellicodeSSEListener listener, Project project, int attemptCount) {
        if (attemptCount >= MAX_RETRY_ATTEMPTS) {
            listener.onError(new IntellicodeException(
                "Failed to establish SSE connection after " + MAX_RETRY_ATTEMPTS + " attempts",
                IntellicodeException.ErrorType.NETWORK_ERROR
            ));
            return;
        }
        
        Call call = httpClient.newCall(request);
        currentCall.set(call);
        
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                if (call.isCanceled()) {
                    return;
                }
                
                LOG.warn("SSE connection failed (attempt " + (attemptCount + 1) + "): " + e.getMessage());
                
                // 延迟重试
                if (attemptCount < MAX_RETRY_ATTEMPTS - 1) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                        connectWithRetry(request, listener, project, attemptCount + 1);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        listener.onError(ie);
                    }
                } else {
                    listener.onError(new IntellicodeException(
                        "Network connection failed: " + e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    ));
                }
            }
            
            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                if (!response.isSuccessful()) {
                    handleErrorResponse(response, listener, request, project, attemptCount);
                    return;
                }
                
                isConnected.set(true);
                
                try {
                    processSSEStream(response, listener);
                } catch (Exception e) {
                    LOG.error("Error processing SSE stream: " + e.getMessage(), e);
                    listener.onError(e instanceof IntellicodeException ? 
                        (IntellicodeException) e : 
                        new IntellicodeException(
                            "Failed to process SSE stream: " + e.getMessage(),
                            IntellicodeException.ErrorType.SSE_ERROR,
                            e
                        )
                    );
                } finally {
                    isConnected.set(false);
                    response.close();
                }
            }
        });
    }
    
    /**
     * 处理错误响应
     */
    private void handleErrorResponse(Response response, IntellicodeSSEListener listener, 
                                   Request request, Project project, int attemptCount) throws IOException {
        String errorBody = "";
        String contentType = "";
        
        try {
            if (response.body() != null) {
                errorBody = response.body().string();
                contentType = response.header("Content-Type", "unknown");
            }
        } catch (Exception e) {
            LOG.warn("Failed to read error response body: " + e.getMessage());
            errorBody = "[Failed to read response body: " + e.getMessage() + "]";
        }
        
        IntellicodeException.ErrorType errorType = mapHttpStatusToErrorType(response.code());
        boolean isRetryable = isRetryableError(response.code());
        
        // 处理认证错误，参考 CodeReviewAPI 的方式
        if (response.code() == 401) {
            handleAuthenticationError(project);
        }
        
        // 详细记录错误信息
        LOG.error("SSE connection failed with HTTP " + response.code() + " (" + response.message() + ")");
        LOG.error("Request URL: " + request.url());
        LOG.error("Request Method: " + request.method());
        LOG.error("Response Content-Type: " + contentType);
        LOG.error("Response Headers: " + response.headers());
        LOG.error("Response Body: " + errorBody);
        
        // 如果是400错误，打印请求体用于调试
        if (response.code() == 400) {
            try {
                if (request.body() != null) {
                }
            } catch (Exception e) {
            }
        }
        
        if (isRetryable && attemptCount < MAX_RETRY_ATTEMPTS - 1) {
            try {
                Thread.sleep(RETRY_DELAY_MS);
                connectWithRetry(request, listener, project, attemptCount + 1);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                listener.onError(new IntellicodeException(
                    "Connection retry interrupted",
                    IntellicodeException.ErrorType.NETWORK_ERROR,
                    ie
                ));
            }
        } else {
            String detailedError = String.format(
                "HTTP %d (%s): %s\nURL: %s\nContent-Type: %s",
                response.code(), response.message(), errorBody, request.url(), contentType
            );
            listener.onError(new IntellicodeException(
                detailedError,
                errorType
            ));
        }
    }
    
    /**
     * 将请求体转换为字符串用于调试
     */
    private String requestBodyToString(Request request) {
        try {
            RequestBody body = request.body();
            if (body == null) {
                return "[No request body]";
            }
            
            okio.Buffer buffer = new okio.Buffer();
            body.writeTo(buffer);
            return buffer.readUtf8();
        } catch (Exception e) {
            return "[Failed to read request body: " + e.getMessage() + "]";
        }
    }
    
    /**
     * 处理 SSE 事件流
     */
    private void processSSEStream(Response response, IntellicodeSSEListener listener) throws IOException, IntellicodeException {
        ResponseBody body = response.body();
        if (body == null) {
            throw new IntellicodeException(
                "Empty response body",
                IntellicodeException.ErrorType.SSE_ERROR
            );
        }
        
        try (BufferedReader reader = new BufferedReader(body.charStream())) {
            String line;
            StringBuilder eventData = new StringBuilder();
            String eventType = null;
            
            while ((line = reader.readLine()) != null && isConnected.get()) {
                if (line.isEmpty()) {
                    // 空行表示事件结束，处理累积的事件数据
                    if (eventType != null && eventData.length() > 0) {
                        try {
                            eventParser.parseAndDispatch(eventType, eventData.toString(), listener);
                        } catch (Exception e) {
                            LOG.warn("Failed to parse SSE event: " + e.getMessage(), e);
                            // 继续处理下一个事件，不中断连接
                        }
                    }
                    
                    // 重置状态
                    eventType = null;
                    eventData.setLength(0);
                    
                } else if (line.startsWith("event:")) {
                    eventType = line.substring(6).trim();
                    
                } else if (line.startsWith("data:")) {
                    String data = line.substring(5).trim();
                    if (eventData.length() > 0) {
                        eventData.append("\n");
                    }
                    eventData.append(data);
                    
                } else if (line.startsWith(":")) {
                    // 注释行，忽略
                }
            }
        }
    }
    
    /**
     * 断开 SSE 连接
     */
    public void disconnect() {
        isConnected.set(false);
        
        Call call = currentCall.get();
        if (call != null && !call.isCanceled()) {
            call.cancel();
        }
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected.get();
    }
    
    /**
     * 映射 HTTP 状态码到错误类型
     */
    private IntellicodeException.ErrorType mapHttpStatusToErrorType(int statusCode) {
        switch (statusCode) {
            case 400:
                return IntellicodeException.ErrorType.INVALID_REQUEST;
            case 401:
                return IntellicodeException.ErrorType.AUTHENTICATION_ERROR;
            case 403:
                return IntellicodeException.ErrorType.AUTHENTICATION_ERROR;
            case 404:
                return IntellicodeException.ErrorType.SERVICE_UNAVAILABLE;
            case 429:
                return IntellicodeException.ErrorType.RATE_LIMIT_EXCEEDED;
            case 500:
            case 502:
            case 503:
            case 504:
                return IntellicodeException.ErrorType.SERVER_ERROR;
            default:
                return IntellicodeException.ErrorType.UNKNOWN_ERROR;
        }
    }
    
    /**
     * 判断是否为可重试的错误
     */
    private boolean isRetryableError(int statusCode) {
        return statusCode >= 500 || statusCode == 429; // 服务器错误和限流错误可重试
    }
    
    /**
     * 处理认证错误，参考 CodeReviewAPI 的处理方式
     */
    private void handleAuthenticationError(Project project) {
        NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
        if (settings != null) {
            settings.cleanUserToken();
        }
        
        ApplicationManager.getApplication().invokeLater(() -> {
            Notifications.showNotification(project, Constants.LOGIN_NOTIFICATION_GROUP,
                Constants.LOGIN_EXPIRE, Constants.LOGIN_EXPIRE_CONTENT, 
                NotificationType.ERROR);
            
            ApplicationManager.getApplication().getMessageBus()
                .syncPublisher(RefreshToolWindowNotifier.REFRESH_TOOL_WINDOW_TOPIC)
                .refresh();
        });
    }
    
    /**
     * SSE 专用日志拦截器
     */
    private static class SSELoggingInterceptor implements Interceptor {
        @NotNull
        @Override
        public Response intercept(@NotNull Chain chain) throws IOException {
            Request request = chain.request();
            long startTime = System.currentTimeMillis();
            
            try {
                Response response = chain.proceed(request);
                long endTime = System.currentTimeMillis();
                
                return response;
            } catch (IOException e) {
                long endTime = System.currentTimeMillis();
                LOG.warn("SSE Request failed in " + (endTime - startTime) + "ms: " + e.getMessage());
                throw e;
            }
        }
    }
}