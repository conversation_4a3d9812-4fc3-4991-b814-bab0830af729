package com.sentinel.nocalhost.intellicode.ui.handler;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.popup.Balloon;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.ui.JBColor;
import com.intellij.ui.awt.RelativePoint;
import com.intellij.icons.AllIcons;
import com.sentinel.nocalhost.utils.CodeUtils;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.StringSelection;

/**
 * 处理建议的接受操作
 */
public class AcceptSuggestionHandler {
    private static final Logger LOG = Logger.getInstance(AcceptSuggestionHandler.class);
    
    private final Project project;
    
    public AcceptSuggestionHandler(Project project) {
        this.project = project;
    }
    
    /**
     * 接受建议并应用到文件
     */
    public void acceptSuggestion(intellicode.event.v1.EventOuterClass.Suggestion suggestion, 
                                JComponent sourceComponent,
                                Runnable onSuccess, 
                                Runnable onFailure) {
        
        try {
            // 构建文件路径
            String fullPath = project.getBasePath() + "/" + suggestion.getFilePath();
            VirtualFile virtualFile = LocalFileSystem.getInstance().findFileByPath(fullPath);
            
            if (virtualFile == null) {
                showError(sourceComponent, "文件未找到: " + suggestion.getFilePath(), suggestion);
                if (onFailure != null) onFailure.run();
                return;
            }
            
            // 获取 PsiFile
            PsiFile psiFile = PsiManager.getInstance(project).findFile(virtualFile);
            if (psiFile == null) {
                showError(sourceComponent, "无法访问文件: " + suggestion.getFilePath(), suggestion);
                if (onFailure != null) onFailure.run();
                return;
            }
            
            // 获取文档
            Document document = PsiDocumentManager.getInstance(project).getDocument(psiFile);
            if (document == null) {
                showError(sourceComponent, "无法获取文档: " + suggestion.getFilePath(), suggestion);
                if (onFailure != null) onFailure.run();
                return;
            }
            
            // 查找要替换的代码
            String oldString = suggestion.getOldString();
            String newString = suggestion.getNewString();
            
            int startOffset = document.getText().indexOf(oldString);
            if (startOffset == -1) {
                showError(sourceComponent, "在文件中未找到要替换的代码", suggestion);
                if (onFailure != null) onFailure.run();
                return;
            }
            
            // 执行文件修改
            WriteCommandAction.runWriteCommandAction(project, () -> {
                try {
                    int endOffset = startOffset + oldString.length();
                    
                    // 替换代码
                    document.deleteString(startOffset, endOffset);
                    document.insertString(startOffset, newString);
                    
                    // 打开文件并高亮修改区域
                    ApplicationManager.getApplication().invokeLater(() -> {
                        openFileAndHighlight(virtualFile, startOffset, startOffset + newString.length());
                    });
                    
                    LOG.info("Successfully applied suggestion for file: " + suggestion.getFilePath());
                    showSuccess(sourceComponent, "建议已成功应用到文件");
                    
                    if (onSuccess != null) onSuccess.run();
                    
                } catch (Exception e) {
                    LOG.error("Error applying suggestion", e);
                    showError(sourceComponent, "应用建议时发生错误: " + e.getMessage(), suggestion);
                    if (onFailure != null) onFailure.run();
                }
            });
            
        } catch (Exception e) {
            LOG.error("Error in acceptSuggestion", e);
            showError(sourceComponent, "处理建议时发生错误: " + e.getMessage(), suggestion);
            if (onFailure != null) onFailure.run();
        }
    }
    
    /**
     * 打开文件并高亮指定区域
     */
    private void openFileAndHighlight(VirtualFile file, int startOffset, int endOffset) {
        ApplicationManager.getApplication().invokeLater(() -> {
            FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);
            
            // 打开文件
            com.intellij.openapi.fileEditor.FileEditor[] fileEditors = fileEditorManager.openFile(file, true);
            
            if (fileEditors != null && fileEditors.length > 0) {
                // 尝试获取文本编辑器
                for (com.intellij.openapi.fileEditor.FileEditor fileEditor : fileEditors) {
                    if (fileEditor instanceof com.intellij.openapi.fileEditor.TextEditor) {
                        Editor editor = ((com.intellij.openapi.fileEditor.TextEditor) fileEditor).getEditor();
                        
                        // 设置绿色高亮表示成功应用
                        JBColor highlightColor = new JBColor(new Color(41, 68, 54), new Color(41, 68, 54));
                        CodeUtils.highlighterCode(editor, startOffset, endOffset, highlightColor);
                        
                        // 滚动到修改位置
                        editor.getCaretModel().moveToOffset(startOffset);
                        editor.getScrollingModel().scrollToCaret(com.intellij.openapi.editor.ScrollType.CENTER);
                        break;
                    }
                }
            }
        });
    }
    
    /**
     * 显示成功消息
     */
    private void showSuccess(JComponent component, String message) {
        Balloon balloon = JBPopupFactory.getInstance()
                .createHtmlTextBalloonBuilder(message, AllIcons.General.InspectionsOK, 
                    JBColor.GREEN.brighter(), null)
                .setFadeoutTime(3000)
                .createBalloon();
        balloon.show(RelativePoint.getCenterOf(component), Balloon.Position.above);
    }
    
    /**
     * 显示错误消息并将建议代码复制到剪贴板
     */
    private void showError(JComponent component, String message, 
                          intellicode.event.v1.EventOuterClass.Suggestion suggestion) {
        // 复制建议代码到剪贴板
        StringSelection stringSelection = new StringSelection(suggestion.getNewString());
        Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stringSelection, null);
        
        String fullMessage = message + "\n建议的代码已复制到剪贴板";
        
        Balloon balloon = JBPopupFactory.getInstance()
                .createHtmlTextBalloonBuilder(fullMessage, AllIcons.General.NotificationError, 
                    JBColor.RED.brighter(), null)
                .setFadeoutTime(5000)
                .createBalloon();
        balloon.show(RelativePoint.getCenterOf(component), Balloon.Position.above);
        
        LOG.warn("Failed to apply suggestion: " + message + ", suggestion: " + suggestion.getDescription());
    }
}