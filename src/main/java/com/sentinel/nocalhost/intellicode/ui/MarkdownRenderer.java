package com.sentinel.nocalhost.intellicode.ui;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.Disposable;
import com.intellij.ui.JBColor;
import com.intellij.util.ui.JBUI;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.data.MutableDataSet;

import javax.swing.*;
import javax.swing.text.html.HTMLEditorKit;
import javax.swing.text.html.StyleSheet;
import java.awt.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Markdown 渲染工具类，专门用于 Agent 输出窗口
 * 基于 CodeUtils 的成功实现，优化用于实时流式内容显示
 */
public class MarkdownRenderer implements Disposable {
    private static final Logger LOG = Logger.getInstance(MarkdownRenderer.class);
    
    private final StringBuilder markdownBuffer = new StringBuilder();
    private final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
    private final JEditorPane editorPane;
    private final HTMLEditorKit htmlKit;
    private final Parser markdownParser;
    private final HtmlRenderer htmlRenderer;
    
    public MarkdownRenderer(JEditorPane editorPane) {
        this.editorPane = editorPane;
        
        // 初始化 Markdown 解析器
        MutableDataSet options = new MutableDataSet();
        this.markdownParser = Parser.builder(options).build();
        this.htmlRenderer = HtmlRenderer.builder(options)
                .softBreak("<br/>")
                .build();
        
        // 配置 HTML Editor Kit
        this.htmlKit = new HTMLEditorKit();
        StyleSheet styleSheet = htmlKit.getStyleSheet();
        configureStyles(styleSheet);
        
        // 配置编辑器
        editorPane.setContentType("text/html");
        editorPane.setEditorKit(htmlKit);
        editorPane.setEditable(false);
        editorPane.putClientProperty(JEditorPane.HONOR_DISPLAY_PROPERTIES, Boolean.TRUE);
        editorPane.setBackground(null);
        editorPane.putClientProperty("JEditorPane.honorDisplayProperties", Boolean.TRUE);
        editorPane.putClientProperty("html.disable", Boolean.FALSE);
        
        // 设置首选大小确保可见性
        editorPane.setPreferredSize(new java.awt.Dimension(400, 300));
        editorPane.setMinimumSize(new java.awt.Dimension(400, 300));
        editorPane.setSize(new java.awt.Dimension(400, 300));
    }
    
    /**
     * 配置 HTML 样式，优化用于 Agent 输出
     */
    private void configureStyles(StyleSheet styleSheet) {
        // 基础样式
        styleSheet.addRule("body {"
                + "font-family: " + JBUI.Fonts.label().getFamily() + ", -apple-system, sans-serif;"
                + "font-size: " + JBUI.Fonts.label().getSize() + "px;"
                + "line-height: 1.6;"
                + "color: " + colorToHex(JBColor.foreground()) + ";"
                + "margin: 8px;"
                + "padding: 0;"
                + "}");

        // 标题样式
        styleSheet.addRule("h1, h2, h3, h4, h5, h6 {"
                + "margin: 12px 0 6px 0;"
                + "padding: 0;"
                + "line-height: 1.3;"
                + "font-weight: bold;"
                + "}");
        
        styleSheet.addRule("h1 { font-size: " + (JBUI.Fonts.label().getSize() + 6) + "px; }");
        styleSheet.addRule("h2 { font-size: " + (JBUI.Fonts.label().getSize() + 4) + "px; }");
        styleSheet.addRule("h3 { font-size: " + (JBUI.Fonts.label().getSize() + 2) + "px; }");

        // 段落样式
        styleSheet.addRule("p {"
                + "margin: 6px 0;"
                + "padding: 0;"
                + "}");

        // 代码块样式
        styleSheet.addRule("pre {"
                + "background-color: " + colorToHex(JBUI.CurrentTheme.EditorTabs.background()) + ";"
                + "border: 1px solid " + colorToHex(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()) + ";"
                + "border-radius: 4px;"
                + "padding: 8px;"
                + "margin: 8px 0;"
                + "overflow-x: auto;"
                + "font-family: 'JetBrains Mono', monospace;"
                + "font-size: " + (JBUI.Fonts.label().getSize() - 1) + "px;"
                + "}");

        // 行内代码样式
        styleSheet.addRule("code {"
                + "background-color: " + colorToHex(JBUI.CurrentTheme.EditorTabs.background()) + ";"
                + "border-radius: 3px;"
                + "padding: 1px 4px;"
                + "font-family: 'JetBrains Mono', monospace;"
                + "color: " + colorToHex(new JBColor(new Color(204, 120, 50), new Color(204, 120, 50))) + ";"
                + "font-size: " + (JBUI.Fonts.label().getSize() - 1) + "px;"
                + "}");

        // 引用块样式
        styleSheet.addRule("blockquote {"
                + "border-left: 4px solid " + colorToHex(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()) + ";"
                + "padding-left: 12px;"
                + "margin: 8px 0;"
                + "color: " + colorToHex(JBUI.CurrentTheme.Label.disabledForeground()) + ";"
                + "font-style: italic;"
                + "}");

        // 列表样式
        styleSheet.addRule("ul, ol {"
                + "margin: 6px 0;"
                + "padding-left: 24px;"
                + "}");

        styleSheet.addRule("li {"
                + "margin: 2px 0;"
                + "padding: 0;"
                + "}");

        // 分隔线样式
        styleSheet.addRule("hr {"
                + "border: none;"
                + "border-top: 1px solid " + colorToHex(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()) + ";"
                + "margin: 16px 0;"
                + "}");

        // 表格样式
        styleSheet.addRule("table {"
                + "border-collapse: collapse;"
                + "margin: 8px 0;"
                + "width: 100%;"
                + "}");

        styleSheet.addRule("th, td {"
                + "border: 1px solid " + colorToHex(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()) + ";"
                + "padding: 6px 8px;"
                + "text-align: left;"
                + "}");

        styleSheet.addRule("th {"
                + "background-color: " + colorToHex(JBUI.CurrentTheme.EditorTabs.background()) + ";"
                + "font-weight: bold;"
                + "}");

        // 时间戳样式
        styleSheet.addRule(".timestamp {"
                + "color: " + colorToHex(JBUI.CurrentTheme.Label.disabledForeground()) + ";"
                + "font-size: " + (JBUI.Fonts.label().getSize() - 2) + "px;"
                + "font-style: italic;"
                + "}");

        // 事件状态样式
        styleSheet.addRule(".event-success { color: " + colorToHex(JBColor.GREEN) + "; font-weight: bold; }");
        styleSheet.addRule(".event-error { color: " + colorToHex(JBColor.RED) + "; font-weight: bold; }");
        styleSheet.addRule(".event-running { color: " + colorToHex(JBColor.BLUE) + "; font-weight: bold; }");
    }

    /**
     * 颜色转换为十六进制字符串
     */
    private String colorToHex(Color color) {
        return String.format("#%02x%02x%02x", color.getRed(), color.getGreen(), color.getBlue());
    }

    /**
     * 初始化输出内容
     */
    public void initializeContent(List<String> reviewFiles) {
        markdownBuffer.setLength(0);
        markdownBuffer.append("# 📋 Code Review Session\n\n");
        markdownBuffer.append("**Files to Review:**\n");
        for (String file : reviewFiles) {
            markdownBuffer.append("- `").append(file).append("`\n");
        }
        markdownBuffer.append("\n---\n\n");
        renderContent();
    }

    /**
     * 添加会话开始事件
     */
    public void addSessionStart(String sessionId, List<String> files) {
        String timestamp = timeFormat.format(new Date());
        markdownBuffer.append("## 🚀 Session Started\n");
        markdownBuffer.append("**Session ID:** `").append(sessionId).append("`  \n");
        markdownBuffer.append("**Time:** <span class=\"timestamp\">").append(timestamp).append("</span>  \n");
        markdownBuffer.append("**Files:** ").append(String.join(", ", files.stream().map(f -> "`" + f + "`").toList())).append("\n\n");
        renderContent();
    }

    /**
     * 添加会话结束事件
     */
    public void addSessionEnd(String sessionId, boolean success) {
        String timestamp = timeFormat.format(new Date());
        String statusIcon = success ? "✅" : "❌";
        String statusClass = success ? "event-success" : "event-error";
        String statusText = success ? "Successfully" : "Failed";
        
        markdownBuffer.append("## ").append(statusIcon).append(" Session ").append(statusText).append("\n");
        markdownBuffer.append("**Session ID:** `").append(sessionId).append("`  \n");
        markdownBuffer.append("**Time:** <span class=\"timestamp\">").append(timestamp).append("</span>  \n");
        markdownBuffer.append("**Status:** <span class=\"").append(statusClass).append("\">").append(statusText).append("</span>\n\n");
        renderContent();
    }

    /**
     * 添加工具开始事件
     */
    public void addToolStart(String toolName, String input) {
        markdownBuffer.append("🔧 ").append(toolName).append(" | Status: 🔄\n\n");
        
        if (input != null && !input.isEmpty()) {
            if ("TodoWrite".equals(toolName)) {
                // TodoWrite 工具使用美化的格式
                String formattedInput = formatTodoInput(input);
                markdownBuffer.append("**Input:**\n").append(formattedInput).append("\n");
            } else if ("IssueAnalyzer".equals(toolName)) {
                // IssueAnalyzer 工具使用美化的格式
                String formattedInput = formatIssueAnalyzerInput(input);
                markdownBuffer.append(formattedInput).append("\n");
            } else {
                // 其他工具使用原始的代码块格式
                markdownBuffer.append("**Input:**\n```\n").append(input).append("\n```\n\n");
            }
        }
        renderContent();
    }

    /**
     * 添加工具结束事件
     */
    public void addToolEnd(String toolName, String result, boolean success) {
        String statusIcon = success ? "✅" : "❌";
        
        // 更新工具状态显示
        String content = markdownBuffer.toString();
        String searchPattern = "🔧 " + toolName + " | Status: 🔄";
        String replacement = "🔧 " + toolName + " | Status: " + statusIcon;
        
        content = content.replace(searchPattern, replacement);
        markdownBuffer.setLength(0);
        markdownBuffer.append(content);
        
        // 统一不显示所有工具的 output 部分，保持界面简洁
        markdownBuffer.append("\n");
        
        renderContent();
    }

    /**
     * 开始Assistant消息
     */
    public void startAssistantMessage() {
        markdownBuffer.append("🤖");
        renderContent();
    }

    /**
     * 追加Assistant消息chunk
     */
    public void appendAssistantChunk(String chunk) {
        markdownBuffer.append(chunk);
        renderContent();
    }

    /**
     * 完成Assistant消息
     */
    public void finishAssistantMessage() {
        markdownBuffer.append("\n\n---\n\n");
        renderContent();
    }

    /**
     * 添加错误事件
     */
    public void addError(String errorMessage) {
        String timestamp = timeFormat.format(new Date());
        markdownBuffer.append("> ⚠️ **Error Occurred**  \n");
        markdownBuffer.append("> ").append(errorMessage).append("  \n");
        markdownBuffer.append("> <span class=\"timestamp\">").append(timestamp).append("</span>\n\n");
        renderContent();
    }

    /**
     * 渲染 Markdown 内容为 HTML 并更新编辑器
     */
    private void renderContent() {
        SwingUtilities.invokeLater(() -> {
            try {
                String markdownContent = markdownBuffer.toString();
                if (markdownContent.isEmpty()) {
                    return; // 如果内容为空，不进行渲染
                }
                
                String html = htmlRenderer.render(markdownParser.parse(markdownContent));
                editorPane.setText(html);
                
                // 强制重新布局和重绘
                editorPane.revalidate();
                editorPane.repaint();
                
                // 检查父容器也重新布局
                if (editorPane.getParent() != null) {
                    editorPane.getParent().revalidate();
                    editorPane.getParent().repaint();
                }
                
                // 移除强制滚动逻辑，由 JScrollPane 的 AdjustmentListener 处理
            } catch (Exception e) {
                LOG.error("Error rendering markdown content: " + e.getMessage(), e);
                // 如果渲染失败，使用简单的HTML格式显示内容
                String fallbackHtml = "<html><body><pre>" + 
                    markdownBuffer.toString().replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;") + 
                    "</pre></body></html>";
                editorPane.setText(fallbackHtml);
            }
        });
    }

    /**
     * 清空内容
     */
    public void clear() {
        markdownBuffer.setLength(0);
        SwingUtilities.invokeLater(() -> {
            editorPane.setText("");
        });
    }

    /**
     * 获取当前 Markdown 内容
     */
    public String getMarkdownContent() {
        return markdownBuffer.toString();
    }

    /**
     * 格式化 TodoWrite 工具的 input 为美化的 Markdown 列表
     */
    private String formatTodoInput(String input) {
        try {
            JSONObject inputJson = JSON.parseObject(input);
            JSONArray todos = inputJson.getJSONArray("todos");
            
            if (todos == null || todos.isEmpty()) {
                return input; // 如果解析失败或为空，返回原始输入
            }
            
            StringBuilder formatted = new StringBuilder();
            formatted.append("📋 Todo List (").append(todos.size()).append(" items)\n\n");
            
            for (int i = 0; i < todos.size(); i++) {
                JSONObject todo = todos.getJSONObject(i);
                String status = todo.getString("status");
                String difficulty = todo.getString("difficulty");
                String content = todo.getString("content");
                
                // 状态映射
                String statusEmoji;
                switch (status) {
                    case "completed":
                        statusEmoji = "✅";
                        break;
                    case "in_progress":
                        statusEmoji = "🔄";
                        break;
                    case "pending":
                        statusEmoji = "⏳";
                        break;
                    default:
                        statusEmoji = "❓";
                        break;
                }
                
                // 难度格式化
                String difficultyLabel = "[" + (difficulty != null ? 
                    difficulty.substring(0, 1).toUpperCase() + difficulty.substring(1).toLowerCase() : 
                    "Unknown") + "]";
                
                // 为已完成的 todo 添加删除线效果
                String formattedContent = content;
                if ("completed".equals(status)) {
                    formattedContent = "<s>" + content + "</s>";
                }
                
                formatted.append("- ").append(statusEmoji).append(" ")
                         .append(difficultyLabel).append(" ")
                         .append(formattedContent).append("\n");
            }
            
            return formatted.toString();
            
        } catch (Exception e) {
            LOG.warn("Failed to format todo input: " + e.getMessage());
            return input; // 如果格式化失败，返回原始输入
        }
    }

    /**
     * 格式化 IssueAnalyzer 工具的 input 为美化的 Markdown 格式
     */
    private String formatIssueAnalyzerInput(String input) {
        try {
            JSONObject inputJson = JSON.parseObject(input);
            String thought = inputJson.getString("thought");
            String action = inputJson.getString("action");
            
            if (thought == null && action == null) {
                return input; // 如果没有找到相关字段，返回原始输入
            }
            
            StringBuilder formatted = new StringBuilder();
            
            // 添加思考部分
            if (thought != null && !thought.trim().isEmpty()) {
                formatted.append("🤔 **Thought:** \n")
                         .append(thought.trim())
                         .append("\n\n");
            }
            
            // 添加行动部分
            if (action != null && !action.trim().isEmpty()) {
                formatted.append("🎯 **Action:**\n");
                
                // 将 action 字段按换行符分割并转换为有序列表
                String[] actionLines = action.trim().split("\n");
                int stepNumber = 1;
                
                for (String line : actionLines) {
                    String trimmedLine = line.trim();
                    if (!trimmedLine.isEmpty()) {
                        // 移除可能已经存在的序号（如 "1. ", "2. " 等）
                        String cleanLine = trimmedLine.replaceFirst("^\\d+\\.\\s*", "");
                        formatted.append(stepNumber).append(". ").append(cleanLine).append("\n");
                        stepNumber++;
                    }
                }
            }
            
            return formatted.toString();
            
        } catch (Exception e) {
            LOG.warn("Failed to format IssueAnalyzer input: " + e.getMessage());
            return input; // 如果格式化失败，返回原始输入
        }
    }
    
    @Override
    public void dispose() {
        try {
            if (markdownBuffer != null) {
                markdownBuffer.setLength(0);
            }
            LOG.debug("MarkdownRenderer disposed");
        } catch (Exception e) {
            LOG.warn("Error disposing MarkdownRenderer", e);
        }
    }
}