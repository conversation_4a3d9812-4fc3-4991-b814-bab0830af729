package com.sentinel.nocalhost.intellicode.ui;

import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentFactory;
import org.jetbrains.annotations.NotNull;

/**
 * Intellicode 工具窗口工厂
 * 创建 Intellicode Agent 相关的工具窗口内容
 */
public class IntellicodeWindowFactory implements ToolWindowFactory, DumbAware {
    
    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        // 创建 Intellicode Home 页面
        IntellicodeHomePanel homePanel = new IntellicodeHomePanel(project);
        Content homeTab = ContentFactory.getInstance().createContent(
            homePanel.getMainPanel(), 
            "Intellicode Home", 
            true
        );
        
        // 添加到工具窗口
        toolWindow.getContentManager().addContent(homeTab);
        
        // 设置为默认选中的 tab
        toolWindow.getContentManager().setSelectedContent(homeTab);
    }
}