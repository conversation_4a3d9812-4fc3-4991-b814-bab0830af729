package com.sentinel.nocalhost.intellicode.ui;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.SimpleToolWindowPanel;
import com.intellij.openapi.util.Disposer;
import com.intellij.ui.JBColor;
import com.intellij.ui.JBSplitter;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBLoadingPanel;
import com.intellij.util.ui.JBUI;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import com.sentinel.nocalhost.intellicode.api.IntellicodeAPI;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * AgentWorkspace 主面板
 * 实现上下分区布局，上部为 Execution Display Area（暂时空白），下部为 Suggestion Area
 */
public class AgentWorkspacePanel implements Disposable {
    
    private static final Logger LOG = Logger.getInstance(AgentWorkspacePanel.class);
    
    // 常量定义
    private static final float COLLAPSED_SPLITTER_PROPORTION = 0.8f; // 折叠状态下的分区比例 80:20
    private static final float EXPANDED_SPLITTER_PROPORTION = 0.3f; // 展开状态下的分区比例 30:70
    private static final String TITLE_EXECUTION = "执行过程";
    
    private final Project project;
    private final String sessionId;
    private final int reviewId;
    private final IntellicodeAPI api;
    
    // UI 组件
    private SimpleToolWindowPanel mainPanel;
    private JBSplitter splitter;
    private JPanel executionDisplayArea;
    private SuggestionListComponent suggestionArea;
    private JBLoadingPanel loadingPanel;
    
    // 页面切换回调
    private IntellicodeHomePanel homePanel;
    
    public AgentWorkspacePanel(@NotNull Project project, @NotNull String sessionId, int reviewId) {
        this.project = project;
        this.sessionId = sessionId;
        this.reviewId = reviewId;
        this.api = IntellicodeAPI.getInstance();
        
        try {
            initializeUI();
            loadSuggestionData();
        } catch (Exception e) {
            LOG.error("Failed to initialize AgentWorkspacePanel", e);
            createErrorPanel(e);
        }
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 创建主面板
        mainPanel = new SimpleToolWindowPanel(false);
        mainPanel.setProvideQuickActions(false);
        
        // 创建上下分区布局
        createSplitterLayout();
        
        // 创建加载面板
        loadingPanel = new JBLoadingPanel(new BorderLayout(), this);
        loadingPanel.add(splitter, BorderLayout.CENTER);
        
        mainPanel.setContent(loadingPanel);
        
        LOG.debug("AgentWorkspacePanel UI initialized for sessionId: " + sessionId + ", reviewId: " + reviewId);
    }
    
    /**
     * 创建分割面板布局
     */
    private void createSplitterLayout() {
        // 创建垂直分割器（上下布局）
        splitter = new JBSplitter(true, COLLAPSED_SPLITTER_PROPORTION);
        splitter.setDividerWidth(JBUI.scale(6)); // 适中的分割条宽度
        splitter.setHonorComponentsMinimumSize(true);
        
        // 自定义分割条的视觉样式，使其更明显和可拖拽
        customizeSplitterDivider();
        
        // JBSplitter本身就支持拖动调整，无需额外设置
        
        // 创建上半部分：Execution Display Area（暂时空白）
        createExecutionDisplayArea();
        
        // 创建下半部分：Suggestion Area
        createSuggestionArea();
        
        // 不设置固定最小尺寸，允许动态调整
        
        splitter.setFirstComponent(executionDisplayArea);
        splitter.setSecondComponent(suggestionArea.getComponent());
        
        // 设置展开监听器
        suggestionArea.setExpansionListener(this::onSuggestionExpansionChanged);
    }
    
    /**
     * 创建执行显示区域（暂时保留空白）
     */
    private void createExecutionDisplayArea() {
        executionDisplayArea = new JPanel(new BorderLayout());
        executionDisplayArea.setBorder(JBUI.Borders.empty(12));
        executionDisplayArea.setBackground(JBUI.CurrentTheme.ToolWindow.background());
        
        // 添加占位符标题
        JBLabel titleLabel = new JBLabel(TITLE_EXECUTION);
        titleLabel.setFont(JBUI.Fonts.label(14).asBold());
        titleLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        executionDisplayArea.add(titleLabel, BorderLayout.NORTH);
        
        // 添加空白内容区域
        JPanel contentArea = new JPanel();
        contentArea.setBackground(JBUI.CurrentTheme.ToolWindow.background());
        contentArea.setBorder(JBUI.Borders.compound(
            JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()),
            JBUI.Borders.empty(16)
        ));
        
        JBLabel placeholderLabel = new JBLabel("执行过程显示区域（待实现）");
        placeholderLabel.setFont(JBUI.Fonts.label(12));
        placeholderLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        placeholderLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        contentArea.setLayout(new BorderLayout());
        contentArea.add(placeholderLabel, BorderLayout.CENTER);
        
        executionDisplayArea.add(contentArea, BorderLayout.CENTER);
    }
    
    /**
     * 创建建议区域
     */
    private void createSuggestionArea() {
        suggestionArea = new SuggestionListComponent(project, reviewId, false); // 历史模式
        Disposer.register(this, suggestionArea);
    }
    
    /**
     * 处理建议区域展开状态变化
     */
    private void onSuggestionExpansionChanged(boolean expanded) {
        SwingUtilities.invokeLater(() -> {
            try {
                float targetProportion = expanded ? EXPANDED_SPLITTER_PROPORTION : COLLAPSED_SPLITTER_PROPORTION;
                
                // 使用动画效果平滑过渡（简单版本）
                animateSplitterProportion(targetProportion);
                
                LOG.debug("Splitter proportion changed to: " + targetProportion + " (expanded: " + expanded + ")");
            } catch (Exception e) {
                LOG.error("Failed to adjust splitter proportion", e);
            }
        });
    }
    
    /**
     * 动画调整分割器比例
     */
    private void animateSplitterProportion(float targetProportion) {
        if (splitter == null) {
            return;
        }
        
        float currentProportion = splitter.getProportion();
        float difference = targetProportion - currentProportion;
        
        // 如果差异很小，直接设置
        if (Math.abs(difference) < 0.01f) {
            splitter.setProportion(targetProportion);
            return;
        }
        
        // 简单的动画效果，分步调整
        Timer timer = new Timer(20, null); // 20ms间隔
        final int steps = 10; // 分10步完成
        final float stepSize = difference / steps;
        final int[] currentStep = {0};
        
        timer.addActionListener(e -> {
            currentStep[0]++;
            float newProportion = currentProportion + (stepSize * currentStep[0]);
            
            if (currentStep[0] >= steps) {
                newProportion = targetProportion; // 确保最终准确
                timer.stop();
            }
            
            splitter.setProportion(newProportion);
        });
        
        timer.start();
    }
    
    /**
     * 自定义分割条视觉样式
     */
    private void customizeSplitterDivider() {
        JComponent divider = splitter.getDivider();
        
        // 设置分割条背景色，使其与工具窗口背景略有区别
        divider.setBackground(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground());
        
        // 设置复合边框：外层为细线边框，内层为填充
        divider.setBorder(JBUI.Borders.compound(
            // 外层：上下各一条细线，模拟 IntelliJ 标准分割线
            JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground(), 1, 0, 1, 0),
            // 内层：添加一些填充，并在中间绘制拖拽提示
            JBUI.Borders.empty(1, 0, 1, 0)
        ));
        
        // 添加鼠标悬停效果
        divider.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                divider.setCursor(java.awt.Cursor.getPredefinedCursor(java.awt.Cursor.N_RESIZE_CURSOR));
                // 悬停时稍微加深背景色
                divider.setBackground(JBUI.CurrentTheme.List.Hover.background(false));
            }
            
            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                divider.setCursor(java.awt.Cursor.getDefaultCursor());
                // 恢复原始背景色
                divider.setBackground(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground());
            }
        });
        
        // 可选：添加自定义绘制，在分割条中央绘制拖拽握把
        customizeDividerPaint(divider);
    }
    
    /**
     * 自定义分割条绘制，添加拖拽握把视觉提示
     */
    private void customizeDividerPaint(JComponent divider) {
        divider.addComponentListener(new java.awt.event.ComponentAdapter() {
            @Override
            public void componentResized(java.awt.event.ComponentEvent e) {
                // 当组件大小改变时，重新绘制
                divider.repaint();
            }
        });
        
        // 重写分割条的绘制方法
        divider.setBorder(JBUI.Borders.compound(
            divider.getBorder(),
            new javax.swing.border.AbstractBorder() {
                @Override
                public void paintBorder(java.awt.Component c, java.awt.Graphics g, int x, int y, int width, int height) {
                    java.awt.Graphics2D g2d = (java.awt.Graphics2D) g.create();
                    try {
                        g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
                        
                        // 在分割条中央绘制三个小圆点作为拖拽提示
                        int centerY = y + height / 2;
                        int centerX = x + width / 2;
                        int dotSize = JBUI.scale(2);
                        int spacing = JBUI.scale(4);
                        
                        g2d.setColor(JBUI.CurrentTheme.Label.disabledForeground());
                        
                        // 绘制三个水平排列的小圆点
                        for (int i = -1; i <= 1; i++) {
                            int dotX = centerX + i * spacing - dotSize / 2;
                            int dotY = centerY - dotSize / 2;
                            g2d.fillOval(dotX, dotY, dotSize, dotSize);
                        }
                    } finally {
                        g2d.dispose();
                    }
                }
            }
        ));
    }
    
    /**
     * 加载建议数据
     */
    private void loadSuggestionData() {
        // 显示加载状态
        SwingUtilities.invokeLater(() -> {
            loadingPanel.startLoading();
        });
        
        // 异步加载建议数据
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                CompletableFuture<List<IntellicodeAPI.ReviewSuggestionInfo>> future = 
                    api.getReviewRecordSuggestions(String.valueOf(reviewId));
                    
                future.whenComplete((suggestions, throwable) -> {
                    SwingUtilities.invokeLater(() -> {
                        try {
                            if (throwable != null) {
                                LOG.warn("Failed to load review suggestions", throwable);
                                showError("加载建议失败: " + throwable.getMessage());
                            } else {
                                suggestionArea.updateSuggestions(suggestions);
                                LOG.info("Loaded " + suggestions.size() + " suggestions for review " + reviewId);
                            }
                        } finally {
                            loadingPanel.stopLoading();
                        }
                    });
                });
                
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    LOG.error("Failed to load suggestion data", e);
                    showError("加载数据失败: " + e.getMessage());
                    loadingPanel.stopLoading();
                });
            }
        });
    }
    
    /**
     * 显示错误信息
     */
    private void showError(@NotNull String message) {
        // 可以在建议区域显示错误信息
        suggestionArea.showError(message);
    }
    
    /**
     * 创建错误面板
     */
    private void createErrorPanel(@NotNull Exception e) {
        mainPanel = new SimpleToolWindowPanel(false);
        JPanel errorPanel = new JPanel(new BorderLayout());
        errorPanel.setBorder(JBUI.Borders.empty(20));
        
        JBLabel errorLabel = new JBLabel(
            "<html><div style='text-align: center;'>" +
            "<h3>AgentWorkspace 初始化失败</h3>" +
            "<p>错误信息: " + e.getMessage() + "</p>" +
            "<p>请检查网络连接或重试</p>" +
            "</div></html>"
        );
        errorLabel.setHorizontalAlignment(SwingConstants.CENTER);
        errorLabel.setForeground(JBColor.RED);
        
        errorPanel.add(errorLabel, BorderLayout.CENTER);
        mainPanel.setContent(errorPanel);
    }
    
    /**
     * 设置首页面板引用（用于页面切换）
     */
    public void setHomePanel(@Nullable IntellicodeHomePanel homePanel) {
        this.homePanel = homePanel;
    }
    
    /**
     * 返回到首页
     */
    public void backToHome() {
        if (homePanel != null) {
            homePanel.showHomePanel();
        }
    }
    
    /**
     * 获取主面板
     */
    @NotNull
    public JPanel getMainPanel() {
        return (JPanel) mainPanel;
    }
    
    /**
     * 获取会话ID
     */
    @NotNull
    public String getSessionId() {
        return sessionId;
    }
    
    /**
     * 获取审查ID
     */
    public int getReviewId() {
        return reviewId;
    }
    
    @Override
    public void dispose() {
        try {
            // 清理建议区域
            if (suggestionArea != null) {
                Disposer.dispose(suggestionArea);
                suggestionArea = null;
            }
            
            // 停止加载
            if (loadingPanel != null) {
                loadingPanel.stopLoading();
            }
            
            LOG.debug("AgentWorkspacePanel disposed for sessionId: " + sessionId + ", reviewId: " + reviewId);
        } catch (Exception e) {
            LOG.error("Error disposing AgentWorkspacePanel", e);
        }
    }
}