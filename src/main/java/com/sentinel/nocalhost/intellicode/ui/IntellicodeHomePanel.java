package com.sentinel.nocalhost.intellicode.ui;

import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.ActionPlaces;
import com.intellij.openapi.actionSystem.ActionToolbar;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.DataContext;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.actionSystem.Separator;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.project.DumbService;
import com.intellij.openapi.startup.StartupManager;
import com.intellij.openapi.ui.SimpleToolWindowPanel;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vcs.ProjectLevelVcsManager;
import com.intellij.util.messages.MessageBusConnection;
import com.intellij.openapi.vcs.changes.Change;
import com.intellij.openapi.vcs.changes.ChangeListManager;
import com.intellij.ui.components.JBCheckBox;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.components.JBList;
import com.intellij.ui.CollectionListModel;
import com.intellij.ui.SimpleColoredComponent;
import com.intellij.ui.SimpleTextAttributes;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.util.ui.JBUI;
import com.intellij.icons.AllIcons;
import com.sentinel.nocalhost.intellicode.service.IntellicodeGitService;
import com.sentinel.nocalhost.intellicode.api.IntellicodeAPI;
import com.sentinel.nocalhost.intellicode.api.IntellicodeSSEListener;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.ui.action.toolbar.LoginAccountsAction;
import com.sentinel.nocalhost.ui.action.toolbar.HelpAction;
import com.sentinel.nocalhost.intellicode.ui.action.IntellicodeHomeAction;
import com.sentinel.nocalhost.intellicode.ui.action.IntellicodeHistoryAction;
import com.sentinel.nocalhost.topic.RefreshToolWindowNotifier;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Intellicode Home 页面主面板
 * 显示文件列表，支持工作区变更/committed 切换，提供 Review 按钮
 * 工作区变更包括已修改、已暂存、已删除、重命名的文件以及未跟踪的文件
 */
public class IntellicodeHomePanel implements Disposable, RefreshToolWindowNotifier {
  
  private static final Logger LOG = Logger.getInstance(IntellicodeHomePanel.class);
  
  // 常量定义
  private static final String OPTION_UNCOMMITTED = "查看未提交文件";
  private static final String OPTION_COMMITTED = "查看上次提交文件";
  private static final String CHECKBOX_SELECT_ALL = "全选";
  private static final String CHECKBOX_UNSELECT_ALL = "反选";
  private static final String BUTTON_REVIEW = "Review";
  private static final String TITLE_WOODPECKER = "啄木鸟";
  private static final String SUBTITLE_SLOGAN = "为您的代码保驾护航";
  
  private final Project project;
  private final NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
  private SimpleToolWindowPanel mainPanel;
  private JPanel contentPanel;
  private JPanel headerPanel;
  private JPanel actionPanel;
  private JPanel commitInfoPanel;
  private JButton reviewButton;
  private JBLabel statusLabel;
  private JComboBox<String> reviewModeComboBox;
  private boolean isShowingUncommitted = true;
  private JScrollPane scrollPane;
  private JBList<String> fileList;
  private CollectionListModel<String> fileListModel;
  private Set<String> selectedFiles;
  private IntellicodeGitService gitService;
  private IntellicodeAgentPanel agentPanel;
  private IntellicodeHistoryListPanel historyPanel;
  private AgentWorkspacePanel agentWorkspacePanel;
  private boolean isShowingAgent = false;
  private boolean isShowingHistory = false;
  private boolean isShowingAgentWorkspace = false;
  private JBLabel branchLabel;
  private JBLabel commitMessageLabel;
  private JBLabel commitHashLabel;
  private JBLabel commitDateLabel;
  private JBCheckBox selectAllCheckBox;
  private JBLabel commitSummaryLabel;
  private JPanel commitInfoBar;
  
  // Git 初始化状态管理
  private volatile boolean isGitInitialized = false;
  private final Object gitInitLock = new Object();
  private MessageBusConnection messageBusConnection;

  public IntellicodeHomePanel(Project project) {
    this.project = project;
    this.gitService = new IntellicodeGitService(project);
    this.selectedFiles = new HashSet<>();
    this.fileListModel = new CollectionListModel<>();
    try {
      initializeUI();
      // 显示加载状态
      showLoadingState();
      // 延迟初始化 Git 相关功能
      scheduleGitInitialization();
    } catch (Exception e) {
      // 如果初始化失败，创建一个简单的错误显示面板
      System.err.println("IntellicodeHomePanel initialization failed: " + e.getMessage());
      e.printStackTrace();
      createErrorPanel(e);
    }
  }

  private void initializeUI() {
    // 使用 SimpleToolWindowPanel 支持工具栏
    mainPanel = new SimpleToolWindowPanel(true);

    // 创建内容面板
    contentPanel = new JPanel(new BorderLayout());
    contentPanel.setBorder(JBUI.Borders.empty(JBUI.scale(12))); // 统一使用合理边距
    contentPanel.setBackground(JBUI.CurrentTheme.DefaultTabs.background());

    // 先创建 commit 信息面板
    createCommitInfoPanel();

    // 然后创建头部面板（依赖于 commitInfoPanel）
    createHeaderPanel();

    // 创建文件列表面板
    createFileListPanel();

    // 创建操作面板
    createActionPanel();

    // 创建主内容区域，只包含文件列表
    JPanel contentCard = createContentCard();

    contentPanel.add(headerPanel, BorderLayout.NORTH);
    contentPanel.add(contentCard, BorderLayout.CENTER);
    contentPanel.add(actionPanel, BorderLayout.SOUTH);

    // 将内容面板添加到主面板
    mainPanel.setContent(contentPanel);

    // 设置工具栏
    setToolbar(getActionGroup());
    
    // 添加客户端属性以便 Action 类可以找到此面板实例
    mainPanel.putClientProperty("IntellicodeHomePanel", this);
  }

  private void createHeaderPanel() {
    headerPanel = new JPanel(new BorderLayout());
    headerPanel.setBorder(JBUI.Borders.emptyBottom(32));
    headerPanel.setOpaque(false);

    // 上下布局的主要内容
    JPanel mainHeaderContent = new JPanel();
    mainHeaderContent.setLayout(new BoxLayout(mainHeaderContent, BoxLayout.Y_AXIS));
    mainHeaderContent.setOpaque(false);

    // 标题区域
    JPanel titleSection = createTitleSection();

    mainHeaderContent.add(titleSection);

    headerPanel.add(mainHeaderContent, BorderLayout.NORTH);

    // commitInfoPanel 不再添加到 header 中，使用内联的 commitSummaryLabel 代替
  }

  private JPanel createTitleSection() {
    JPanel titlePanel = new JPanel();
    titlePanel.setLayout(new BoxLayout(titlePanel, BoxLayout.Y_AXIS));
    titlePanel.setOpaque(false);
    titlePanel.setBorder(JBUI.Borders.empty(JBUI.scale(8), 0, JBUI.scale(24), 0));

    // 主标题 - 使用主题色彩的大标题
    JBLabel titleLabel = new JBLabel(TITLE_WOODPECKER);
    titleLabel.setFont(titleLabel.getFont().deriveFont(Font.BOLD, 24f));
    titleLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
    titleLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

    // 副标题 - 使用主题色彩的副标题
    JBLabel descLabel = new JBLabel(SUBTITLE_SLOGAN);
    descLabel.setFont(descLabel.getFont().deriveFont(Font.PLAIN, 14f));
    descLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
    descLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
    descLabel.setBorder(JBUI.Borders.emptyTop(JBUI.scale(6)));

    titlePanel.add(titleLabel);
    titlePanel.add(descLabel);

    return titlePanel;
  }


  private JPanel createContentCard() {
    // 使用标准面板，符合 IntelliJ 设计规范
    JPanel contentCard = new JPanel(new BorderLayout());
    contentCard.setBackground(JBUI.CurrentTheme.ToolWindow.background());
    contentCard.setBorder(JBUI.Borders.compound(
        JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()),
        JBUI.Borders.empty(JBUI.scale(12))));

    // 创建顶部容器，纵向排列
    JPanel topContainer = new JPanel();
    topContainer.setLayout(new BoxLayout(topContainer, BoxLayout.Y_AXIS));
    topContainer.setOpaque(false);

    // 文件列表头部（全选和下拉框）
    JPanel fileListHeader = createFileListHeader();
    fileListHeader.setAlignmentX(Component.LEFT_ALIGNMENT);

    // Commit 信息栏
    JPanel commitInfoBarPanel = createCommitInfoBar();
    commitInfoBarPanel.setAlignmentX(Component.LEFT_ALIGNMENT);

    topContainer.add(fileListHeader);
    topContainer.add(commitInfoBarPanel);

    contentCard.add(topContainer, BorderLayout.NORTH);
    contentCard.add(scrollPane, BorderLayout.CENTER);

    return contentCard;
  }

  private void createCommitInfoPanel() {
    commitInfoPanel = new JPanel(new BorderLayout());
    commitInfoPanel.setBorder(JBUI.Borders.empty(0, 0, 16, 0));
    commitInfoPanel.setOpaque(false);

    // 创建现代化的信息卡片
    JPanel infoCard = new JPanel();
    infoCard.setLayout(new BoxLayout(infoCard, BoxLayout.Y_AXIS));
    infoCard.setBorder(JBUI.Borders.compound(
        JBUI.Borders.customLine(JBUI.CurrentTheme.Popup.borderColor(true), 1),
        JBUI.Borders.empty(12, 16)));
    infoCard.setBackground(JBUI.CurrentTheme.Popup.headerBackground(true));

    // 初始化标签
    branchLabel = new JBLabel("Unknown");
    commitMessageLabel = new JBLabel("No commit information");
    commitHashLabel = new JBLabel("");
    commitDateLabel = new JBLabel("");

    // 设置现代化字体样式
    Font labelFont = branchLabel.getFont().deriveFont(Font.PLAIN, 12f);
    Font valueFont = branchLabel.getFont().deriveFont(Font.PLAIN, 11f);

    // 创建信息行
    JPanel branchRow = createInfoRow("Branch", branchLabel, labelFont, valueFont);
    JPanel messageRow = createInfoRow("Message", commitMessageLabel, labelFont, valueFont);
    JPanel hashRow = createInfoRow("Hash", commitHashLabel, labelFont, valueFont);
    JPanel dateRow = createInfoRow("Date", commitDateLabel, labelFont, valueFont);

    infoCard.add(branchRow);
    infoCard.add(Box.createVerticalStrut(8));
    infoCard.add(messageRow);
    infoCard.add(Box.createVerticalStrut(8));
    infoCard.add(hashRow);
    infoCard.add(Box.createVerticalStrut(8));
    infoCard.add(dateRow);

    commitInfoPanel.add(infoCard, BorderLayout.CENTER);

    // 默认隐藏，只在显示 committed 文件时显示
    commitInfoPanel.setVisible(false);
    commitInfoPanel.setBorder(JBUI.Borders.emptyTop(16));
  }

  private JPanel createInfoRow(String label, JBLabel valueLabel, Font labelFont, Font valueFont) {
    JPanel row = new JPanel(new BorderLayout());
    row.setOpaque(false);

    JBLabel keyLabel = new JBLabel(label + ":");
    keyLabel.setFont(labelFont.deriveFont(Font.BOLD));
    keyLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
    keyLabel.setPreferredSize(new Dimension(60, keyLabel.getPreferredSize().height));

    valueLabel.setFont(valueFont);
    valueLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());

    row.add(keyLabel, BorderLayout.WEST);
    row.add(valueLabel, BorderLayout.CENTER);

    return row;
  }

  private JPanel createFileListHeader() {
    JPanel headerPanel = new JPanel(new BorderLayout());
    headerPanel.setBorder(JBUI.Borders.emptyBottom(JBUI.scale(8)));
    headerPanel.setOpaque(false);

    // 创建左侧工具栏面板
    JPanel leftToolbar = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
    leftToolbar.setOpaque(false);

    // 全选/反选复选框 - 使用主题色彩
    selectAllCheckBox = new JBCheckBox(CHECKBOX_SELECT_ALL);
    selectAllCheckBox.setFont(selectAllCheckBox.getFont().deriveFont(Font.PLAIN, 12f));
    selectAllCheckBox.setForeground(JBUI.CurrentTheme.Label.foreground());
    selectAllCheckBox.setOpaque(false);

    // 添加全选/反选逻辑
    selectAllCheckBox.addActionListener(e -> {
      boolean selected = selectAllCheckBox.isSelected();

      if (selected) {
        // 全选所有文件
        selectedFiles.clear();
        for (int i = 0; i < fileListModel.getSize(); i++) {
          selectedFiles.add(fileListModel.getElementAt(i));
        }
      } else {
        // 反选所有文件
        selectedFiles.clear();
      }

      // 刷新界面
      fileList.repaint();
      updateReviewButtonState();

      // 更新复选框文本
      selectAllCheckBox.setText(selected ? CHECKBOX_UNSELECT_ALL : CHECKBOX_SELECT_ALL);
      selectAllCheckBox.setForeground(JBUI.CurrentTheme.Label.foreground());
    });

    leftToolbar.add(selectAllCheckBox);

    // 添加分隔符
    leftToolbar.add(Box.createHorizontalStrut(JBUI.scale(12)));
    JSeparator separator = new JSeparator(SwingConstants.VERTICAL);
    separator.setPreferredSize(new Dimension(1, JBUI.scale(20)));
    separator.setForeground(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground());
    leftToolbar.add(separator);
    leftToolbar.add(Box.createHorizontalStrut(JBUI.scale(12)));

    // 添加下拉选择框
    String[] options = { OPTION_UNCOMMITTED, OPTION_COMMITTED };
    reviewModeComboBox = new JComboBox<>(options);
    reviewModeComboBox.setPreferredSize(new Dimension(JBUI.scale(160), JBUI.scale(24)));
    reviewModeComboBox.setFont(JBUI.Fonts.label(12));
    reviewModeComboBox.addActionListener(e -> handleModeChange());

    leftToolbar.add(reviewModeComboBox);

    headerPanel.add(leftToolbar, BorderLayout.WEST);

    return headerPanel;
  }

  /**
   * 创建 commit 信息栏
   */
  private JPanel createCommitInfoBar() {
    commitInfoBar = new JPanel(new BorderLayout());
    commitInfoBar.setBackground(JBUI.CurrentTheme.ToolWindow.headerBackground());
    commitInfoBar.setBorder(JBUI.Borders.compound(
        JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground(), 0, 0, 1, 0),
        JBUI.Borders.empty(4, 12, 4, 12)
    ));
    commitInfoBar.setMaximumSize(new Dimension(Integer.MAX_VALUE, JBUI.scale(28)));
    commitInfoBar.setMinimumSize(new Dimension(0, JBUI.scale(28)));
    commitInfoBar.setPreferredSize(new Dimension(0, JBUI.scale(28)));

    // 创建信息标签
    commitSummaryLabel = new JBLabel();
    commitSummaryLabel.setFont(JBUI.Fonts.label(11));
    commitSummaryLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());

    commitInfoBar.add(commitSummaryLabel, BorderLayout.WEST);
    commitInfoBar.setVisible(false); // 默认隐藏

    return commitInfoBar;
  }

  private void createFileListPanel() {
    // 创建 JBList 替代自定义面板
    fileList = new JBList<>(fileListModel);
    fileList.setCellRenderer(new FileListCellRenderer());
    fileList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
    fileList.setBackground(JBUI.CurrentTheme.DefaultTabs.background());

    // 紧凑的行高
    fileList.setFixedCellHeight(28);

    // 添加鼠标点击监听，处理复选框点击
    fileList.addMouseListener(new MouseAdapter() {
      @Override
      public void mouseClicked(MouseEvent e) {
        handleFileListClick(e);
      }
    });

    scrollPane = new JBScrollPane(fileList);
    scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
    scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
    scrollPane.setBorder(JBUI.Borders.empty());
    scrollPane.setBackground(JBUI.CurrentTheme.DefaultTabs.background());
    scrollPane.getViewport().setBackground(JBUI.CurrentTheme.DefaultTabs.background());

    // 紧凑的尺寸
    scrollPane.setPreferredSize(new Dimension(0, 200));
    scrollPane.setMinimumSize(new Dimension(0, 100));
    scrollPane.getVerticalScrollBar().setUnitIncrement(16);
  }

  private void createActionPanel() {
    actionPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 0, 0));
    actionPanel.setBorder(JBUI.Borders.emptyTop(JBUI.scale(16)));
    actionPanel.setOpaque(false);

    // 创建符合 IntelliJ 设计语言的按钮，居中显示
    reviewButton = new JButton(BUTTON_REVIEW);
    styleReviewButton(reviewButton);
    reviewButton.addActionListener(new ReviewButtonListener());

    actionPanel.add(reviewButton);
  }

  /**
   * 处理下拉框模式切换
   */
  private void handleModeChange() {
    String selectedMode = (String) reviewModeComboBox.getSelectedItem();
    if (OPTION_UNCOMMITTED.equals(selectedMode)) {
      isShowingUncommitted = true;
      loadUncommittedFiles();
    } else if (OPTION_COMMITTED.equals(selectedMode)) {
      isShowingUncommitted = false;
      loadCommittedFiles();
    }
  }

  /**
   * 样式化 Review 按钮 - 使用 IntelliJ 原生样式
   */
  private void styleReviewButton(JButton button) {
    // 使用 IntelliJ 默认样式
    button.putClientProperty("JButton.buttonType", "borderless");
    button.setPreferredSize(new Dimension(JBUI.scale(100), JBUI.scale(32)));
    button.setFont(JBUI.Fonts.label(13));
    button.setFocusPainted(false);
    
    // 添加图标
    button.setIcon(AllIcons.Actions.Execute);
    button.setIconTextGap(JBUI.scale(6));
    
    // 设置初始工具提示
    button.setToolTipText("点击开始代码审查");
  }

  /**
   * 延迟初始化 Git 相关功能
   */
  private void scheduleGitInitialization() {
    // 使用 StartupManager 确保项目初始化完成
    StartupManager.getInstance(project).runAfterOpened(() -> {
      // 使用 DumbService 确保索引就绪
      DumbService.getInstance(project).runWhenSmart(() -> {
        // 在后台线程执行 Git 检查
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
          initializeGitWithRetry();
        });
      });
    });
    
    // 监听 VCS 配置变化作为补充
    messageBusConnection = project.getMessageBus().connect(this);
    messageBusConnection.subscribe(ProjectLevelVcsManager.VCS_CONFIGURATION_CHANGED, () -> {
      // VCS配置变化时重新初始化Git
      ApplicationManager.getApplication().executeOnPooledThread(() -> {
        initializeGitWithRetry();
      });
    });
    
    // 监听登录状态变化，重新加载工具栏
    messageBusConnection.subscribe(RefreshToolWindowNotifier.REFRESH_TOOL_WINDOW_TOPIC, this);
  }
  
  /**
   * 带重试机制的 Git 初始化
   */
  private void initializeGitWithRetry() {
    synchronized (gitInitLock) {
      if (isGitInitialized) {
        return;
      }
      
      int maxRetries = 3;
      int retryDelay = 500; // 毫秒
      
      for (int i = 0; i < maxRetries; i++) {
        if (tryInitializeGit()) {
          isGitInitialized = true;
          return;
        }
        
        if (i < maxRetries - 1) {
          try {
            Thread.sleep(retryDelay);
            retryDelay *= 2; // 指数退避
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return;
          }
        }
      }
      
      // 最终失败处理
      SwingUtilities.invokeLater(() -> {
        showNonGitProjectUI();
      });
    }
  }
  
  /**
   * 单次 Git 初始化尝试
   */
  private boolean tryInitializeGit() {
    try {
      if (gitService.isGitRepository()) {
        SwingUtilities.invokeLater(() -> {
          hideLoadingState();
          initializeFileListBasedOnGitStatus();
        });
        return true;
      }
    } catch (Exception e) {
      // 忽略异常，继续重试
    }
    return false;
  }
  
  /**
   * 显示加载状态
   */
  private void showLoadingState() {
    fileListModel.add("正在初始化 Git 仓库...");
    if (reviewModeComboBox != null) {
      reviewModeComboBox.setEnabled(false);
    }
    if (reviewButton != null) {
      reviewButton.setEnabled(false);
      reviewButton.setToolTipText("正在初始化中，请稍候...");
    }
    if (selectAllCheckBox != null) {
      selectAllCheckBox.setEnabled(false);
    }
  }
  
  /**
   * 隐藏加载状态
   */
  private void hideLoadingState() {
    fileListModel.removeAll();
    if (reviewModeComboBox != null) {
      reviewModeComboBox.setEnabled(true);
    }
    if (reviewButton != null) {
      reviewButton.setEnabled(false); // 初始状态应该是禁用，等待文件选择
      reviewButton.setToolTipText("请先选择要审查的文件");
    }
    if (selectAllCheckBox != null) {
      selectAllCheckBox.setEnabled(true);
    }
  }
  
  /**
   * 显示非 Git 项目界面
   */
  private void showNonGitProjectUI() {
    hideLoadingState();
    showErrorMessage("此功能只能在 Git 仓库中使用");
    disableReviewButton("此功能只能在 Git 仓库中使用");
  }

  /**
   * 根据需求文档逻辑初始化文件列表
   */
  private void initializeFileListBasedOnGitStatus() {
    // 异步检查Git状态
    ApplicationManager.getApplication().executeOnPooledThread(() -> {
      try {
        // 1. 检查是否是Git仓库
        if (!gitService.isGitRepository()) {
          SwingUtilities.invokeLater(() -> {
            showErrorMessage("此功能只能在git 仓库中使用");
            disableReviewButton("此功能只能在 Git 仓库中使用");
          });
          return;
        }

        // 2. 检查是否有未提交的文件
        List<String> uncommittedFiles = gitService.getUncommittedFiles();
        if (!uncommittedFiles.isEmpty()) {
          // 有未提交文件，显示工作区变更，默认选中查看未提交文件
          SwingUtilities.invokeLater(() -> {
            reviewModeComboBox.setSelectedItem(OPTION_UNCOMMITTED);
            isShowingUncommitted = true;
            updateFileList(uncommittedFiles, "No working directory changes found.");
            if (commitInfoBar != null) {
              commitInfoBar.setVisible(false);
            }
          });
          return;
        }

        // 3. 检查是否有历史提交
        if (gitService.hasCommitHistory()) {
          // 无变更但有历史提交，显示最近commit文件，默认选中查看上次提交文件
          SwingUtilities.invokeLater(() -> {
            reviewModeComboBox.setSelectedItem(OPTION_COMMITTED);
            isShowingUncommitted = false;
            loadCommittedFiles();
          });
          return;
        }

        // 4. 无变更且无历史提交
        SwingUtilities.invokeLater(() -> {
          showErrorMessage("没有可以review的文件");
          disableReviewButton("没有可以审查的文件");
        });

      } catch (Exception e) {
        SwingUtilities.invokeLater(() -> {
          showErrorMessage("检查Git状态时发生错误: " + e.getMessage());
          disableReviewButton("检查 Git 状态时发生错误");
        });
      }
    });
  }

  /**
   * 显示错误信息
   */
  private void showErrorMessage(String message) {
    // 清空文件列表
    fileListModel.removeAll();
    selectedFiles.clear();

    // 创建错误消息标签
    // 可以考虑在文件列表区域显示错误信息，或者使用通知
    updateFileList(new ArrayList<>(), message);

    // 隐藏commit信息
    if (commitInfoBar != null) {
      commitInfoBar.setVisible(false);
    }
  }

  /**
   * 禁用Review按钮
   */
  private void disableReviewButton() {
    disableReviewButton("此功能暂不可用");
  }
  
  /**
   * 禁用Review按钮并设置特定的错误提示
   */
  private void disableReviewButton(String errorMessage) {
    reviewButton.setEnabled(false);
    reviewButton.setToolTipText(errorMessage);
    if (selectAllCheckBox != null) {
      selectAllCheckBox.setEnabled(false);
    }
  }

  private void createErrorPanel(Exception e) {
    mainPanel = new SimpleToolWindowPanel(true, true);
    JPanel errorPanel = new JPanel(new BorderLayout());
    errorPanel.setBorder(JBUI.Borders.empty(20));

    JBLabel errorLabel = new JBLabel("<html><div style='text-align: center;'>" +
        "<h3>Intellicode 初始化失败</h3>" +
        "<p>错误信息: " + e.getMessage() + "</p>" +
        "<p>请检查项目配置或重启 IDE</p>" +
        "</div></html>");
    errorLabel.setHorizontalAlignment(SwingConstants.CENTER);
    errorLabel.setForeground(JBColor.RED);

    errorPanel.add(errorLabel, BorderLayout.CENTER);
    mainPanel.setContent(errorPanel);
  }

  private void loadUncommittedFiles() {
    // 异步加载，避免阻塞UI
    ApplicationManager.getApplication().executeOnPooledThread(() -> {
      try {
        List<String> files = gitService.getUncommittedFiles();

        // 在 EDT 上更新 UI
        SwingUtilities.invokeLater(() -> {
          updateFileList(files, "No working directory changes found.");
          if (commitInfoBar != null) {
            commitInfoBar.setVisible(false);
          }
        });
      } catch (Exception e) {
        SwingUtilities.invokeLater(() -> {
          updateFileList(new ArrayList<>(), "Error loading files: " + e.getMessage());
          if (commitInfoBar != null) {
            commitInfoBar.setVisible(false);
          }
        });
      }
    });
  }

  private void loadCommittedFiles() {
    // 在后台线程执行 Git 操作，避免阻塞 EDT
    com.intellij.openapi.application.ApplicationManager.getApplication().executeOnPooledThread(() -> {
      try {
        IntellicodeGitService.LatestCommitInfo commitInfo = gitService.getLatestCommitInfo();
        List<String> files = commitInfo.getFiles();

        // 在 EDT 上更新 UI
        SwingUtilities.invokeLater(() -> {
          updateFileList(files, "No committed files found.");
          // 使用新的简洁 commit 信息展示方式
          updateCommitSummary(commitInfo);
        });
      } catch (Exception e) {
        // 在 EDT 上显示错误信息
        SwingUtilities.invokeLater(() -> {
          updateFileList(new ArrayList<>(), "Error loading committed files: " + e.getMessage());
          if (commitInfoBar != null) {
            commitInfoBar.setVisible(false);
          }
        });
      }
    });
  }

  private void updateCommitInfo(IntellicodeGitService.LatestCommitInfo commitInfo) {
    branchLabel.setText(commitInfo.getBranchName());

    String commitMessage = commitInfo.getCommitMessage();
    if (commitMessage != null && commitMessage.length() > 60) {
      commitMessage = commitMessage.substring(0, 57) + "...";
    }
    commitMessageLabel.setText(commitMessage != null ? commitMessage : "No message");

    String hash = commitInfo.getShortCommitHash();
    commitHashLabel.setText(hash != null && !hash.isEmpty() ? hash : "Unknown");

    String date = commitInfo.getFormattedCommitDate();
    commitDateLabel.setText(date != null ? date : "Unknown");
  }

  /**
   * 更新简洁的 commit 信息摘要
   */
  private void updateCommitSummary(IntellicodeGitService.LatestCommitInfo commitInfo) {
    if (commitInfo == null || commitInfoBar == null) {
      if (commitInfoBar != null) {
        commitInfoBar.setVisible(false);
      }
      return;
    }

    // 构建简洁的摘要文本
    String hash = commitInfo.getShortCommitHash();
    String shortHash = hash != null && !hash.isEmpty() ? hash : "Unknown";

    String message = commitInfo.getCommitMessage();
    // 根据窗口宽度动态调整消息长度，窄窗口使用更短的长度
    int maxMessageLength = 40;
    String shortMessage = message != null && message.length() > maxMessageLength ? 
        message.substring(0, maxMessageLength - 3) + "..." : message;
    if (shortMessage == null) {
      shortMessage = "No message";
    }

    String summaryText = String.format("● %s • %s", shortHash, shortMessage);
    
    commitSummaryLabel.setText(summaryText);
    commitInfoBar.setVisible(true);

    // 设置完整信息的工具提示
    String tooltip = String.format(
        "<html>" +
        "<b>Branch:</b> %s<br>" +
        "<b>Commit:</b> %s<br>" +
        "<b>Message:</b> %s<br>" +
        "<b>Date:</b> %s" +
        "</html>",
        commitInfo.getBranchName() != null ? commitInfo.getBranchName() : "Unknown",
        commitInfo.getShortCommitHash() != null ? commitInfo.getShortCommitHash() : "Unknown",
        commitInfo.getCommitMessage() != null ? commitInfo.getCommitMessage() : "No message",
        commitInfo.getFormattedCommitDate() != null ? commitInfo.getFormattedCommitDate() : "Unknown"
    );
    
    commitSummaryLabel.setToolTipText(tooltip);
  }

  private void updateFileList(List<String> files, String emptyMessage) {
    // 清空当前数据
    fileListModel.removeAll();
    selectedFiles.clear();

    if (files.isEmpty()) {
      // TODO: 显示空消息 - 可以考虑在列表上方添加一个标签
      // 暂时不处理空状态，让列表保持空白
    } else {
      // 添加文件到模型
      for (String filePath : files) {
        fileListModel.add(filePath);
        // 默认选中所有文件
        selectedFiles.add(filePath);
      }
    }

    // 刷新列表显示
    fileList.revalidate();
    fileList.repaint();

    // 更新 Review 按钮状态
    updateReviewButtonState();
    // 更新全选复选框状态
    updateSelectAllCheckBox();
  }

  private void updateReviewButtonState() {
    boolean hasSelectedFiles = !selectedFiles.isEmpty();
    reviewButton.setEnabled(hasSelectedFiles);
    
    // 根据状态设置工具提示
    if (hasSelectedFiles) {
      reviewButton.setToolTipText("点击开始代码审查");
    } else {
      reviewButton.setToolTipText("请先选择要审查的文件");
    }
  }

  private void updateSelectAllCheckBox() {
    if (selectAllCheckBox == null || fileListModel.getSize() == 0) {
      return;
    }

    int totalFiles = fileListModel.getSize();
    int selectedCount = selectedFiles.size();

    if (selectedCount == 0) {
      selectAllCheckBox.setSelected(false);
      selectAllCheckBox.setText(CHECKBOX_SELECT_ALL);
    } else if (selectedCount == totalFiles) {
      selectAllCheckBox.setSelected(true);
      selectAllCheckBox.setText(CHECKBOX_UNSELECT_ALL);
    } else {
      selectAllCheckBox.setSelected(false);
      selectAllCheckBox.setText(CHECKBOX_SELECT_ALL);
    }
  }

  public List<String> getSelectedFiles() {
    return new ArrayList<>(selectedFiles);
  }

  public JPanel getMainPanel() {
    return (JPanel) mainPanel;
  }

  /**
   * 刷新数据 - 供外部调用
   * 根据当前显示状态刷新相应的文件列表
   */
  public void refreshData() {
    if (isShowingUncommitted) {
      loadUncommittedFiles();
    } else {
      loadCommittedFiles();
    }
  }

  /**
   * 设置工具栏
   */
  private void setToolbar(DefaultActionGroup actionGroup) {
    // 创建并设置 ActionToolbar
    ActionToolbar actionToolbar = ActionManager.getInstance().createActionToolbar("Intellicode.Toolbar", actionGroup,
        false);
    actionToolbar.setTargetComponent(mainPanel);
    // 添加 ActionToolbar 到主面板
    mainPanel.setToolbar(actionToolbar.getComponent());
  }

  /**
   * 更新工具栏（用于刷新时重新设置工具栏）
   */
  private void updateToolbar(DefaultActionGroup actionGroup) {
    // 移除旧的工具栏组件
    Component[] components = mainPanel.getComponents();
    for (Component component : components) {
      if (component instanceof JPanel) {
        JPanel panel = (JPanel) component;
        // 检查是否是工具栏面板（包含 ActionToolbar）
        Component[] panelComponents = panel.getComponents();
        for (Component panelComponent : panelComponents) {
          if (panelComponent.getClass().getName().contains("ActionToolbarImpl")) {
            mainPanel.remove(panel);
            break;
          }
        }
      }
    }
    
    // 设置新的工具栏
    setToolbar(actionGroup);
    addToolbarToMainPanel();
  }

  /**
   * 获取按钮栏动作组（根据当前状态）
   */
  private DefaultActionGroup getActionGroup() {
    if (isShowingAgent) {
      return getAgentActionGroup();
    } else if (isShowingHistory) {
      return getHistoryActionGroup();
    } else {
      return getHomeActionGroup();
    }
  }
  
  /**
   * 获取 Home 页面的工具栏配置
   */
  private DefaultActionGroup getHomeActionGroup() {
    DefaultActionGroup actionGroup = new DefaultActionGroup();
    if (org.apache.commons.lang3.StringUtils.isNotEmpty(settings.getUserToken())) {
      // 已登录状态的按钮
      actionGroup.add(new IntellicodeHomeAction(project));
      actionGroup.add(new Separator());
      actionGroup.add(new IntellicodeHistoryAction(project));
      actionGroup.add(new Separator());
      actionGroup.add(new HelpAction());
    } else {
      // 未登录状态只显示登录按钮
      actionGroup.add(new LoginAccountsAction(project));
    }
    return actionGroup;
  }
  
  /**
   * 获取 Agent 页面的工具栏配置
   */
  private DefaultActionGroup getAgentActionGroup() {
    DefaultActionGroup actionGroup = new DefaultActionGroup();
    if (org.apache.commons.lang3.StringUtils.isNotEmpty(settings.getUserToken())) {
      actionGroup.add(new IntellicodeHomeAction(project));
      actionGroup.add(new Separator());
      actionGroup.add(new IntellicodeHistoryAction(project));
      actionGroup.add(new Separator());
      actionGroup.add(new HelpAction());
      // 可以添加 Agent 特有的操作，如停止审查
    } else {
      actionGroup.add(new LoginAccountsAction(project));
    }
    return actionGroup;
  }
  
  /**
   * 获取历史记录页面的工具栏配置
   */
  private DefaultActionGroup getHistoryActionGroup() {
    DefaultActionGroup actionGroup = new DefaultActionGroup();
    if (org.apache.commons.lang3.StringUtils.isNotEmpty(settings.getUserToken())) {
      actionGroup.add(new IntellicodeHomeAction(project));
      actionGroup.add(new Separator());
      actionGroup.add(new IntellicodeHistoryAction(project));
      actionGroup.add(new Separator());
      actionGroup.add(new HelpAction());
      // 可以添加历史记录特有的操作，如清除历史
    } else {
      actionGroup.add(new LoginAccountsAction(project));
    }
    return actionGroup;
  }
  
  /**
   * 将工具栏添加到主面板
   */
  private void addToolbarToMainPanel() {
    // 获取工具栏组件
    JComponent toolbarComponent = mainPanel.getToolbar();
    if (toolbarComponent == null) {
      return;
    }
    
    // 创建分隔线
    JSeparator separator = new JSeparator();
    separator.setPreferredSize(new Dimension(0, 2));
    
    // 创建工具栏面板
    JPanel toolbarPanel = new JPanel(new BorderLayout());
    toolbarPanel.add(toolbarComponent, BorderLayout.CENTER);
    toolbarPanel.add(separator, BorderLayout.SOUTH);
    
    // 添加到主面板顶部
    mainPanel.add(toolbarPanel, BorderLayout.NORTH);
  }

  /**
   * 自定义文件列表渲染器 - 仿照 IntelliJ 原生样式
   */
  private class FileListCellRenderer extends JPanel implements ListCellRenderer<String> {
    private JBCheckBox checkBox;
    private JLabel iconLabel;
    private SimpleColoredComponent textComponent;

    public FileListCellRenderer() {
      setLayout(new BorderLayout());
      setBorder(JBUI.Borders.empty(1, 6, 1, 6)); // 更紧凑的边距
      setOpaque(true);

      // 左侧复选框 - 更小更紧凑
      checkBox = new JBCheckBox();
      checkBox.setOpaque(false);
      checkBox.setFocusPainted(false);
      checkBox.setBorder(JBUI.Borders.emptyRight(4));

      // 中间图标 - 更紧凑的间距
      iconLabel = new JLabel();
      iconLabel.setBorder(JBUI.Borders.emptyRight(4));

      // 右侧文本（文件名+路径）
      textComponent = new SimpleColoredComponent();

      // 布局：复选框 | 图标 | 文本
      JPanel leftPanel = new JPanel(new BorderLayout());
      leftPanel.setOpaque(false);
      leftPanel.add(checkBox, BorderLayout.WEST);
      leftPanel.add(iconLabel, BorderLayout.CENTER);

      add(leftPanel, BorderLayout.WEST);
      add(textComponent, BorderLayout.CENTER);
    }

    @Override
    public Component getListCellRendererComponent(JList<? extends String> list, String filePath,
        int index, boolean isSelected, boolean cellHasFocus) {

      // 设置背景色
      if (isSelected) {
        setBackground(JBUI.CurrentTheme.List.Selection.background(true));
      } else {
        setBackground(JBUI.CurrentTheme.DefaultTabs.background());
      }

      // 设置复选框状态
      checkBox.setSelected(selectedFiles.contains(filePath));

      // 设置文件图标
      setFileIcon(filePath);

      // 设置文件名和路径文本
      setFileText(filePath);

      return this;
    }

    private void setFileIcon(String filePath) {
      try {
        // 获取文件类型图标
        VirtualFile virtualFile = VirtualFileManager.getInstance().findFileByUrl("file://" + filePath);
        if (virtualFile != null) {
          FileType fileType = FileTypeManager.getInstance().getFileTypeByFile(virtualFile);
          Icon icon = fileType.getIcon();
          if (icon != null) {
            iconLabel.setIcon(icon);
            return;
          }
        }

        // 后备方案：根据扩展名判断
        String extension = getFileExtension(filePath);
        FileType fileType = FileTypeManager.getInstance().getFileTypeByExtension(extension);
        iconLabel.setIcon(fileType.getIcon());
      } catch (Exception e) {
        // 使用默认图标
        iconLabel.setIcon(FileTypeManager.getInstance().getFileTypeByExtension("txt").getIcon());
      }
    }

    private void setFileText(String filePath) {
      textComponent.clear();

      String fileName = getFileName(filePath);
      String relativePath = getRelativePath(filePath);

      // 文件名（正常色）
      textComponent.append(fileName, SimpleTextAttributes.REGULAR_ATTRIBUTES);

      // 路径（灰色）
      if (!relativePath.isEmpty()) {
        textComponent.append("  " + relativePath, SimpleTextAttributes.GRAYED_ATTRIBUTES);
      }
    }

    private String getFileExtension(String filePath) {
      int lastDot = filePath.lastIndexOf('.');
      if (lastDot > 0 && lastDot < filePath.length() - 1) {
        return filePath.substring(lastDot + 1);
      }
      return "";
    }

    private String getFileName(String fullPath) {
      int lastSlash = fullPath.lastIndexOf('/');
      if (lastSlash >= 0 && lastSlash < fullPath.length() - 1) {
        return fullPath.substring(lastSlash + 1);
      }
      return fullPath;
    }

    private String getRelativePath(String fullPath) {
      int lastSlash = fullPath.lastIndexOf('/');
      if (lastSlash > 0) {
        return fullPath.substring(0, lastSlash);
      }
      return "";
    }
  }

  /**
   * 处理文件列表点击事件
   */
  private void handleFileListClick(MouseEvent e) {
    int index = fileList.locationToIndex(e.getPoint());
    if (index >= 0) {
      String filePath = fileListModel.getElementAt(index);
      Rectangle cellBounds = fileList.getCellBounds(index, index);

      if (cellBounds != null && cellBounds.contains(e.getPoint())) {
        // 计算复选框区域（大约前 30 像素）
        if (e.getX() <= 30) {
          // 点击复选框区域，切换选择状态
          toggleFileSelection(filePath);
        } else {
          // 点击其他区域，打开文件
          gitService.openFileInEditor(filePath);
        }
      }
    }
  }

  /**
   * 切换文件选择状态
   */
  private void toggleFileSelection(String filePath) {
    if (selectedFiles.contains(filePath)) {
      selectedFiles.remove(filePath);
    } else {
      selectedFiles.add(filePath);
    }

    // 刷新列表显示
    fileList.repaint();

    // 更新按钮状态和全选复选框
    updateReviewButtonState();
    updateSelectAllCheckBox();
  }

  /**
   * 显示 Agent 页面
   */
  public void showAgentPanel(List<String> reviewFiles) {
    // 暂停历史页面的轮询
    if (historyPanel != null) {
      historyPanel.pauseAutoRefresh();
    }
    
    if (agentPanel == null) {
      agentPanel = new IntellicodeAgentPanel(project, reviewFiles);
      agentPanel.setHomePanel(this);
      Disposer.register(this, agentPanel);
    }

    // 清空内容区域并添加 Agent 页面内容
    mainPanel.removeAll();
    mainPanel.add(agentPanel.getMainPanel(), BorderLayout.CENTER);
    
    // 为 Agent 页面设置工具栏
    ActionToolbar actionToolbar = ActionManager.getInstance().createActionToolbar("Intellicode.Toolbar", getAgentActionGroup(), false);
    actionToolbar.setTargetComponent(agentPanel.getMainPanel());
    agentPanel.setToolbar(actionToolbar.getComponent());
    
    mainPanel.revalidate();
    mainPanel.repaint();

    isShowingAgent = true;
    isShowingHistory = false;

    // 启动审查过程
    startReview(reviewFiles);
  }

  /**
   * 显示历史记录页面
   */
  public void showHistoryPanel() {
    if (historyPanel == null) {
      historyPanel = new IntellicodeHistoryListPanel(project);
      historyPanel.setHomePanel(this);
      Disposer.register(this, historyPanel);
    }

    // 恢复历史页面的轮询
    historyPanel.resumeAutoRefresh();

    // 清空内容区域并添加历史记录页面内容
    mainPanel.removeAll();
    mainPanel.add(historyPanel.getMainPanel(), BorderLayout.CENTER);
    
    // 设置历史记录页面的工具栏（保持工具栏存在）
    setToolbar(getHistoryActionGroup());
    
    // 重新布局工具栏
    addToolbarToMainPanel();
    
    mainPanel.revalidate();
    mainPanel.repaint();

    isShowingHistory = true;
    isShowingAgent = false;
  }

  /**
   * 返回到 Home 页面
   */
  public void showHomePanel() {
    // 暂停历史页面的轮询
    if (historyPanel != null) {
      historyPanel.pauseAutoRefresh();
    }
    
    // 清空内容区域并添加 Home 页面内容
    mainPanel.removeAll();
    mainPanel.add(contentPanel, BorderLayout.CENTER);
    
    // 设置 Home 页面的工具栏
    setToolbar(getHomeActionGroup());
    
    // 重新布局工具栏
    addToolbarToMainPanel();
    
    mainPanel.revalidate();
    mainPanel.repaint();

    isShowingAgent = false;
    isShowingHistory = false;
    isShowingAgentWorkspace = false;
    
    // 不再主动释放子页面，保持它们存在以提高性能
    // 只在 IntellicodeHomePanel dispose 时统一清理
  }

  /**
   * 显示 AgentWorkspace 页面（历史模式）
   */
  public void showAgentWorkspaceHistoryMode(@NotNull String sessionId, int reviewId) {
    try {
      // 暂停历史页面的轮询
      if (historyPanel != null) {
        historyPanel.pauseAutoRefresh();
      }
      
      // 创建或重用 AgentWorkspace 面板
      if (agentWorkspacePanel != null) {
        Disposer.dispose(agentWorkspacePanel);
      }
      
      agentWorkspacePanel = new AgentWorkspacePanel(project, sessionId, reviewId);
      agentWorkspacePanel.setHomePanel(this);
      Disposer.register(this, agentWorkspacePanel);

      // 清空内容区域并添加 AgentWorkspace 页面内容
      mainPanel.removeAll();
      mainPanel.add(agentWorkspacePanel.getMainPanel(), BorderLayout.CENTER);
      
      // 设置 AgentWorkspace 页面的工具栏（复用历史页面工具栏配置）
      setToolbar(getHistoryActionGroup());
      
      // 重新布局工具栏
      addToolbarToMainPanel();
      
      mainPanel.revalidate();
      mainPanel.repaint();

      isShowingAgent = false;
      isShowingHistory = false;
      isShowingAgentWorkspace = true;
      
      LOG.info("Switched to AgentWorkspace history mode for sessionId: " + sessionId + ", reviewId: " + reviewId);
      
    } catch (Exception e) {
      LOG.error("Failed to show AgentWorkspace history mode", e);
      Messages.showErrorDialog(project, 
          "显示详情页面失败: " + e.getMessage(), 
          "显示失败");
    }
  }

  /**
   * 启动审查过程
   */
  private void startReview(List<String> reviewFiles) {
    IntellicodeAPI api = IntellicodeAPI.getInstance();

    // 直接使用 agentPanel 作为监听器，它已经实现了正确的 Markdown 格式化
    api.startReview(project, reviewFiles, agentPanel);
  }

  /**
   * 显示登录提示对话框
   */
  private void showLoginPrompt() {
    ApplicationManager.getApplication().invokeLater(() -> {
      int result = Messages.showYesNoDialog(
          project,
          "使用 Intellicode 功能需要先登录，是否现在登录？",
          "需要登录",
          "登录",
          "取消",
          Messages.getQuestionIcon());

      if (result == Messages.YES) {
        // 触发登录 Action
        LoginAccountsAction loginAction = new LoginAccountsAction(project);
        AnActionEvent event = AnActionEvent.createFromAnAction(loginAction, null, ActionPlaces.UNKNOWN,
            DataContext.EMPTY_CONTEXT);
        loginAction.actionPerformed(event);
      }
    });
  }

  /**
   * Review 按钮点击监听器
   */
  private class ReviewButtonListener implements ActionListener {
    @Override
    public void actionPerformed(ActionEvent e) {
      // 检查认证状态
      if (org.apache.commons.lang3.StringUtils.isEmpty(settings.getUserToken())) {
        showLoginPrompt();
        return;
      }

      List<String> selectedFiles = getSelectedFiles();
      if (selectedFiles.isEmpty()) {
        return;
      }

      // 跳转到 Agent 页面
      showAgentPanel(selectedFiles);
    }
  }

  /**
   * 实现 RefreshToolWindowNotifier 接口，响应登录状态变化
   */
  @Override
  public void refresh() {
    SwingUtilities.invokeLater(() -> {
      // 重新设置当前页面的工具栏
      if (isShowingAgent) {
        updateToolbar(getAgentActionGroup());
      } else if (isShowingHistory) {
        updateToolbar(getHistoryActionGroup());
      } else {
        updateToolbar(getHomeActionGroup());
      }
      
      // 重新验证和重绘界面
      mainPanel.revalidate();
      mainPanel.repaint();
    });
  }

  /**
   * 外部调用方法：切换到查看上次提交文件视图
   */
  public void switchToCommittedView() {
    SwingUtilities.invokeLater(() -> {
      try {
        // 确保当前显示的是home视图而不是agent或history
        if (isShowingAgent || isShowingHistory) {
          showHomePanel();
        }
        
        // 切换下拉框选项到"查看上次提交文件"
        if (reviewModeComboBox != null) {
          reviewModeComboBox.setSelectedItem(OPTION_COMMITTED);
          // 手动触发模式切换
          handleModeChange();
        }
      } catch (Exception e) {
        // 处理异常并记录日志
        System.err.println("Error in switchToCommittedView: " + e.getMessage());
        e.printStackTrace();
      }
    });
  }

  @Override
  public void dispose() {
    try {
      // 清理消息总线连接
      if (messageBusConnection != null) {
        messageBusConnection.disconnect();
        messageBusConnection = null;
      }
      
      if (agentPanel != null) {
        Disposer.dispose(agentPanel);
        agentPanel = null;
      }
      
      if (historyPanel != null) {
        Disposer.dispose(historyPanel);
        historyPanel = null;
      }
      
      if (agentWorkspacePanel != null) {
        Disposer.dispose(agentWorkspacePanel);
        agentWorkspacePanel = null;
      }

      if (fileListModel != null) {
        fileListModel.removeAll();
      }

      if (selectedFiles != null) {
        selectedFiles.clear();
      }
    } catch (Exception e) {
      System.err.println("Error disposing IntellicodeHomePanel: " + e.getMessage());
    }
  }
}
