package com.sentinel.nocalhost.intellicode.ui.handler;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.popup.Balloon;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.ui.JBColor;
import com.intellij.ui.awt.RelativePoint;
import com.intellij.icons.AllIcons;

import javax.swing.*;

/**
 * 处理建议的忽略操作
 */
public class IgnoreSuggestionHandler {
    private static final Logger LOG = Logger.getInstance(IgnoreSuggestionHandler.class);
    
    private final Project project;
    
    public IgnoreSuggestionHandler(Project project) {
        this.project = project;
    }
    
    /**
     * 忽略建议
     */
    public void ignoreSuggestion(intellicode.event.v1.EventOuterClass.Suggestion suggestion, 
                                JComponent sourceComponent,
                                Runnable onSuccess, 
                                Runnable onFailure) {
        
        try {
            // 记录忽略操作
            LOG.info("User ignored suggestion: " + suggestion.getDescription() + " for file: " + suggestion.getFilePath());
            
            // 显示忽略确认消息
            showIgnoredMessage(sourceComponent, "建议已忽略");
            
            // 执行成功回调
            if (onSuccess != null) {
                onSuccess.run();
            }
            
        } catch (Exception e) {
            LOG.error("Error ignoring suggestion", e);
            showError(sourceComponent, "忽略建议时发生错误: " + e.getMessage());
            
            if (onFailure != null) {
                onFailure.run();
            }
        }
    }
    
    /**
     * 批量忽略建议
     */
    public void ignoreAllSuggestions(java.util.List<intellicode.event.v1.EventOuterClass.Suggestion> suggestions,
                                   JComponent sourceComponent,
                                   Runnable onSuccess,
                                   Runnable onFailure) {
        try {
            for (intellicode.event.v1.EventOuterClass.Suggestion suggestion : suggestions) {
                LOG.info("User ignored suggestion (batch): " + suggestion.getDescription() + " for file: " + suggestion.getFilePath());
            }
            
            showIgnoredMessage(sourceComponent, "已忽略 " + suggestions.size() + " 个建议");
            
            if (onSuccess != null) {
                onSuccess.run();
            }
            
        } catch (Exception e) {
            LOG.error("Error ignoring all suggestions", e);
            showError(sourceComponent, "批量忽略建议时发生错误: " + e.getMessage());
            
            if (onFailure != null) {
                onFailure.run();
            }
        }
    }
    
    /**
     * 显示忽略确认消息
     */
    private void showIgnoredMessage(JComponent component, String message) {
        Balloon balloon = JBPopupFactory.getInstance()
                .createHtmlTextBalloonBuilder(message, AllIcons.General.Information, 
                    JBColor.YELLOW.darker(), null)
                .setFadeoutTime(2000)
                .createBalloon();
        balloon.show(RelativePoint.getCenterOf(component), Balloon.Position.above);
    }
    
    /**
     * 显示错误消息
     */
    private void showError(JComponent component, String message) {
        Balloon balloon = JBPopupFactory.getInstance()
                .createHtmlTextBalloonBuilder(message, AllIcons.General.NotificationError, 
                    JBColor.RED.brighter(), null)
                .setFadeoutTime(3000)
                .createBalloon();
        balloon.show(RelativePoint.getCenterOf(component), Balloon.Position.above);
        
        LOG.warn("Error in IgnoreSuggestionHandler: " + message);
    }
}