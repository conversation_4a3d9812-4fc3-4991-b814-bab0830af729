package com.sentinel.nocalhost.intellicode.ui;

import com.intellij.openapi.diagnostic.Logger;

/**
 * 消息流管理器
 * 负责管理IntellicodeAgentPanel中各种事件的状态转换和内容处理
 */
public class MessageFlowManager {
    private static final Logger LOG = Logger.getInstance(MessageFlowManager.class);
    
    /**
     * 消息状态枚举
     */
    public enum MessageState {
        IDLE,                    // 空闲状态，等待事件
        ASSISTANT_MESSAGE,       // 正在处理assistant消息
        TOOL_EXECUTION,         // 正在执行tool
        SESSION_ENDING          // 会话结束中
    }
    
    private MessageState currentState = MessageState.IDLE;
    private String currentAssistantMessageId;
    private StringBuilder pendingAssistantContent;
    private final MarkdownRenderer markdownRenderer;
    
    public MessageFlowManager(MarkdownRenderer markdownRenderer) {
        this.markdownRenderer = markdownRenderer;
        this.pendingAssistantContent = new StringBuilder();
    }
    
    /**
     * 获取当前状态
     */
    public MessageState getCurrentState() {
        return currentState;
    }
    
    /**
     * 开始会话
     */
    public void startSession(String sessionId) {
        LOG.debug("Session started: " + sessionId + ", current state: " + currentState);
        currentState = MessageState.IDLE;
        currentAssistantMessageId = null;
        pendingAssistantContent.setLength(0);
    }
    
    /**
     * 处理Assistant消息
     */
    public void handleAssistantMessage(String chunk) {
        LOG.debug("Handling assistant message, current state: " + currentState + ", chunk length: " + chunk.length());
        
        switch (currentState) {
            case IDLE:
                // 开始新的assistant消息
                startNewAssistantMessage();
                appendToAssistantMessage(chunk);
                break;
                
            case ASSISTANT_MESSAGE:
                // 继续当前assistant消息
                appendToAssistantMessage(chunk);
                break;
                
            case TOOL_EXECUTION:
                // tool执行期间收到assistant消息，缓存起来
                pendingAssistantContent.append(chunk);
                LOG.debug("Cached assistant content during tool execution, total cached: " + pendingAssistantContent.length());
                break;
                
            case SESSION_ENDING:
                // 会话结束期间忽略新的assistant消息
                LOG.debug("Ignoring assistant message during session ending");
                break;
        }
    }
    
    /**
     * 开始工具执行
     */
    public void startToolExecution(String toolName, String input) {
        LOG.debug("Starting tool execution: " + toolName + ", current state: " + currentState);
        
        switch (currentState) {
            case ASSISTANT_MESSAGE:
                // 如果当前有assistant消息在进行，先结束它
                finishCurrentAssistantMessage();
                break;
            case IDLE:
            case TOOL_EXECUTION:
                // 可以直接开始tool执行
                break;
            case SESSION_ENDING:
                LOG.warn("Starting tool execution during session ending");
                break;
        }
        
        currentState = MessageState.TOOL_EXECUTION;
        markdownRenderer.addToolStart(toolName, input);
    }
    
    /**
     * 结束工具执行
     */
    public void finishToolExecution(String toolName, String result, boolean success) {
        LOG.debug("Finishing tool execution: " + toolName + ", success: " + success + ", current state: " + currentState);
        
        if (currentState != MessageState.TOOL_EXECUTION) {
            LOG.warn("Finishing tool execution but current state is: " + currentState);
        }
        
        markdownRenderer.addToolEnd(toolName, result, success);
        
        // 检查是否有缓存的assistant内容需要处理
        if (pendingAssistantContent.length() > 0) {
            LOG.debug("Processing cached assistant content: " + pendingAssistantContent.length() + " chars");
            startNewAssistantMessage();
            appendToAssistantMessage(pendingAssistantContent.toString());
            pendingAssistantContent.setLength(0);
        } else {
            currentState = MessageState.IDLE;
        }
    }
    
    /**
     * 结束会话
     */
    public void endSession(String sessionId, boolean success) {
        LOG.debug("Ending session: " + sessionId + ", success: " + success + ", current state: " + currentState);
        
        // 确保当前的assistant消息正确结束
        if (currentState == MessageState.ASSISTANT_MESSAGE) {
            finishCurrentAssistantMessage();
        }
        
        currentState = MessageState.SESSION_ENDING;
        
        // 清理缓存的内容
        pendingAssistantContent.setLength(0);
        currentAssistantMessageId = null;
    }
    
    /**
     * 处理错误
     */
    public void handleError(Throwable error) {
        LOG.debug("Handling error, current state: " + currentState + ", error: " + error.getMessage());
        
        // 确保当前的assistant消息正确结束
        if (currentState == MessageState.ASSISTANT_MESSAGE) {
            finishCurrentAssistantMessage();
        }
        
        currentState = MessageState.IDLE;
        pendingAssistantContent.setLength(0);
        markdownRenderer.addError(error.getMessage());
    }
    
    /**
     * 强制结束当前的assistant消息（用于异常恢复）
     */
    public void forceFinishAssistantMessage() {
        if (currentState == MessageState.ASSISTANT_MESSAGE) {
            LOG.debug("Force finishing assistant message");
            finishCurrentAssistantMessage();
        }
    }
    
    /**
     * 开始新的Assistant消息
     */
    private void startNewAssistantMessage() {
        currentAssistantMessageId = generateMessageId();
        currentState = MessageState.ASSISTANT_MESSAGE;
        markdownRenderer.startAssistantMessage();
        LOG.debug("Started new assistant message: " + currentAssistantMessageId);
    }
    
    /**
     * 向当前Assistant消息追加内容
     */
    private void appendToAssistantMessage(String content) {
        markdownRenderer.appendAssistantChunk(content);
    }
    
    /**
     * 完成当前Assistant消息
     */
    private void finishCurrentAssistantMessage() {
        if (currentState == MessageState.ASSISTANT_MESSAGE) {
            markdownRenderer.finishAssistantMessage();
            currentState = MessageState.IDLE;
            LOG.debug("Finished assistant message: " + currentAssistantMessageId);
            currentAssistantMessageId = null;
        }
    }
    
    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
    
    /**
     * 重置状态（用于异常恢复）
     */
    public void reset() {
        LOG.debug("Resetting MessageFlowManager state");
        currentState = MessageState.IDLE;
        currentAssistantMessageId = null;
        pendingAssistantContent.setLength(0);
    }
    
    /**
     * 获取状态信息（用于调试）
     */
    public String getStateInfo() {
        return String.format("State: %s, AssistantMsgId: %s, PendingContent: %d chars", 
                currentState, currentAssistantMessageId, pendingAssistantContent.length());
    }
}