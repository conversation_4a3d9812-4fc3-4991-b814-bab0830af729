package com.sentinel.nocalhost.intellicode.ui;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.SimpleToolWindowPanel;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.util.ui.JBUI;
import com.intellij.ui.JBColor;
import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.editor.colors.EditorColorsScheme;
import com.intellij.openapi.diagnostic.Logger;
import com.sentinel.nocalhost.intellicode.api.IntellicodeAPI;
import com.sentinel.nocalhost.intellicode.api.IntellicodeSSEListener;
import com.sentinel.nocalhost.intellicode.session.IntellicodeSessionManager;
import com.sentinel.nocalhost.intellicode.ui.handler.AcceptSuggestionHandler;
import com.sentinel.nocalhost.intellicode.ui.handler.IgnoreSuggestionHandler;
import com.sentinel.nocalhost.intellicode.ui.model.SuggestionItem;
import com.sentinel.nocalhost.intellicode.ui.model.SuggestionState;

import javax.swing.*;
import javax.swing.text.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.util.Disposer;

/**
 * Intellicode Agent 页面
 * 显示实时的代码审查过程和结果
 */
public class IntellicodeAgentPanel implements IntellicodeSSEListener, Disposable {
    private static final Logger LOG = Logger.getInstance(IntellicodeAgentPanel.class);
    
    private final Project project;
    private final List<String> reviewFiles;
    private IntellicodeHomePanel homePanel;
    private IntellicodeSessionManager sessionManager;
    private IntellicodeSessionManager.SessionInfo currentSession;
    private SimpleToolWindowPanel mainPanel;
    private JPanel headerPanel;
    private JPanel contentPanel;
    private JPanel outputPanel;
    private JPanel suggestionsPanel;
    private JSplitPane splitPane;
    private JEditorPane outputArea;
    private JScrollPane outputScrollPane;
    private MarkdownRenderer markdownRenderer;
    private SimpleDateFormat timeFormat;
    private MessageFlowManager messageFlowManager;
    private List<SuggestionItem> suggestionItems;
    private JPanel suggestionsListPanel;
    private JScrollPane suggestionsScrollPane;
    private boolean isSuggestionsPanelVisible = false;
    private AcceptSuggestionHandler acceptHandler;
    private IgnoreSuggestionHandler ignoreHandler;
    private SuggestionState currentFilter = null; // null 表示显示所有建议
    private JBLabel suggestionStatsLabel;
    
    public IntellicodeAgentPanel(Project project, List<String> reviewFiles) {
        this.project = project;
        this.reviewFiles = reviewFiles;
        this.sessionManager = IntellicodeSessionManager.getInstance(project);
        this.acceptHandler = new AcceptSuggestionHandler(project);
        this.ignoreHandler = new IgnoreSuggestionHandler(project);
        this.suggestionItems = new java.util.ArrayList<>();
        initializeStyles();
        initializeUI();
    }
    
    private void initializeStyles() {
        timeFormat = new SimpleDateFormat("HH:mm:ss");
    }
    
    public void setHomePanel(IntellicodeHomePanel homePanel) {
        this.homePanel = homePanel;
    }
    
    private void initializeUI() {
        // 使用SimpleToolWindowPanel以支持工具栏
        mainPanel = new SimpleToolWindowPanel(false, true);
        mainPanel.setProvideQuickActions(false);
        
        // 创建内容容器
        JPanel contentContainer = new JPanel(new BorderLayout());
        contentContainer.setBorder(JBUI.Borders.empty(12));
        contentContainer.setBackground(JBUI.CurrentTheme.DefaultTabs.background());
        
        createHeaderPanel();
        createContentPanel();
        
        contentContainer.add(headerPanel, BorderLayout.NORTH);
        contentContainer.add(contentPanel, BorderLayout.CENTER);
        
        // 设置内容到SimpleToolWindowPanel
        mainPanel.setContent(contentContainer);
    }
    
    private void createHeaderPanel() {
        headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBorder(JBUI.Borders.emptyBottom(10));
        
        // 现在工具栏提供导航功能，不再需要页面内的返回按钮
        // 如果需要添加其他头部内容，可以在这里添加
    }
    
    private void createContentPanel() {
        contentPanel = new JPanel(new BorderLayout());
        
        // 创建主输出视图
        createOutputPanel();
        
        // 创建建议视图
        createSuggestionsPanel();
        
        // 使用 splitPane 布局，支持建议面板显示
        splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        splitPane.setTopComponent(outputScrollPane);
        splitPane.setBottomComponent(suggestionsPanel);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.7);
        splitPane.setContinuousLayout(true);
        
        contentPanel.add(splitPane, BorderLayout.CENTER);
        
        // 初始隐藏建议面板
        hideSuggestionsPanel();
    }
    
    private void createOutputPanel() {
        outputPanel = new JPanel(new BorderLayout());
        outputPanel.setBorder(JBUI.Borders.compound(
            JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()),
            JBUI.Borders.empty(5)
        ));
        
        // 输出文本区域 - 使用 JEditorPane 支持 HTML/Markdown 渲染
        outputArea = new JEditorPane();
        outputArea.setEditable(false);
        
        // 初始化 Markdown 渲染器
        markdownRenderer = new MarkdownRenderer(outputArea);
        
        // 初始化消息流管理器
        messageFlowManager = new MessageFlowManager(markdownRenderer);
        
        // 设置初始内容
        markdownRenderer.initializeContent(reviewFiles);
        
        outputScrollPane = new JBScrollPane(outputArea);
        outputScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        outputScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        
        // 设置滚动条默认在底部，避免每次渲染都触发滚动动作
        JScrollBar verticalScrollBar = outputScrollPane.getVerticalScrollBar();
        verticalScrollBar.addAdjustmentListener(e -> {
            // 当内容更新时，自动保持在底部，除非用户手动滚动了
            if (!e.getValueIsAdjusting()) {
                verticalScrollBar.setValue(verticalScrollBar.getMaximum());
            }
        });
        
        outputPanel.add(outputScrollPane, BorderLayout.CENTER);
    }
    
    
    private void createSuggestionsPanel() {
        suggestionsPanel = new JPanel(new BorderLayout());
        suggestionsPanel.setBorder(JBUI.Borders.compound(
            JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()),
            JBUI.Borders.empty(5)
        ));
        
        // 创建标题面板
        JPanel titlePanel = new JPanel(new BorderLayout());
        
        // 左侧：标题和状态统计
        JPanel leftTitlePanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        JBLabel suggestionsTitle = new JBLabel("Suggestions");
        suggestionsTitle.setFont(suggestionsTitle.getFont().deriveFont(Font.BOLD));
        leftTitlePanel.add(suggestionsTitle);
        
        // 状态统计标签
        suggestionStatsLabel = new JBLabel();
        suggestionStatsLabel.setFont(suggestionStatsLabel.getFont().deriveFont(Font.PLAIN, 11f));
        suggestionStatsLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        suggestionStatsLabel.setBorder(JBUI.Borders.emptyLeft(8));
        leftTitlePanel.add(suggestionStatsLabel);
        
        // 中间：筛选按钮
        JPanel filterPanel = createFilterPanel();
        
        // 创建批量操作按钮面板
        JPanel batchButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 0));
        
        JButton acceptAllButton = new JButton("Accept All");
        acceptAllButton.setFont(acceptAllButton.getFont().deriveFont(Font.PLAIN, 10f));
        acceptAllButton.setPreferredSize(new Dimension(70, 20));
        acceptAllButton.setToolTipText("接受所有建议");
        acceptAllButton.addActionListener(e -> acceptAllSuggestions());
        
        JButton ignoreAllButton = new JButton("Ignore All");
        ignoreAllButton.setFont(ignoreAllButton.getFont().deriveFont(Font.PLAIN, 10f));
        ignoreAllButton.setPreferredSize(new Dimension(70, 20));
        ignoreAllButton.setToolTipText("忽略所有建议");
        ignoreAllButton.addActionListener(e -> ignoreAllSuggestions());
        
        // 添加折叠/展开按钮
        JButton collapseButton = new JButton("∧");
        collapseButton.setPreferredSize(new Dimension(20, 20));
        collapseButton.setFont(collapseButton.getFont().deriveFont(Font.BOLD, 12f));
        collapseButton.setToolTipText("折叠/展开建议面板");
        collapseButton.addActionListener(e -> {
            toggleSuggestionsPanel();
            // 更新按钮图标
            collapseButton.setText(isSuggestionsPanelVisible ? "∧" : "∨");
        });
        
        batchButtonPanel.add(acceptAllButton);
        batchButtonPanel.add(ignoreAllButton);
        batchButtonPanel.add(collapseButton);
        
        titlePanel.add(leftTitlePanel, BorderLayout.WEST);
        titlePanel.add(filterPanel, BorderLayout.CENTER);
        titlePanel.add(batchButtonPanel, BorderLayout.EAST);
        titlePanel.setBorder(JBUI.Borders.emptyBottom(5));
        
        // 创建建议列表面板
        suggestionsListPanel = new JPanel();
        suggestionsListPanel.setLayout(new BoxLayout(suggestionsListPanel, BoxLayout.Y_AXIS));
        
        // 创建滚动面板
        suggestionsScrollPane = new JBScrollPane(suggestionsListPanel);
        suggestionsScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        suggestionsScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        
        // 默认显示无建议状态
        showNoSuggestionsMessage();
        
        suggestionsPanel.add(titlePanel, BorderLayout.NORTH);
        suggestionsPanel.add(suggestionsScrollPane, BorderLayout.CENTER);
    }
    
    /**
     * 创建筛选面板
     */
    private JPanel createFilterPanel() {
        JPanel filterPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 5, 0));
        filterPanel.setOpaque(false);
        
        // 筛选标签
        JBLabel filterLabel = new JBLabel("Filter:");
        filterLabel.setFont(filterLabel.getFont().deriveFont(Font.PLAIN, 11f));
        filterLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        filterPanel.add(filterLabel);
        
        // 全部按钮
        JButton allButton = createFilterButton("All", null);
        filterPanel.add(allButton);
        
        // 待处理按钮
        JButton pendingButton = createFilterButton("Pending", SuggestionState.PENDING);
        filterPanel.add(pendingButton);
        
        // 已应用按钮
        JButton appliedButton = createFilterButton("Applied", SuggestionState.APPLIED);
        filterPanel.add(appliedButton);
        
        // 已拒绝按钮
        JButton rejectedButton = createFilterButton("Rejected", SuggestionState.REJECTED);
        filterPanel.add(rejectedButton);
        
        return filterPanel;
    }
    
    /**
     * 创建筛选按钮
     */
    private JButton createFilterButton(String text, SuggestionState filterState) {
        JButton button = new JButton(text);
        button.setFont(button.getFont().deriveFont(Font.PLAIN, 10f));
        button.setPreferredSize(new Dimension(60, 18));
        button.setFocusPainted(false);
        
        // 设置初始样式
        updateFilterButtonStyle(button, currentFilter == filterState);
        
        button.addActionListener(e -> {
            currentFilter = filterState;
            refreshSuggestionsUI();
            updateAllFilterButtonStyles();
        });
        
        return button;
    }
    
    /**
     * 更新筛选按钮样式
     */
    private void updateFilterButtonStyle(JButton button, boolean isActive) {
        if (isActive) {
            button.setBackground(JBUI.CurrentTheme.Button.buttonColorStart());
            button.setForeground(JBColor.WHITE);
            button.setBorderPainted(true);
        } else {
            button.setBackground(JBUI.CurrentTheme.ActionButton.hoverBackground());
            button.setForeground(JBUI.CurrentTheme.Label.foreground());
            button.setBorderPainted(false);
        }
        button.setOpaque(true);
    }
    
    /**
     * 更新所有筛选按钮的样式
     */
    private void updateAllFilterButtonStyles() {
        // 这个方法需要访问筛选按钮，为简化实现，我们在refreshSuggestionsUI中更新统计
        // 实际项目中可以保存筛选按钮的引用来更新样式
    }
    
    /**
     * 显示无建议消息
     */
    private void showNoSuggestionsMessage() {
        suggestionsListPanel.removeAll();
        JBLabel noSuggestionsLabel = new JBLabel("No suggestions available");
        noSuggestionsLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        noSuggestionsLabel.setHorizontalAlignment(SwingConstants.CENTER);
        noSuggestionsLabel.setBorder(JBUI.Borders.empty(20));
        suggestionsListPanel.add(noSuggestionsLabel);
        suggestionsListPanel.revalidate();
        suggestionsListPanel.repaint();
    }
    
    /**
     * 更新建议显示
     */
    private void updateSuggestionsDisplay(List<intellicode.event.v1.EventOuterClass.Suggestion> suggestions) {
        SwingUtilities.invokeLater(() -> {
            if (suggestions == null || suggestions.isEmpty()) {
                showNoSuggestionsMessage();
                hideSuggestionsPanel();
                return;
            }
            
            // 将新的建议转换为 SuggestionItem，保持现有状态
            updateSuggestionItems(suggestions);
            
            // 显示建议面板
            showSuggestionsPanel();
            
            // 刷新UI显示
            refreshSuggestionsUI();
        });
    }
    
    /**
     * 更新建议项列表，保持已有状态
     */
    private void updateSuggestionItems(List<intellicode.event.v1.EventOuterClass.Suggestion> newSuggestions) {
        // 创建ID到现有项的映射
        java.util.Map<Integer, SuggestionItem> existingItems = new java.util.HashMap<>();
        for (SuggestionItem item : suggestionItems) {
            existingItems.put(item.getSuggestion().getId(), item);
        }
        
        // 清空列表并重新构建
        suggestionItems.clear();
        
        for (intellicode.event.v1.EventOuterClass.Suggestion suggestion : newSuggestions) {
            SuggestionItem existingItem = existingItems.get(suggestion.getId());
            if (existingItem != null) {
                // 保持现有状态，但更新建议数据
                suggestionItems.add(existingItem);
            } else {
                // 新建议项
                suggestionItems.add(new SuggestionItem(suggestion));
            }
        }
    }
    
    /**
     * 刷新建议UI显示
     */
    private void refreshSuggestionsUI() {
        suggestionsListPanel.removeAll();
        
        if (suggestionItems.isEmpty()) {
            showNoSuggestionsMessage();
            updateSuggestionsStats();
            return;
        }
        
        // 根据筛选条件过滤建议
        java.util.List<SuggestionItem> filteredItems = suggestionItems.stream()
            .filter(item -> currentFilter == null || item.getState() == currentFilter)
            .collect(java.util.stream.Collectors.toList());
        
        if (filteredItems.isEmpty()) {
            String filterText = currentFilter == null ? "All" : currentFilter.getText();
            JBLabel emptyLabel = new JBLabel("No " + filterText.toLowerCase() + " suggestions");
            emptyLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
            emptyLabel.setBorder(JBUI.Borders.empty(20));
            suggestionsListPanel.add(emptyLabel);
        } else {
            // 为每个筛选后的建议创建面板
            for (SuggestionItem item : filteredItems) {
                JPanel suggestionPanel = createSuggestionItemPanel(item);
                suggestionsListPanel.add(suggestionPanel);
                suggestionsListPanel.add(Box.createRigidArea(new Dimension(0, 2))); // 减少间距
            }
        }
        
        // 更新状态统计
        updateSuggestionsStats();
        
        suggestionsListPanel.revalidate();
        suggestionsListPanel.repaint();
    }
    
    /**
     * 更新建议状态统计
     */
    private void updateSuggestionsStats() {
        if (suggestionItems.isEmpty()) {
            suggestionStatsLabel.setText("");
            return;
        }
        
        int pendingCount = 0;
        int appliedCount = 0;
        int rejectedCount = 0;
        
        for (SuggestionItem item : suggestionItems) {
            switch (item.getState()) {
                case PENDING:
                    pendingCount++;
                    break;
                case APPLIED:
                    appliedCount++;
                    break;
                case REJECTED:
                    rejectedCount++;
                    break;
            }
        }
        
        int total = suggestionItems.size();
        
        StringBuilder stats = new StringBuilder();
        stats.append("(").append(total).append(" total");
        
        if (pendingCount > 0) {
            stats.append(", ").append(pendingCount).append(" pending");
        }
        if (appliedCount > 0) {
            stats.append(", ").append(appliedCount).append(" applied");
        }
        if (rejectedCount > 0) {
            stats.append(", ").append(rejectedCount).append(" rejected");
        }
        
        stats.append(")");
        
        suggestionStatsLabel.setText(stats.toString());
    }
    
    /**
     * 创建单个建议项的面板（新版本，支持折叠/展开）
     */
    private JPanel createSuggestionItemPanel(SuggestionItem item) {
        JPanel containerPanel = new JPanel(new BorderLayout());
        containerPanel.setBorder(JBUI.Borders.compound(
            JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()),
            JBUI.Borders.empty(2)
        ));
        
        // 创建折叠行 - 始终显示
        JPanel collapsedRow = createCollapsedRow(item);
        containerPanel.add(collapsedRow, BorderLayout.NORTH);
        
        // 创建展开区域 - 根据状态显示/隐藏
        if (item.isExpanded()) {
            JPanel expandedArea = createExpandedArea(item);
            containerPanel.add(expandedArea, BorderLayout.CENTER);
        }
        
        return containerPanel;
    }
    
    /**
     * 创建折叠行
     * 格式: [状态图标] [级别] [类别] 文件路径:行号 描述摘要
     */
    private JPanel createCollapsedRow(SuggestionItem item) {
        JPanel rowPanel = new JPanel(new BorderLayout());
        rowPanel.setBorder(JBUI.Borders.empty(4, 8));
        rowPanel.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        
        // 左侧：状态图标 + 级别 + 类别
        JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 5, 0));
        leftPanel.setOpaque(false);
        
        // 状态图标
        JBLabel stateIcon = createStateIcon(item.getState());
        leftPanel.add(stateIcon);
        
        // 级别标签
        JBLabel levelLabel = createLevelLabel(item);
        leftPanel.add(levelLabel);
        
        // 类别标签
        JBLabel categoryLabel = new JBLabel("[" + item.getSuggestion().getCategory() + "]");
        categoryLabel.setFont(categoryLabel.getFont().deriveFont(Font.PLAIN, 11f));
        categoryLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        leftPanel.add(categoryLabel);
        
        // 中间：文件信息 + 描述摘要
        JPanel centerPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 8, 0));
        centerPanel.setOpaque(false);
        
        // 文件信息
        JBLabel fileLabel = new JBLabel(item.getFileInfo());
        fileLabel.setFont(fileLabel.getFont().deriveFont(Font.BOLD, 11f));
        fileLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
        centerPanel.add(fileLabel);
        
        // 描述摘要
        JBLabel summaryLabel = new JBLabel(item.getSummary());
        summaryLabel.setFont(summaryLabel.getFont().deriveFont(Font.PLAIN, 11f));
        summaryLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
        centerPanel.add(summaryLabel);
        
        // 右侧：展开/折叠指示器
        JBLabel expandIndicator = new JBLabel(item.isExpanded() ? "▼" : "▶");
        expandIndicator.setFont(expandIndicator.getFont().deriveFont(Font.PLAIN, 10f));
        expandIndicator.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        
        rowPanel.add(leftPanel, BorderLayout.WEST);
        rowPanel.add(centerPanel, BorderLayout.CENTER);
        rowPanel.add(expandIndicator, BorderLayout.EAST);
        
        // 添加点击事件，切换展开/折叠
        java.awt.event.MouseAdapter clickListener = new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                item.toggleExpanded();
                refreshSuggestionsUI(); // 刷新UI
            }
            
            @Override
            public void mouseEntered(java.awt.event.MouseEvent e) {
                if (!item.isProcessed()) {
                    rowPanel.setBackground(JBUI.CurrentTheme.ActionButton.hoverBackground());
                } else {
                    rowPanel.setBackground(JBUI.CurrentTheme.ActionButton.hoverBackground().brighter());
                }
                rowPanel.setOpaque(true);
            }
            
            @Override
            public void mouseExited(java.awt.event.MouseEvent e) {
                rowPanel.setOpaque(false);
            }
        };
        
        rowPanel.addMouseListener(clickListener);
        
        // 添加键盘支持
        rowPanel.setFocusable(true);
        rowPanel.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyPressed(java.awt.event.KeyEvent e) {
                if (e.getKeyCode() == java.awt.event.KeyEvent.VK_SPACE || 
                    e.getKeyCode() == java.awt.event.KeyEvent.VK_ENTER) {
                    item.toggleExpanded();
                    refreshSuggestionsUI();
                }
            }
        });
        
        return rowPanel;
    }
    
    /**
     * 创建状态文字标签
     */
    private JBLabel createStateIcon(SuggestionState state) {
        JBLabel statusLabel = new JBLabel(state.getText());
        statusLabel.setFont(JBUI.Fonts.label(11).asBold());
        statusLabel.setForeground(state.getColor());
        statusLabel.setToolTipText(state.getText());
        
        return statusLabel;
    }
    
    // 保留原有的颜色设置逻辑以防其他地方需要
    private void setStateIconColor(JBLabel iconLabel, SuggestionState state) {
        switch (state) {
            case PENDING:
                iconLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
                break;
            case APPLIED:
                iconLabel.setForeground(JBColor.GREEN.darker());
                break;
            case REJECTED:
                iconLabel.setForeground(JBColor.RED.darker());
                break;
        }
    }
    
    /**
     * 创建级别标签
     */
    private JBLabel createLevelLabel(SuggestionItem item) {
        JBLabel levelLabel = new JBLabel("[" + item.getLevelDisplay() + "]");
        levelLabel.setOpaque(true);
        levelLabel.setBorder(JBUI.Borders.empty(1, 4));
        levelLabel.setFont(levelLabel.getFont().deriveFont(Font.BOLD, 10f));
        
        // 根据级别设置颜色
        switch (item.getSuggestion().getLevel()) {
            case LEVEL_CRITICAL:
            case LEVEL_BLOCKER:
                levelLabel.setBackground(JBColor.RED);
                levelLabel.setForeground(JBColor.WHITE);
                break;
            case LEVEL_MAJOR:
                levelLabel.setBackground(JBColor.ORANGE);
                levelLabel.setForeground(JBColor.WHITE);
                break;
            case LEVEL_MINOR:
                levelLabel.setBackground(JBColor.YELLOW);
                levelLabel.setForeground(JBColor.BLACK);
                break;
            case LEVEL_INFO:
                levelLabel.setBackground(JBColor.BLUE);
                levelLabel.setForeground(JBColor.WHITE);
                break;
            default:
                levelLabel.setBackground(JBColor.GRAY);
                levelLabel.setForeground(JBColor.WHITE);
                break;
        }
        
        return levelLabel;
    }
    
    /**
     * 创建展开区域
     */
    private JPanel createExpandedArea(SuggestionItem item) {
        JPanel expandedPanel = new JPanel(new BorderLayout());
        expandedPanel.setBorder(JBUI.Borders.empty(8, 20, 8, 8)); // 左侧缩进
        
        intellicode.event.v1.EventOuterClass.Suggestion suggestion = item.getSuggestion();
        
        // 详细描述
        JBLabel descriptionLabel = new JBLabel("<html>" + suggestion.getDescription() + "</html>");
        descriptionLabel.setBorder(JBUI.Borders.emptyBottom(8));
        expandedPanel.add(descriptionLabel, BorderLayout.NORTH);
        
        // 代码对比区域
        JPanel codeComparePanel = createCodeComparePanel(suggestion);
        expandedPanel.add(codeComparePanel, BorderLayout.CENTER);
        
        // 操作按钮区域
        if (item.isPending()) {
            JPanel actionPanel = createActionPanel(item);
            expandedPanel.add(actionPanel, BorderLayout.SOUTH);
        }
        
        return expandedPanel;
    }
    
    /**
     * 创建代码对比面板
     */
    private JPanel createCodeComparePanel(intellicode.event.v1.EventOuterClass.Suggestion suggestion) {
        JPanel codePanel = new JPanel(new GridLayout(1, 2, 8, 0));
        
        // 当前代码
        JPanel currentPanel = new JPanel(new BorderLayout());
        JBLabel currentTitle = new JBLabel("Current:");
        currentTitle.setFont(currentTitle.getFont().deriveFont(Font.BOLD, 10f));
        
        JTextArea currentArea = new JTextArea(suggestion.getOldString());
        currentArea.setEditable(false);
        currentArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        currentArea.setBackground(JBUI.CurrentTheme.EditorTabs.background());
        
        JScrollPane currentScroll = new JBScrollPane(currentArea);
        currentScroll.setPreferredSize(new Dimension(200, 80)); // 稍微增加高度
        currentScroll.setBorder(JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()));
        
        currentPanel.add(currentTitle, BorderLayout.NORTH);
        currentPanel.add(currentScroll, BorderLayout.CENTER);
        
        // 建议代码
        JPanel suggestedPanel = new JPanel(new BorderLayout());
        JBLabel suggestedTitle = new JBLabel("Suggested:");
        suggestedTitle.setFont(suggestedTitle.getFont().deriveFont(Font.BOLD, 10f));
        
        JTextArea suggestedArea = new JTextArea(suggestion.getNewString());
        suggestedArea.setEditable(false);
        suggestedArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        suggestedArea.setBackground(JBUI.CurrentTheme.EditorTabs.background());
        
        JScrollPane suggestedScroll = new JScrollPane(suggestedArea);
        suggestedScroll.setPreferredSize(new Dimension(200, 80));
        suggestedScroll.setBorder(JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()));
        
        suggestedPanel.add(suggestedTitle, BorderLayout.NORTH);
        suggestedPanel.add(suggestedScroll, BorderLayout.CENTER);
        
        codePanel.add(currentPanel);
        codePanel.add(suggestedPanel);
        
        return codePanel;
    }
    
    /**
     * 创建操作按钮面板
     */
    private JPanel createActionPanel(SuggestionItem item) {
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 5, 8));
        
        JButton ignoreButton = new JButton("Ignore");
        JButton acceptButton = new JButton("Accept");
        
        ignoreButton.setPreferredSize(new Dimension(70, 25));
        acceptButton.setPreferredSize(new Dimension(70, 25));
        
        // 添加按钮事件处理
        ignoreButton.addActionListener(e -> {
            ignoreButton.setEnabled(false);
            acceptButton.setEnabled(false);
            
            ignoreHandler.ignoreSuggestion(item.getSuggestion(), buttonPanel,
                () -> {
                    // 成功回调：更新状态
                    item.setState(SuggestionState.REJECTED);
                    item.setExpanded(false); // 折叠已处理的建议
                    refreshSuggestionsUI();
                },
                () -> {
                    // 失败回调：重新启用按钮
                    ignoreButton.setEnabled(true);
                    acceptButton.setEnabled(true);
                }
            );
        });
        
        acceptButton.addActionListener(e -> {
            acceptButton.setEnabled(false);
            ignoreButton.setEnabled(false);
            
            acceptHandler.acceptSuggestion(item.getSuggestion(), buttonPanel,
                () -> {
                    // 成功回调：更新状态
                    item.setState(SuggestionState.APPLIED);
                    item.setExpanded(false); // 折叠已处理的建议
                    refreshSuggestionsUI();
                },
                () -> {
                    // 失败回调：重新启用按钮
                    acceptButton.setEnabled(true);
                    ignoreButton.setEnabled(true);
                }
            );
        });
        
        buttonPanel.add(ignoreButton);
        buttonPanel.add(acceptButton);
        
        return buttonPanel;
    }
    
    /**
     * 创建单个建议的面板（旧版本，保持兼容性）
     */
    private JPanel createSuggestionPanel(intellicode.event.v1.EventOuterClass.Suggestion suggestion) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(JBUI.Borders.compound(
            JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()),
            JBUI.Borders.empty(8)
        ));
        
        // 创建头部信息面板
        JPanel headerPanel = new JPanel(new BorderLayout());
        
        // 左侧：级别和类别
        JPanel leftHeaderPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        
        // 级别标签
        JBLabel levelLabel = new JBLabel(suggestion.getLevel().name());
        levelLabel.setOpaque(true);
        levelLabel.setBorder(JBUI.Borders.empty(2, 6));
        levelLabel.setFont(levelLabel.getFont().deriveFont(Font.BOLD, 10f));
        
        // 根据级别设置颜色
        switch (suggestion.getLevel()) {
            case LEVEL_CRITICAL:
            case LEVEL_BLOCKER:
                levelLabel.setBackground(JBColor.RED);
                levelLabel.setForeground(JBColor.WHITE);
                break;
            case LEVEL_MAJOR:
                levelLabel.setBackground(JBColor.ORANGE);
                levelLabel.setForeground(JBColor.WHITE);
                break;
            case LEVEL_MINOR:
                levelLabel.setBackground(JBColor.YELLOW);
                levelLabel.setForeground(JBColor.BLACK);
                break;
            case LEVEL_INFO:
                levelLabel.setBackground(JBColor.BLUE);
                levelLabel.setForeground(JBColor.WHITE);
                break;
            default:
                levelLabel.setBackground(JBColor.GRAY);
                levelLabel.setForeground(JBColor.WHITE);
                break;
        }
        
        // 类别标签
        JBLabel categoryLabel = new JBLabel(" " + suggestion.getCategory());
        categoryLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        categoryLabel.setFont(categoryLabel.getFont().deriveFont(Font.ITALIC, 10f));
        
        leftHeaderPanel.add(levelLabel);
        leftHeaderPanel.add(categoryLabel);
        
        // 右侧：文件路径和行号
        String fileInfo = suggestion.getFilePath() + ":" + suggestion.getStartLine();
        if (suggestion.getEndLine() != suggestion.getStartLine()) {
            fileInfo += "-" + suggestion.getEndLine();
        }
        JBLabel fileLabel = new JBLabel(fileInfo);
        fileLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        fileLabel.setFont(fileLabel.getFont().deriveFont(Font.PLAIN, 10f));
        
        headerPanel.add(leftHeaderPanel, BorderLayout.WEST);
        headerPanel.add(fileLabel, BorderLayout.EAST);
        
        // 描述标签
        JBLabel descriptionLabel = new JBLabel("<html>" + suggestion.getDescription() + "</html>");
        descriptionLabel.setBorder(JBUI.Borders.empty(5, 0));
        
        // 代码对比面板
        JPanel codePanel = new JPanel(new GridLayout(1, 2, 10, 0));
        
        // 原始代码
        JPanel oldCodePanel = new JPanel(new BorderLayout());
        JBLabel oldCodeTitle = new JBLabel("Current:");
        oldCodeTitle.setFont(oldCodeTitle.getFont().deriveFont(Font.BOLD, 10f));
        JTextArea oldCodeArea = new JTextArea(suggestion.getOldString());
        oldCodeArea.setEditable(false);
        oldCodeArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        oldCodeArea.setBackground(JBUI.CurrentTheme.EditorTabs.background());
        JScrollPane oldScrollPane = new JBScrollPane(oldCodeArea);
        oldScrollPane.setPreferredSize(new Dimension(200, 60));
        oldScrollPane.setBorder(JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()));
        
        oldCodePanel.add(oldCodeTitle, BorderLayout.NORTH);
        oldCodePanel.add(oldScrollPane, BorderLayout.CENTER);
        
        // 建议代码
        JPanel newCodePanel = new JPanel(new BorderLayout());
        JBLabel newCodeTitle = new JBLabel("Suggested:");
        newCodeTitle.setFont(newCodeTitle.getFont().deriveFont(Font.BOLD, 10f));
        JTextArea newCodeArea = new JTextArea(suggestion.getNewString());
        newCodeArea.setEditable(false);
        newCodeArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        newCodeArea.setBackground(JBUI.CurrentTheme.EditorTabs.background());
        JScrollPane newScrollPane = new JScrollPane(newCodeArea);
        newScrollPane.setPreferredSize(new Dimension(200, 60));
        newScrollPane.setBorder(JBUI.Borders.customLine(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()));
        
        newCodePanel.add(newCodeTitle, BorderLayout.NORTH);
        newCodePanel.add(newScrollPane, BorderLayout.CENTER);
        
        codePanel.add(oldCodePanel);
        codePanel.add(newCodePanel);
        
        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton acceptButton = new JButton("Accept");
        JButton ignoreButton = new JButton("Ignore");
        
        acceptButton.setPreferredSize(new Dimension(70, 25));
        ignoreButton.setPreferredSize(new Dimension(70, 25));
        
        // 添加按钮事件处理
        acceptButton.addActionListener(e -> {
            acceptButton.setEnabled(false);
            acceptHandler.acceptSuggestion(suggestion, panel,
                () -> {
                    // 成功回调：从建议列表中移除这个建议
                    removeSuggestionFromDisplay(suggestion);
                },
                () -> {
                    // 失败回调：重新启用按钮
                    acceptButton.setEnabled(true);
                }
            );
        });
        
        ignoreButton.addActionListener(e -> {
            ignoreButton.setEnabled(false);
            ignoreHandler.ignoreSuggestion(suggestion, panel,
                () -> {
                    // 成功回调：从建议列表中移除这个建议
                    removeSuggestionFromDisplay(suggestion);
                },
                () -> {
                    // 失败回调：重新启用按钮
                    ignoreButton.setEnabled(true);
                }
            );
        });
        
        buttonPanel.add(ignoreButton);
        buttonPanel.add(acceptButton);
        
        // 组装面板
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.add(descriptionLabel, BorderLayout.NORTH);
        contentPanel.add(codePanel, BorderLayout.CENTER);
        contentPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        panel.add(headerPanel, BorderLayout.NORTH);
        panel.add(contentPanel, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 从显示中移除指定的建议（已废弃，现在使用状态更新）
     */
    @Deprecated
    private void removeSuggestionFromDisplay(intellicode.event.v1.EventOuterClass.Suggestion suggestion) {
        // 现在我们不再移除建议，而是更新状态
        SwingUtilities.invokeLater(() -> {
            for (SuggestionItem item : suggestionItems) {
                if (item.getSuggestion().getId() == suggestion.getId()) {
                    // 根据需要更新状态，这里设为已处理但不确定具体状态
                    // 实际状态应该在操作按钮中设置
                    LOG.debug("Suggestion " + suggestion.getId() + " should be updated via state change, not removal");
                    break;
                }
            }
        });
    }
    
    /**
     * 清空所有建议
     */
    private void clearAllSuggestions() {
        SwingUtilities.invokeLater(() -> {
            suggestionItems.clear();
            refreshSuggestionsUI();
            LOG.debug("Cleared all suggestions from display");
        });
    }
    
    /**
     * 接受所有建议
     */
    private void acceptAllSuggestions() {
        if (suggestionItems.isEmpty()) {
            return;
        }
        
        // 只处理未处理的建议
        java.util.List<SuggestionItem> pendingItems = suggestionItems.stream()
            .filter(SuggestionItem::isPending)
            .collect(java.util.stream.Collectors.toList());
            
        if (pendingItems.isEmpty()) {
            return;
        }
        
        int totalSuggestions = pendingItems.size();
        final int[] processedCount = {0};
        final int[] successCount = {0};
        
        for (SuggestionItem item : pendingItems) {
            acceptHandler.acceptSuggestion(item.getSuggestion(), suggestionsPanel,
                () -> {
                    // 成功回调
                    item.setState(SuggestionState.APPLIED);
                    item.setExpanded(false);
                    processedCount[0]++;
                    successCount[0]++;
                    
                    if (processedCount[0] == totalSuggestions) {
                        // 所有建议处理完成，刷新UI
                        refreshSuggestionsUI();
                        LOG.info("Batch accept completed: " + successCount[0] + "/" + totalSuggestions + " successful");
                    }
                },
                () -> {
                    // 失败回调
                    processedCount[0]++;
                    
                    if (processedCount[0] == totalSuggestions) {
                        // 所有建议处理完成，刷新显示
                        refreshSuggestionsUI();
                        LOG.info("Batch accept completed: " + successCount[0] + "/" + totalSuggestions + " successful");
                    }
                }
            );
        }
    }
    
    /**
     * 忽略所有建议
     */
    private void ignoreAllSuggestions() {
        if (suggestionItems.isEmpty()) {
            return;
        }
        
        // 只处理未处理的建议
        java.util.List<SuggestionItem> pendingItems = suggestionItems.stream()
            .filter(SuggestionItem::isPending)
            .collect(java.util.stream.Collectors.toList());
            
        if (pendingItems.isEmpty()) {
            return;
        }
        
        // 提取建议列表用于批量操作
        java.util.List<intellicode.event.v1.EventOuterClass.Suggestion> pendingSuggestions = 
            pendingItems.stream()
                .map(SuggestionItem::getSuggestion)
                .collect(java.util.stream.Collectors.toList());
        
        // 批量忽略所有待处理建议
        ignoreHandler.ignoreAllSuggestions(pendingSuggestions, suggestionsPanel,
            () -> {
                // 成功回调：更新所有待处理建议的状态
                for (SuggestionItem item : pendingItems) {
                    item.setState(SuggestionState.REJECTED);
                    item.setExpanded(false);
                }
                refreshSuggestionsUI();
            },
            () -> {
                // 失败回调
                LOG.warn("Failed to ignore all suggestions");
            }
        );
    }
    
    /**
     * 获取消息流管理器状态信息（用于调试）
     */
    public String getMessageFlowState() {
        return messageFlowManager.getStateInfo();
    }
    
    /**
     * 重置消息流管理器（用于异常恢复）
     */
    public void resetMessageFlow() {
        LOG.warn("Resetting MessageFlowManager due to error or manual request");
        messageFlowManager.reset();
    }
    
    /**
     * 更新进度状态（进度条已移除，此方法保留用于兼容性）
     */
    public void updateProgress(int value, String message) {
        // 进度条已移除，此方法暂时保留用于兼容性
        // 可以在此添加其他状态更新逻辑，如状态栏显示等
    }
    
    /**
     * 更新状态（兼容性方法）
     */
    public void updateStatus(String status) {
        updateProgress(0, status);
    }
    
    /**
     * 添加输出（兼容性方法，用于简单文本输出）
     */
    public void appendOutput(String text) {
        // 使用MessageFlowManager统一处理所有文本内容
        messageFlowManager.handleAssistantMessage("\n" + text);
    }
    
    
    public SimpleToolWindowPanel getMainPanel() {
        return mainPanel;
    }
    
    /**
     * 设置工具栏
     */
    public void setToolbar(javax.swing.JComponent toolbar) {
        if (mainPanel != null) {
            mainPanel.setToolbar(toolbar);
        }
    }
    
    /**
     * 显示建议面板
     */
    private void showSuggestionsPanel() {
        if (splitPane != null && !isSuggestionsPanelVisible) {
            // 确保建议面板已经添加到 splitPane
            splitPane.setBottomComponent(suggestionsPanel);
            // 设置合适的分割位置（70% 上方，30% 下方）
            SwingUtilities.invokeLater(() -> {
                splitPane.setDividerLocation(0.7);
                suggestionsPanel.setVisible(true);
            });
            isSuggestionsPanelVisible = true;
            LOG.debug("Suggestions panel is now visible");
        }
    }
    
    /**
     * 隐藏建议面板
     */
    private void hideSuggestionsPanel() {
        if (splitPane != null && isSuggestionsPanelVisible) {
            // 设置分割位置到最底部，隐藏建议面板
            splitPane.setDividerLocation(1.0);
            suggestionsPanel.setVisible(false);
            isSuggestionsPanelVisible = false;
            LOG.debug("Suggestions panel is now hidden");
        } else if (splitPane != null && !isSuggestionsPanelVisible) {
            // 初始隐藏状态
            splitPane.setDividerLocation(1.0);
            suggestionsPanel.setVisible(false);
        }
    }
    
    /**
     * 切换建议面板的显示/隐藏状态
     */
    private void toggleSuggestionsPanel() {
        if (isSuggestionsPanelVisible) {
            hideSuggestionsPanel();
        } else {
            showSuggestionsPanel();
        }
    }
    
    // SSE监听器实现
    @Override
    public void onSessionStart(String sessionId, List<String> files) {
        SwingUtilities.invokeLater(() -> {
            currentSession = sessionManager.getSession(sessionId);
            sessionManager.startSession(sessionId, files);
            markdownRenderer.addSessionStart(sessionId, files);
            messageFlowManager.startSession(sessionId);
            updateProgress(10, "Session started");
            LOG.debug("Session started, MessageFlowManager state: " + messageFlowManager.getStateInfo());
        });
    }
    
    @Override
    public void onSessionEnd(String sessionId, boolean success) {
        SwingUtilities.invokeLater(() -> {
            messageFlowManager.endSession(sessionId, success);
            sessionManager.endSession(sessionId, success);
            markdownRenderer.addSessionEnd(sessionId, success);
            updateProgress(100, "Review " + (success ? "completed" : "failed"));
            
            // 按需求始终隐藏建议面板
            hideSuggestionsPanel();
            LOG.debug("Session ended, MessageFlowManager state: " + messageFlowManager.getStateInfo());
        });
    }
    
    @Override
    public void onToolStart(String toolName, String input) {
        SwingUtilities.invokeLater(() -> {
            messageFlowManager.startToolExecution(toolName, input);
            LOG.debug("Tool started: " + toolName + ", MessageFlowManager state: " + messageFlowManager.getStateInfo());
        });
    }
    
    @Override
    public void onToolEnd(String toolName, String result, boolean success) {
        SwingUtilities.invokeLater(() -> {
            messageFlowManager.finishToolExecution(toolName, result, success);
            LOG.debug("Tool ended: " + toolName + ", success: " + success + ", MessageFlowManager state: " + messageFlowManager.getStateInfo());
        });
    }
    
    @Override
    public void onAssistantMessage(String message) {
        // 使用MessageFlowManager统一处理assistant消息
        SwingUtilities.invokeLater(() -> {
            messageFlowManager.handleAssistantMessage(message);
            LOG.debug("Assistant message received, length: " + message.length() + ", MessageFlowManager state: " + messageFlowManager.getStateInfo());
        });
    }
    
    @Override
    public void onSuggestions(List<intellicode.event.v1.EventOuterClass.Suggestion> suggestions) {
        SwingUtilities.invokeLater(() -> {
            // 启用建议面板显示
            updateSuggestionsDisplay(suggestions);
            LOG.debug("Received " + suggestions.size() + " suggestions, updating display");
        });
    }
    
    @Override
    public void onError(Throwable error) {
        SwingUtilities.invokeLater(() -> {
            messageFlowManager.handleError(error);
            LOG.error("Error occurred in IntellicodeAgent: " + error.getMessage() + ", MessageFlowManager state: " + messageFlowManager.getStateInfo(), error);
        });
    }
    
    
    
    @Override
    public void dispose() {
        try {
            if (messageFlowManager != null) {
                messageFlowManager.reset();
            }
            
            if (markdownRenderer != null) {
                Disposer.dispose(markdownRenderer);
            }
            
            if (currentSession != null && sessionManager != null) {
                sessionManager.endSession(currentSession.sessionId, false);
            }
            
            if (suggestionItems != null) {
                suggestionItems.clear();
            }
            
            if (outputArea != null) {
                outputArea.removeAll();
                outputArea = null;
            }
            
            if (splitPane != null) {
                splitPane.removeAll();
                splitPane = null;
            }
            
            LOG.debug("IntellicodeAgentPanel disposed");
        } catch (Exception e) {
            LOG.warn("Error disposing IntellicodeAgentPanel", e);
        }
    }
}