package com.sentinel.nocalhost.intellicode.ui.model;

import com.intellij.ui.JBColor;

import java.awt.Color;

/**
 * 建议状态枚举
 * 符合设计文档要求的状态管理，使用文字显示状态
 */
public enum SuggestionState {
    /**
     * 待处理状态 - 蓝色文字显示
     */
    PENDING("待处理", new JBColor(0x1976D2, 0x42A5F5)),
    
    /**
     * 已应用状态 - 绿色文字显示
     */
    APPLIED("已应用", new JBColor(0x388E3C, 0x66BB6A)),
    
    /**
     * 已拒绝状态 - 红色文字显示
     */
    REJECTED("已拒绝", new JBColor(0xD32F2F, 0xEF5350));
    
    private final String text;
    private final JBColor color;
    
    SuggestionState(String text, JBColor color) {
        this.text = text;
        this.color = color;
    }
    
    public String getText() {
        return text;
    }
    
    public JBColor getColor() {
        return color;
    }
    
    /**
     * 保持向后兼容性
     * @deprecated 使用 getText() 代替
     */
    @Deprecated
    public String getDescription() {
        return text;
    }
    
    /**
     * 检查是否为已处理状态
     */
    public boolean isProcessed() {
        return this == APPLIED || this == REJECTED;
    }
    
    /**
     * 检查是否为成功状态
     */
    public boolean isSuccess() {
        return this == APPLIED;
    }
    
    /**
     * 根据状态名称获取对应的枚举值
     */
    public static SuggestionState fromStatusName(String statusName, String actionName) {
        if ("SUGGESTION_STATUS_UNDONE".equals(statusName)) {
            return PENDING;
        } else if ("SUGGESTION_STATUS_DONE".equals(statusName)) {
            if ("FEEDBACK_ACTION_AGREE".equals(actionName)) {
                return APPLIED;
            } else if ("FEEDBACK_ACTION_REJECT".equals(actionName)) {
                return REJECTED;
            } else {
                return APPLIED; // 默认为已应用状态
            }
        } else {
            return PENDING; // 未知状态默认为待处理
        }
    }
}