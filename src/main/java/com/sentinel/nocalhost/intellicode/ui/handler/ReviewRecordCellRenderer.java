package com.sentinel.nocalhost.intellicode.ui.handler;

import com.intellij.icons.AllIcons;
import com.intellij.util.ui.JBUI;
import com.sentinel.nocalhost.intellicode.ui.model.ReviewRecordItem;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;

/**
 * 审查记录列表项渲染器
 * 负责在历史记录列表中显示每个审查记录的UI
 */
public class ReviewRecordCellRenderer implements ListCellRenderer<ReviewRecordItem> {
    
    @Override
    public Component getListCellRendererComponent(JList<? extends ReviewRecordItem> list,
                                                ReviewRecordItem value,
                                                int index,
                                                boolean isSelected,
                                                boolean cellHasFocus) {
        
        JPanel panel = new JPanel();
        panel.setBorder(JBUI.Borders.empty(4, 12, 4, 12));
        panel.setOpaque(true);
        
        if (value == null) {
            return panel;
        }
        
        // 设置背景色
        if (isSelected) {
            panel.setBackground(JBUI.CurrentTheme.List.Selection.background(true));
        } else {
            panel.setBackground(JBUI.CurrentTheme.List.BACKGROUND);
        }
        
        // 使用GridBagLayout实现精确布局
        panel.setLayout(new GridBagLayout());
        GridBagConstraints mainGbc = new GridBagConstraints();
        
        // 左侧内容面板（状态图标 + Session ID + 时间）
        JPanel contentPanel = createContentPanel(value, isSelected);
        mainGbc.gridx = 0;
        mainGbc.gridy = 0;
        mainGbc.weightx = 1.0;
        mainGbc.fill = GridBagConstraints.HORIZONTAL;
        mainGbc.anchor = GridBagConstraints.WEST;
        panel.add(contentPanel, mainGbc);
        
        // 右侧复制按钮（固定位置）
        JLabel copyButton = createCopyButton();
        mainGbc.gridx = 1;
        mainGbc.weightx = 0.0;
        mainGbc.fill = GridBagConstraints.NONE;
        mainGbc.anchor = GridBagConstraints.EAST;
        mainGbc.insets = JBUI.insets(0, 8, 0, 0);
        panel.add(copyButton, mainGbc);
        
        // 设置工具提示
        panel.setToolTipText(buildTooltipText(value));
        
        return panel;
    }
    
    /**
     * 创建左侧内容面板（使用GridBagLayout实现四列布局）
     */
    @NotNull
    private JPanel createContentPanel(@NotNull ReviewRecordItem item, boolean selected) {
        JPanel contentPanel = new JPanel(new GridBagLayout());
        contentPanel.setOpaque(false);
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = JBUI.insets(0, 0, 0, 8); // 组件间间距
        gbc.anchor = GridBagConstraints.WEST;
        gbc.fill = GridBagConstraints.NONE;
        
        // 第1列：状态文字
        String statusText = getStatusText(item.getStatusIconType());
        JLabel statusLabel = new JLabel(statusText);
        statusLabel.setFont(JBUI.Fonts.label(11).asBold());
        statusLabel.setForeground(getStatusColor(item.getStatusIconType(), selected));
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.weightx = 0.0;
        contentPanel.add(statusLabel, gbc);
        
        // 第2列：Session ID（左对齐，占据剩余空间）
        JLabel sessionLabel = new JLabel();
        sessionLabel.setFont(JBUI.Fonts.label(12));
        String sessionText = "Session: <b>" + getDisplaySessionId(item) + "</b>";
        sessionLabel.setText("<html>" + sessionText + "</html>");
        
        if (selected) {
            sessionLabel.setForeground(JBUI.CurrentTheme.List.Selection.foreground(true));
        } else {
            sessionLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
        }
        
        gbc.gridx = 1;
        gbc.weightx = 1.0; // 占据剩余空间
        gbc.fill = GridBagConstraints.HORIZONTAL;
        contentPanel.add(sessionLabel, gbc);
        
        // 第3列：时间（右对齐）
        JLabel timeLabel = new JLabel(item.getRelativeTimeDisplay());
        timeLabel.setFont(JBUI.Fonts.label(12));
        
        if (selected) {
            timeLabel.setForeground(JBUI.CurrentTheme.List.Selection.foreground(false));
        } else {
            timeLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        }
        
        gbc.gridx = 2;
        gbc.weightx = 0.0;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.EAST;
        gbc.insets = JBUI.insets(0, 8, 0, 0); // 左侧间距
        contentPanel.add(timeLabel, gbc);
        
        return contentPanel;
    }
    
    /**
     * 创建复制按钮
     */
    @NotNull
    private JLabel createCopyButton() {
        JLabel copyButton = new JLabel(AllIcons.Actions.Copy);
        copyButton.setToolTipText("复制 Session ID");
        copyButton.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
        copyButton.setBorder(JBUI.Borders.empty(4));
        return copyButton;
    }
    
    /**
     * 获取状态对应的文字
     */
    @NotNull
    private String getStatusText(@NotNull String statusType) {
        return switch (statusType) {
            case "success" -> "已完成";
            case "progress" -> "进行中";
            case "error" -> "失败";
            case "pending" -> "待处理";
            default -> "未知";
        };
    }

    /**
     * 获取状态对应的颜色
     */
    @NotNull
    private Color getStatusColor(@NotNull String statusType, boolean selected) {
        if (selected) {
            return JBUI.CurrentTheme.List.Selection.foreground(true);
        }
        
        return switch (statusType) {
            case "success" -> new Color(0x388E3C); // 绿色 - 已完成
            case "progress" -> new Color(0x1976D2); // 蓝色 - 进行中
            case "error" -> new Color(0xD32F2F); // 红色 - 失败
            case "pending" -> new Color(0xFF9800); // 橙色 - 待处理
            default -> new Color(0x757575); // 灰色 - 未知
        };
    }
    
    /**
     * 获取显示Session ID（截取前10位）
     */
    @NotNull
    private String getDisplaySessionId(@NotNull ReviewRecordItem item) {
        String sessionId = item.getSessionId();
        if (sessionId != null && sessionId.length() > 10) {
            return sessionId.substring(0, 10) + "...";
        }
        return sessionId != null ? sessionId : "N/A";
    }
    
    
    /**
     * 构建工具提示文本
     */
    @NotNull
    private String buildTooltipText(@NotNull ReviewRecordItem item) {
        return String.format(
            "<html>" +
            "<b>Session ID:</b> %s<br>" +
            "<b>状态:</b> %s<br>" +
            "<b>触发者:</b> %s<br>" +
            "<b>仓库:</b> %s<br>" +
            "<b>创建时间:</b> %s<br>" +
            "<br>" +
            "<i>点击右侧复制按钮复制Session ID</i>" +
            "</html>",
            item.getSessionId(),
            item.getStatusDisplayText(),
            item.getTriggerDisplayText(),
            item.getRepoPath(),
            item.getFormattedCreatedAt()
        );
    }
}