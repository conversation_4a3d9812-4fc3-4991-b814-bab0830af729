package com.sentinel.nocalhost.intellicode.ui.model;

/**
 * 建议项数据模型
 * 包含原始建议数据、状态和UI状态
 */
public class SuggestionItem {
    private final intellicode.event.v1.EventOuterClass.Suggestion suggestion;
    private SuggestionState state;
    private boolean expanded;
    
    public SuggestionItem(intellicode.event.v1.EventOuterClass.Suggestion suggestion) {
        this.suggestion = suggestion;
        this.state = SuggestionState.PENDING;  // 默认为待处理状态
        this.expanded = false;  // 默认折叠
    }
    
    // Getters
    public intellicode.event.v1.EventOuterClass.Suggestion getSuggestion() {
        return suggestion;
    }
    
    public SuggestionState getState() {
        return state;
    }
    
    public boolean isExpanded() {
        return expanded;
    }
    
    // Setters
    public void setState(SuggestionState state) {
        this.state = state;
    }
    
    public void setExpanded(boolean expanded) {
        this.expanded = expanded;
    }
    
    // 便捷方法
    public void toggleExpanded() {
        this.expanded = !this.expanded;
    }
    
    public boolean isPending() {
        return state == SuggestionState.PENDING;
    }
    
    public boolean isApplied() {
        return state == SuggestionState.APPLIED;
    }
    
    public boolean isRejected() {
        return state == SuggestionState.REJECTED;
    }
    
    public boolean isProcessed() {
        return state.isProcessed();
    }
    
    /**
     * 获取建议的简短摘要
     * 用于折叠视图显示
     */
    public String getSummary() {
        String description = suggestion.getDescription();
        // 限制摘要长度，保持单行显示
        if (description.length() > 60) {
            return description.substring(0, 57) + "...";
        }
        return description;
    }
    
    /**
     * 获取文件信息字符串
     */
    public String getFileInfo() {
        String fileInfo = suggestion.getFilePath() + ":" + suggestion.getStartLine();
        if (suggestion.getEndLine() != suggestion.getStartLine()) {
            fileInfo += "-" + suggestion.getEndLine();
        }
        return fileInfo;
    }
    
    /**
     * 获取级别显示字符串
     */
    public String getLevelDisplay() {
        return suggestion.getLevel().name().substring("LEVEL_".length());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SuggestionItem that = (SuggestionItem) obj;
        return suggestion.getId() == that.suggestion.getId();
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(suggestion.getId());
    }
}