package com.sentinel.nocalhost.intellicode.ui.model;

import com.sentinel.nocalhost.intellicode.api.IntellicodeAPI.ReviewRecordInfo;
import intellicode.api.v1.Api.ReviewStatus;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 审查记录项目数据模型
 * 用于历史记录列表显示
 */
public class ReviewRecordItem implements Comparable<ReviewRecordItem> {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final ReviewRecordInfo recordInfo;
    
    public ReviewRecordItem(@NotNull ReviewRecordInfo recordInfo) {
        this.recordInfo = recordInfo;
    }
    
    public int getReviewId() {
        return recordInfo.reviewId;
    }
    
    @NotNull
    public String getSessionId() {
        return recordInfo.sessionId != null ? recordInfo.sessionId : "";
    }
    
    @NotNull
    public String getRepoPath() {
        return recordInfo.repoPath != null ? recordInfo.repoPath : "";
    }
    
    @NotNull
    public ReviewStatus getStatus() {
        return recordInfo.status != null ? recordInfo.status : ReviewStatus.REVIEW_STATUS_UNSPECIFIED;
    }
    
    @NotNull
    public String getTriggerBy() {
        return recordInfo.triggerBy != null ? recordInfo.triggerBy : "";
    }
    
    @NotNull
    public String getEmail() {
        return recordInfo.email != null ? recordInfo.email : "";
    }
    
    public long getCreatedAt() {
        return recordInfo.createdAt;
    }
    
    /**
     * 获取状态的中文显示文本
     */
    @NotNull
    public String getStatusDisplayText() {
        return switch (getStatus()) {
            case REVIEW_STATUS_DONE -> "已完成";
            case REVIEW_STATUS_CHECKING -> "检查中";
            case REVIEW_STATUS_FAILED -> "失败";
            case REVIEW_STATUS_UNDONE -> "未处理";
            default -> "未知";
        };
    }
    
    /**
     * 获取状态图标的描述（用于渲染器选择图标）
     */
    @NotNull
    public String getStatusIconType() {
        return switch (getStatus()) {
            case REVIEW_STATUS_DONE -> "success";
            case REVIEW_STATUS_CHECKING -> "progress";
            case REVIEW_STATUS_FAILED -> "error";
            case REVIEW_STATUS_UNDONE -> "pending";
            default -> "unknown";
        };
    }
    
    /**
     * 格式化创建时间
     */
    @NotNull
    public String getFormattedCreatedAt() {
        try {
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochSecond(recordInfo.createdAt), 
                ZoneId.systemDefault()
            );
            return dateTime.format(DATE_FORMATTER);
        } catch (Exception e) {
            return "未知时间";
        }
    }
    
    /**
     * 获取相对时间显示（如"5分钟前"）
     */
    @NotNull
    public String getRelativeTimeDisplay() {
        try {
            long now = System.currentTimeMillis() / 1000;
            long diff = now - recordInfo.createdAt;
            
            if (diff < 60) {
                return "刚刚";
            } else if (diff < 3600) {
                return (diff / 60) + "分钟前";
            } else if (diff < 86400) {
                return (diff / 3600) + "小时前";
            } else if (diff < 2592000) {
                return (diff / 86400) + "天前";
            } else {
                return getFormattedCreatedAt();
            }
        } catch (Exception e) {
            return getFormattedCreatedAt();
        }
    }
    
    /**
     * 获取简短的Session ID显示（前8位）
     */
    @NotNull
    public String getShortSessionId() {
        String sessionId = getSessionId();
        if (sessionId.length() > 8) {
            return sessionId.substring(0, 8) + "...";
        }
        return sessionId;
    }
    
    /**
     * 获取触发者显示文本（优先显示email，如果为空则显示triggerBy）
     */
    @NotNull
    public String getTriggerDisplayText() {
        String email = getEmail();
        if (!email.isEmpty()) {
            return email;
        }
        String triggerBy = getTriggerBy();
        return !triggerBy.isEmpty() ? triggerBy : "未知用户";
    }
    
    /**
     * 获取仓库名称（从完整路径提取）
     */
    @NotNull
    public String getRepositoryName() {
        String repoPath = getRepoPath();
        if (repoPath.isEmpty()) {
            return "未知仓库";
        }
        
        // 提取路径的最后一部分作为仓库名
        String[] parts = repoPath.split("[/\\\\]");
        if (parts.length > 0) {
            String lastPart = parts[parts.length - 1];
            return !lastPart.isEmpty() ? lastPart : "未知仓库";
        }
        
        return repoPath;
    }
    
    /**
     * 根据创建时间倒序排列（最新的在前）
     */
    @Override
    public int compareTo(@NotNull ReviewRecordItem other) {
        return Long.compare(other.recordInfo.createdAt, this.recordInfo.createdAt);
    }
    
    /**
     * 获取原始的 ReviewRecordInfo 对象
     */
    @NotNull
    public ReviewRecordInfo getRecordInfo() {
        return recordInfo;
    }
    
    @Override
    public boolean equals(@Nullable Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ReviewRecordItem that = (ReviewRecordItem) obj;
        return recordInfo.reviewId == that.recordInfo.reviewId;
    }
    
    @Override
    public int hashCode() {
        return Integer.hashCode(recordInfo.reviewId);
    }
    
    @Override
    public String toString() {
        return String.format("ReviewRecordItem{reviewId=%d, sessionId='%s', status=%s, createdAt=%d}", 
            recordInfo.reviewId, getSessionId(), getStatus(), recordInfo.createdAt);
    }
}