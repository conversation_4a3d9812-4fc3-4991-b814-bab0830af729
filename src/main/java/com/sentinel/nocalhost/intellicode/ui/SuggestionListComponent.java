package com.sentinel.nocalhost.intellicode.ui;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.util.ui.JBUI;
import org.jetbrains.annotations.NotNull;
import com.sentinel.nocalhost.intellicode.api.IntellicodeAPI;
import com.sentinel.nocalhost.intellicode.ui.model.SuggestionState;
import com.sentinel.nocalhost.utils.CodeUtils;
import com.intellij.diff.DiffManager;
import com.intellij.diff.DiffContentFactory;
import com.intellij.diff.contents.DiffContent;
import com.intellij.diff.requests.SimpleDiffRequest;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileDocumentManager;
import com.intellij.openapi.fileEditor.FileEditor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.fileEditor.TextEditor;
import com.intellij.openapi.ui.popup.Balloon;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.ui.awt.RelativePoint;
import com.intellij.diff.editor.DiffVirtualFile;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.Comparator;

/**
 * 建议列表组件
 * 负责显示代码审查建议，支持折叠/展开、状态排序、滚动显示
 */
public class SuggestionListComponent implements Disposable {

    /**
     * 展开状态监听器接口
     */
    public interface ExpansionListener {
        /**
         * 当有条目展开时调用
         *
         * @param expanded 是否有条目展开
         */
        void onExpansionChanged(boolean expanded);
    }

    /**
     * Diff窗口管理器 - 用于跟踪和复用已打开的diff窗口
     */
    private static class DiffWindowManager {
        // 存储 suggestionId -> DiffVirtualFile 的映射
        private static final Map<Integer, DiffVirtualFile> openDiffWindows = new ConcurrentHashMap<>();

        /**
         * 检查指定建议的diff窗口是否已打开
         */
        public static boolean isDiffWindowOpen(Project project, int suggestionId) {
            // 先清理已关闭的窗口
            cleanup(project);

            DiffVirtualFile diffFile = openDiffWindows.get(suggestionId);
            if (diffFile == null) {
                return false;
            }

            FileEditorManager manager = FileEditorManager.getInstance(project);
            boolean isOpen = manager.isFileOpen(diffFile);

            if (!isOpen) {
                // 如果文件已关闭，从映射中移除
                openDiffWindows.remove(suggestionId);
            }

            return isOpen;
        }

        /**
         * 激活指定建议的diff窗口
         */
        public static void activateDiffWindow(Project project, int suggestionId) {
            DiffVirtualFile diffFile = openDiffWindows.get(suggestionId);
            if (diffFile != null) {
                FileEditorManager manager = FileEditorManager.getInstance(project);
                manager.openFile(diffFile, true);
            }
        }

        /**
         * 记录新创建的diff窗口
         */
        public static void recordDiffWindow(Project project, int suggestionId) {
            // 使用延迟执行，确保diff窗口已经完全创建
            ApplicationManager.getApplication().invokeLater(() -> {
                FileEditorManager manager = FileEditorManager.getInstance(project);
                VirtualFile[] openFiles = manager.getOpenFiles();

                // 查找最新的diff文件（通常是最后一个）
                DiffVirtualFile latestDiffFile = null;
                for (int i = openFiles.length - 1; i >= 0; i--) {
                    if (openFiles[i] instanceof DiffVirtualFile) {
                        latestDiffFile = (DiffVirtualFile) openFiles[i];
                        break;
                    }
                }

                if (latestDiffFile != null) {
                    openDiffWindows.put(suggestionId, latestDiffFile);
                }
            });
        }

        /**
         * 清理已关闭的diff窗口
         */
        public static void cleanup(Project project) {
            FileEditorManager manager = FileEditorManager.getInstance(project);
            openDiffWindows.entrySet().removeIf(entry -> {
                DiffVirtualFile diffFile = entry.getValue();
                return !manager.isFileOpen(diffFile);
            });
        }

        /**
         * 移除特定建议的diff窗口记录
         */
        public static void removeDiffWindow(int suggestionId) {
            openDiffWindows.remove(suggestionId);
        }

        /**
         * 获取当前打开的diff窗口数量
         */
        public static int getOpenWindowCount() {
            return openDiffWindows.size();
        }
    }

    private static final Logger LOG = Logger.getInstance(SuggestionListComponent.class);

    // 常量定义
    private static final String TITLE_SUGGESTIONS = "建议列表";
    private static final int ITEM_HEIGHT = JBUI.scale(40); // 紧凑布局的合理高度
    // 移除固定的展开高度，改为动态计算
    private final Project project;
    private final int reviewId;
    private final boolean isRealTimeMode;

    // UI 组件
    private JPanel mainPanel;
    private JPanel headerPanel;
    private JPanel listContainer;
    private JBScrollPane scrollPane;
    private JBLabel titleLabel;
    private JBLabel countLabel;
    private JBLabel emptyLabel;
    private JButton heightAdjustButton;

    // 数据管理
    private List<IntellicodeAPI.ReviewSuggestionInfo> suggestions;
    private List<SuggestionItemPanel> suggestionPanels;
    private SuggestionItemPanel expandedPanel;

    // 展开状态监听器
    private ExpansionListener expansionListener;

    public SuggestionListComponent(@NotNull Project project, int reviewId, boolean isRealTimeMode) {
        this.project = project;
        this.reviewId = reviewId;
        this.isRealTimeMode = isRealTimeMode;
        this.suggestions = new ArrayList<>();
        this.suggestionPanels = new ArrayList<>();

        initializeUI();
    }

    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 创建主面板
        mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(JBUI.Borders.empty(8, 12));
        mainPanel.setBackground(JBUI.CurrentTheme.ToolWindow.background());

        // 创建头部面板
        createHeaderPanel();

        // 创建列表容器
        createListContainer();

        // 创建滚动面板
        createScrollPane();

        // 布局
        mainPanel.add(headerPanel, BorderLayout.NORTH);
        mainPanel.add(scrollPane, BorderLayout.CENTER);

        LOG.debug(
                "SuggestionListComponent initialized for reviewId: " + reviewId + ", realTimeMode: " + isRealTimeMode);
    }

    /**
     * 创建头部面板
     */
    private void createHeaderPanel() {
        headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBorder(JBUI.Borders.emptyBottom(8));
        headerPanel.setOpaque(false);

        // 左侧面板：高度调整图标 + 标题
        JPanel leftPanel = new JPanel();
        leftPanel.setLayout(new BoxLayout(leftPanel, BoxLayout.X_AXIS));
        leftPanel.setOpaque(false);

        // 高度调整图标
        heightAdjustButton = new JButton(AllIcons.Actions.MoveTo2);
        heightAdjustButton.setBorderPainted(false);
        heightAdjustButton.setContentAreaFilled(false);
        heightAdjustButton.setFocusPainted(false);
        heightAdjustButton.setPreferredSize(new Dimension(JBUI.scale(20), JBUI.scale(20)));
        heightAdjustButton.setMinimumSize(new Dimension(JBUI.scale(20), JBUI.scale(20)));
        heightAdjustButton.setMaximumSize(new Dimension(JBUI.scale(20), JBUI.scale(20)));
        heightAdjustButton.setOpaque(false);
        heightAdjustButton.setToolTipText("调整建议列表区域高度");
        heightAdjustButton.setCursor(Cursor.getPredefinedCursor(Cursor.N_RESIZE_CURSOR));

        // 为高度调整按钮添加鼠标事件
        heightAdjustButton.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseEntered(MouseEvent e) {
                heightAdjustButton.setBackground(JBUI.CurrentTheme.ActionButton.hoverBackground());
                heightAdjustButton.setContentAreaFilled(true);
            }

            @Override
            public void mouseExited(MouseEvent e) {
                heightAdjustButton.setContentAreaFilled(false);
            }
        });

        // 为高度调整按钮添加点击事件
        heightAdjustButton.addActionListener(e -> {
            // TODO: 实现高度调整功能
            // 这里可以实现拖拽调整建议列表区域高度的功能
            LOG.debug("Height adjust button clicked");
        });

        // 标题
        titleLabel = new JBLabel(TITLE_SUGGESTIONS);
        titleLabel.setFont(JBUI.Fonts.label(12).asBold());
        titleLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
        titleLabel.setBorder(JBUI.Borders.emptyLeft(8));

        leftPanel.add(heightAdjustButton);
        leftPanel.add(titleLabel);

        // 计数标签
        countLabel = new JBLabel("0 条建议");
        countLabel.setFont(JBUI.Fonts.label(11));
        countLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());

        headerPanel.add(leftPanel, BorderLayout.WEST);
        headerPanel.add(countLabel, BorderLayout.EAST);
    }

    /**
     * 创建列表容器
     */
    private void createListContainer() {
        listContainer = new JPanel();
        listContainer.setLayout(new BoxLayout(listContainer, BoxLayout.Y_AXIS));
        listContainer.setBackground(JBUI.CurrentTheme.List.BACKGROUND);

        // 创建空状态标签
        emptyLabel = new JBLabel("暂无建议");
        emptyLabel.setFont(JBUI.Fonts.label(12));
        emptyLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        emptyLabel.setHorizontalAlignment(SwingConstants.CENTER);
        emptyLabel.setBorder(JBUI.Borders.empty(20));
    }

    /**
     * 创建滚动面板
     */
    private void createScrollPane() {
        scrollPane = new JBScrollPane(listContainer);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setBorder(JBUI.Borders.customLine(
                JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground(), 1, 0, 0, 0));
        scrollPane.setBackground(JBUI.CurrentTheme.List.BACKGROUND);
        scrollPane.getViewport().setBackground(JBUI.CurrentTheme.List.BACKGROUND);
        scrollPane.getVerticalScrollBar().setUnitIncrement(16);

        // 去掉固定高度限制，允许动态调整
    }

    /**
     * 更新建议列表
     */
    public void updateSuggestions(@NotNull List<IntellicodeAPI.ReviewSuggestionInfo> newSuggestions) {
        SwingUtilities.invokeLater(() -> {
            try {
                // 更新数据
                suggestions.clear();
                suggestions.addAll(newSuggestions);

                // 按状态排序：Pending > Applied > Rejected
                suggestions.sort(new SuggestionComparator());

                // 更新UI
                refreshUI();

                LOG.info("Updated suggestion list with " + suggestions.size() + " items");
            } catch (Exception e) {
                LOG.error("Failed to update suggestions", e);
                showError("更新建议列表失败: " + e.getMessage());
            }
        });
    }

    /**
     * 刷新UI显示
     */
    private void refreshUI() {
        // 清理现有面板
        clearSuggestionPanels();

        if (suggestions.isEmpty()) {
            showEmptyState();
        } else {
            showSuggestionList();
        }

        // 更新计数
        updateCountLabel();

        // 刷新布局
        listContainer.revalidate();
        listContainer.repaint();
    }

    /**
     * 显示空状态
     */
    private void showEmptyState() {
        listContainer.removeAll();
        listContainer.add(emptyLabel);
    }

    /**
     * 显示建议列表
     */
    private void showSuggestionList() {
        listContainer.removeAll();

        for (int i = 0; i < suggestions.size(); i++) {
            IntellicodeAPI.ReviewSuggestionInfo suggestion = suggestions.get(i);
            SuggestionItemPanel itemPanel = new SuggestionItemPanel(suggestion, isRealTimeMode);
            suggestionPanels.add(itemPanel);
            listContainer.add(itemPanel);

            // 添加紧凑的间距（最后一个除外）
            if (i < suggestions.size() - 1) {
                // 添加分隔线，去掉上下间距
                JSeparator separator = new JSeparator(SwingConstants.HORIZONTAL);
                separator.setForeground(JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground());
                separator.setMaximumSize(new Dimension(Integer.MAX_VALUE, JBUI.scale(1)));
                separator.setPreferredSize(new Dimension(0, JBUI.scale(1)));
                listContainer.add(separator);
            }
        }

        // 在列表末尾添加一个可伸缩的空白区域，确保列表项不会被拉伸
        listContainer.add(Box.createVerticalGlue());
    }

    /**
     * 清理建议面板
     */
    private void clearSuggestionPanels() {
        suggestionPanels.clear();
        expandedPanel = null;
    }

    /**
     * 更新计数标签
     */
    private void updateCountLabel() {
        int count = suggestions.size();
        countLabel.setText(count + " 条建议");
    }

    /**
     * 显示错误信息
     */
    public void showError(@NotNull String message) {
        SwingUtilities.invokeLater(() -> {
            listContainer.removeAll();

            JBLabel errorLabel = new JBLabel(message);
            errorLabel.setFont(JBUI.Fonts.label(12));
            errorLabel.setForeground(JBColor.RED);
            errorLabel.setHorizontalAlignment(SwingConstants.CENTER);
            errorLabel.setBorder(JBUI.Borders.empty(20));

            listContainer.add(errorLabel);
            listContainer.revalidate();
            listContainer.repaint();
        });
    }

    /**
     * 获取编辑器字体大小
     */
    private int getEditorFontSize() {
        try {
            com.intellij.openapi.editor.colors.EditorColorsManager colorsManager = com.intellij.openapi.editor.colors.EditorColorsManager
                    .getInstance();
            com.intellij.openapi.editor.colors.EditorColorsScheme scheme = colorsManager.getGlobalScheme();
            int fontSize = scheme.getEditorFontSize();
            return fontSize > 0 ? fontSize : 12; // 默认字体大小为12
        } catch (Exception e) {
            LOG.warn("Failed to get editor font size, using default", e);
            return 12; // 默认字体大小
        }
    }

    /**
     * 获取组件
     */
    @NotNull
    public JComponent getComponent() {
        return mainPanel;
    }

    /**
     * 设置展开状态监听器
     */
    public void setExpansionListener(ExpansionListener listener) {
        this.expansionListener = listener;
    }

    /**
     * 建议项面板
     */
    private class SuggestionItemPanel extends JPanel {

        private final IntellicodeAPI.ReviewSuggestionInfo suggestion;
        private final boolean isRealTimeMode;
        private boolean isExpanded = false;

        private JPanel headerPanel;
        private JPanel contentPanel;
        private JBLabel statusIcon;
        private JBLabel levelLabel;
        private JBLabel categoryLabel;
        private JButton expandButton;

        public SuggestionItemPanel(@NotNull IntellicodeAPI.ReviewSuggestionInfo suggestion, boolean isRealTimeMode) {
            this.suggestion = suggestion;
            this.isRealTimeMode = isRealTimeMode;

            setLayout(new BorderLayout());
            setBackground(JBUI.CurrentTheme.List.BACKGROUND);
            setBorder(JBUI.Borders.empty(1, 8, 1, 8)); // 进一步减少上下边距，让布局更紧凑

            createHeaderPanel();
            // 延迟创建contentPanel，只有在第一次展开时才创建，避免不必要的渲染

            add(headerPanel, BorderLayout.NORTH);

            // 添加鼠标事件监听器（只在非按钮区域生效）
            addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent e) {
                    LOG.debug("Mouse clicked on suggestion item: " + suggestion.id);
                    
                    // 检查是否点击在按钮区域
                    boolean clickOnButton = isClickOnExpandButton(e);
                    LOG.debug("Click on expand button: " + clickOnButton);
                    
                    if (!clickOnButton) {
                        LOG.debug("Opening diff window for suggestion: " + suggestion.id);
                        // 单击同时打开diff窗口和展开/折叠列表项
                        openDiffWindow();
                        toggleExpanded();
                    }
                }

                @Override
                public void mouseEntered(MouseEvent e) {
                    setBackground(JBUI.CurrentTheme.List.Hover.background(true));
                    setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
                }

                @Override
                public void mouseExited(MouseEvent e) {
                    setBackground(JBUI.CurrentTheme.List.BACKGROUND);
                    setCursor(Cursor.getDefaultCursor());
                }
            });

            // 设置固定的尺寸，防止在容器高度变化时被拉伸
            setPreferredSize(new Dimension(0, ITEM_HEIGHT));
            setMaximumSize(new Dimension(Integer.MAX_VALUE, ITEM_HEIGHT));
            setMinimumSize(new Dimension(0, ITEM_HEIGHT));
        }

        private void createHeaderPanel() {
            headerPanel = new JPanel(new BorderLayout());
            headerPanel.setOpaque(false);

            // 最左侧：展开按钮
            expandButton = new JButton(AllIcons.General.ArrowRight);
            expandButton.setBorderPainted(false);
            expandButton.setContentAreaFilled(false);
            expandButton.setFocusPainted(false);
            expandButton.setPreferredSize(new Dimension(JBUI.scale(20), JBUI.scale(20)));
            expandButton.setMinimumSize(new Dimension(JBUI.scale(20), JBUI.scale(20)));
            expandButton.setMaximumSize(new Dimension(JBUI.scale(20), JBUI.scale(20)));
            expandButton.setOpaque(false);
            // 为按钮添加点击事件
            expandButton.addActionListener(e -> {
                toggleExpanded();
            });

            // 中间：状态图标和建议摘要
            JPanel centerPanel = new JPanel();
            centerPanel.setLayout(new BoxLayout(centerPanel, BoxLayout.X_AXIS));
            centerPanel.setOpaque(false);
            centerPanel.setBorder(JBUI.Borders.emptyLeft(8));

            statusIcon = new JBLabel(getStatusText());
            statusIcon.setFont(JBUI.Fonts.label(11).asBold());
            statusIcon.setForeground(getStatusColor());
            statusIcon.setBorder(JBUI.Borders.emptyRight(8));

            // 建议摘要标签（单行显示）
            String summaryText = getSummaryText();
            categoryLabel = new JBLabel(summaryText);
            categoryLabel.setFont(JBUI.Fonts.label(12));
            categoryLabel.setForeground(JBUI.CurrentTheme.Label.foreground());

            centerPanel.add(statusIcon);
            centerPanel.add(categoryLabel);
            centerPanel.add(Box.createHorizontalGlue()); // 推动右侧内容到最右边

            headerPanel.add(expandButton, BorderLayout.WEST);
            headerPanel.add(centerPanel, BorderLayout.CENTER);
        }

        private void createContentPanel() {
            contentPanel = new JPanel(new BorderLayout());
            contentPanel.setOpaque(false);
            contentPanel.setBorder(JBUI.Borders.empty(8, 4, 8, 4)); // 调整内边距

            // 顶部文件信息面板
            JPanel fileInfoPanel = createFileInfoPanel();

            // 中间描述区域 - 优化性能，避免卡顿
            JEditorPane descriptionPane = createOptimizedDescriptionPane();

            // 描述区域的滚动面板 - 增加高度和改善样式
            JBScrollPane scrollPane = new JBScrollPane(descriptionPane);
            scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
            scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED); // 允许横向滚动以显示长代码行
            scrollPane.setBorder(JBUI.Borders.customLine(
                    JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground(), 1));
            scrollPane.setOpaque(false);
            scrollPane.getViewport().setOpaque(false);
            scrollPane.setPreferredSize(new Dimension(0, JBUI.scale(180))); // 增加高度
            scrollPane.setMaximumSize(new Dimension(Integer.MAX_VALUE, JBUI.scale(220))); // 增加最大高度

            // 底部按钮面板
            JPanel buttonPanel = createButtonPanel();

            // 三段式布局
            contentPanel.add(fileInfoPanel, BorderLayout.NORTH);
            contentPanel.add(scrollPane, BorderLayout.CENTER);
            contentPanel.add(buttonPanel, BorderLayout.SOUTH);
        }

        /**
         * 创建优化的描述面板，避免UI卡顿
         */
        private JEditorPane createOptimizedDescriptionPane() {
            JEditorPane descriptionPane = new JEditorPane();
            descriptionPane.setBorder(JBUI.Borders.empty(8));
            descriptionPane.setEditable(false);
            descriptionPane.setFont(getDefaultFont());

            String content = suggestion.suggestionDescription != null ?
                suggestion.suggestionDescription : "暂无详细信息";

            // 简化处理：如果内容较短，直接显示纯文本；如果较长，才进行markdown渲染
            if (content.length() < 500 && !content.contains("```") && !content.contains("#")) {
                // 短文本且无markdown标记，直接显示纯文本
                descriptionPane.setContentType("text/plain");
                descriptionPane.setText(content);
            } else {
                // 长文本或包含markdown，先显示纯文本，然后异步渲染
                descriptionPane.setContentType("text/plain");
                descriptionPane.setText(content);

                // 延迟渲染，避免与展开动画冲突
                Timer renderTimer = new Timer(100, e -> {
                    try {
                        CodeUtils.setStyledText(descriptionPane, content);
                    } catch (Exception ex) {
                        LOG.warn("Failed to render markdown content, keeping plain text", ex);
                    }
                });
                renderTimer.setRepeats(false);
                renderTimer.start();
            }

            return descriptionPane;
        }

        /**
         * 获取IDE默认字体
         */
        private Font getDefaultFont() {
            // 直接使用IDE的默认标签字体，不需要缓存
            return JBUI.Fonts.label();
        }

        /**
         * 创建文件信息面板
         */
        private JPanel createFileInfoPanel() {
            JPanel fileInfoPanel = new JPanel();
            fileInfoPanel.setLayout(new BoxLayout(fileInfoPanel, BoxLayout.Y_AXIS));
            fileInfoPanel.setOpaque(false);
            fileInfoPanel.setBorder(JBUI.Borders.empty(0, 0, 8, 0)); // 增加底部间距

            // 第一行：级别标签和类别
            JPanel firstRowPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 4, 1)); // 减少间距
            firstRowPanel.setOpaque(false);

            // 级别标签（从header移动到这里）
            levelLabel = new JBLabel(suggestion.level);
            levelLabel.setFont(JBUI.Fonts.label(9).asBold()); // 稍微减小字体
            levelLabel.setOpaque(true);
            levelLabel.setBackground(getLevelColor());
            levelLabel.setForeground(JBColor.WHITE);
            levelLabel.setBorder(JBUI.Borders.empty(2, 6, 2, 6)); // 减少内边距

            // 类别标签 - 统一为标签样式
            JBLabel categoryDisplayLabel = new JBLabel(suggestion.suggestionCategory);
            categoryDisplayLabel.setFont(JBUI.Fonts.label(9)); // 稍微减小字体
            categoryDisplayLabel.setOpaque(true);
            categoryDisplayLabel.setBackground(JBUI.CurrentTheme.List.Selection.background(false));
            categoryDisplayLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
            categoryDisplayLabel.setBorder(JBUI.Borders.empty(2, 6, 2, 6)); // 减少内边距

            firstRowPanel.add(levelLabel);
            firstRowPanel.add(categoryDisplayLabel);

            // 第二行：文件路径和行号
            JPanel secondRowPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 1)); // 减少垂直间距
            secondRowPanel.setOpaque(false);

            // 文件信息标签
            JBLabel fileInfoLabel = new JBLabel(formatFileInfo());
            fileInfoLabel.setFont(JBUI.Fonts.label(10)); // 稍微减小字体
            fileInfoLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());

            secondRowPanel.add(fileInfoLabel);

            fileInfoPanel.add(firstRowPanel);
            fileInfoPanel.add(secondRowPanel);

            return fileInfoPanel;
        }

        /**
         * 创建按钮面板
         */
        private JPanel createButtonPanel() {
            JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 6));
            buttonPanel.setOpaque(false);
            buttonPanel.setBorder(JBUI.Borders.empty(8, 0, 4, 0)); // 增加顶部间距

            // Apply 按钮 - 主要操作，使用默认按钮样式
            JButton applyButton = new JButton("Apply");
            applyButton.setFont(JBUI.Fonts.label(11));
            applyButton.setPreferredSize(new Dimension(JBUI.scale(68), JBUI.scale(26)));
            applyButton.setMaximumSize(new Dimension(JBUI.scale(68), JBUI.scale(26)));
            applyButton.putClientProperty("JButton.buttonType", "default"); // 主要按钮样式

            // Reject 按钮 - 次要操作，使用更低调的样式
            JButton rejectButton = new JButton("Reject");
            rejectButton.setFont(JBUI.Fonts.label(11));
            rejectButton.setPreferredSize(new Dimension(JBUI.scale(68), JBUI.scale(26)));
            rejectButton.setMaximumSize(new Dimension(JBUI.scale(68), JBUI.scale(26)));
            rejectButton.putClientProperty("JButton.buttonType", "borderless"); // 次要按钮样式
            rejectButton.setForeground(JBUI.CurrentTheme.Label.disabledForeground());

            // 按钮在历史模式和实时模式下都可以执行操作
            boolean canPerformAction = isPending(); // 只要是待处理状态就可以操作
            applyButton.setEnabled(canPerformAction);
            rejectButton.setEnabled(canPerformAction);

            // 添加按钮点击事件处理
            applyButton.addActionListener(e -> handleApplyAction());
            rejectButton.addActionListener(e -> handleRejectAction());

            // 直接添加按钮，减少嵌套
            buttonPanel.add(applyButton);
            buttonPanel.add(Box.createHorizontalStrut(JBUI.scale(8)));
            buttonPanel.add(rejectButton);

            return buttonPanel;
        }

        /**
         * 格式化描述文本，支持自动换行和行数限制
         */
        private String formatDescriptionText(String text) {
            return formatDescriptionText(text, -1); // 默认不限制行数
        }

        /**
         * 格式化描述文本，支持自动换行和行数限制
         * 
         * @param text     原始文本
         * @param maxLines 最大行数，-1表示不限制
         */
        private String formatDescriptionText(String text, int maxLines) {
            if (text == null || text.trim().isEmpty()) {
                return "<html><div style='color: gray; font-style: italic; padding: 4px;'>暂无描述</div></html>";
            }

            // 处理特殊字符
            String escapedText = text.replace("&", "&amp;")
                    .replace("<", "&lt;")
                    .replace(">", "&gt;")
                    .replace("\"", "&quot;");

            // 按行分割并限制行数
            String[] lines = escapedText.split("\n");
            StringBuilder html = new StringBuilder(
                    "<html><div style='padding: 4px; line-height: 1.4; word-wrap: break-word;'>");

            boolean truncated = false;
            int displayLines = (maxLines > 0 && lines.length > maxLines) ? maxLines : lines.length;

            for (int i = 0; i < displayLines; i++) {
                if (i > 0) {
                    html.append("<br>");
                }
                html.append(lines[i]);
            }

            // 如果内容被截断，添加省略号提示
            if (maxLines > 0 && lines.length > maxLines) {
                html.append("<br><span style='color: gray; font-style: italic;'>... (还有 ")
                        .append(lines.length - maxLines)
                        .append(" 行内容，请滚动查看)</span>");
            }

            html.append("</div></html>");

            return html.toString();
        }

        /**
         * 处理应用建议操作
         */
        private void handleApplyAction() {
            if (isPending()) {
                String mode = isRealTimeMode ? "实时模式" : "历史模式";
                LOG.info(mode + " - Applying suggestion: " + suggestion.id);
                // TODO: 实现应用逻辑
                // 无论是实时模式还是历史模式，都执行相同的应用逻辑
                executeApplyAction();
            }
        }

        /**
         * 处理拒绝建议操作
         */
        private void handleRejectAction() {
            if (isPending()) {
                String mode = isRealTimeMode ? "实时模式" : "历史模式";
                LOG.info(mode + " - Rejecting suggestion: " + suggestion.id);
                // TODO: 实现拒绝逻辑
                // 无论是实时模式还是历史模式，都执行相同的拒绝逻辑
                executeRejectAction();
            }
        }

        /**
         * 执行应用建议的具体逻辑
         */
        private void executeApplyAction() {
            try {
                // 获取建议的相关信息
                String filePath = suggestion.filePath;
                String originalCode = suggestion.originalCodeSnippet;
                String suggestedCode = suggestion.suggestedCodeSnippet;

                if (filePath == null || originalCode == null || suggestedCode == null) {
                    showApplyError("建议数据不完整，无法应用");
                    return;
                }

                // 查找文件
                VirtualFile file = findProjectFile(filePath);
                if (file == null) {
                    showApplyError("找不到文件: " + filePath);
                    return;
                }

                // 获取文档
                Document document = getDocumentForFile(file);
                if (document == null) {
                    showApplyError("无法获取文档: " + filePath);
                    return;
                }

                // 在文档中搜索原始代码
                String fileContent = document.getText();
                int startOffset = fileContent.indexOf(originalCode);

                if (startOffset == -1) {
                    // 搜索失败，复制建议代码到剪贴板
                    copyToClipboardAndNotify(suggestedCode, "apply失败，已复制建议代码");
                    return;
                }

                // 执行代码替换
                applyCodeReplacement(document, file, startOffset, originalCode, suggestedCode);

            } catch (Exception e) {
                LOG.error("Failed to execute apply action for suggestion: " + suggestion.id, e);
                showApplyError("应用建议时发生错误: " + e.getMessage());
            }
        }

        /**
         * 查找项目中的文件
         */
        private VirtualFile findProjectFile(String filePath) {
            try {
                // 首先尝试相对于项目根目录的路径
                String projectBasePath = project.getBasePath();
                if (projectBasePath != null) {
                    String fullPath = projectBasePath + "/" + filePath;
                    VirtualFile file = LocalFileSystem.getInstance().findFileByPath(fullPath);
                    if (file != null && file.exists()) {
                        return file;
                    }
                }

                // 尝试直接使用文件路径
                VirtualFile file = LocalFileSystem.getInstance().findFileByPath(filePath);
                if (file != null && file.exists()) {
                    return file;
                }

                // 尝试使用 VirtualFileManager
                file = VirtualFileManager.getInstance().findFileByUrl("file://" + filePath);
                if (file != null && file.exists()) {
                    return file;
                }

                return null;
            } catch (Exception e) {
                LOG.warn("Error finding file: " + filePath, e);
                return null;
            }
        }

        /**
         * 获取文件对应的文档
         */
        private Document getDocumentForFile(VirtualFile file) {
            try {
                return FileDocumentManager.getInstance().getDocument(file);
            } catch (Exception e) {
                LOG.warn("Error getting document for file: " + file.getPath(), e);
                return null;
            }
        }

        /**
         * 执行代码替换
         */
        private void applyCodeReplacement(Document document, VirtualFile file, int startOffset,
                                        String originalCode, String suggestedCode) {
            WriteCommandAction.runWriteCommandAction(project, () -> {
                try {
                    int endOffset = startOffset + originalCode.length();

                    // 替换代码
                    document.deleteString(startOffset, endOffset);
                    document.insertString(startOffset, suggestedCode);

                    // 保存文档
                    FileDocumentManager.getInstance().saveDocument(document);

                    // 打开文件并高亮修改区域
                    ApplicationManager.getApplication().invokeLater(() -> {
                        openFileAndHighlight(file, startOffset, startOffset + suggestedCode.length());
                        showApplySuccess("建议已成功应用到文件");
                    });

                    LOG.info("Successfully applied suggestion for file: " + file.getPath());

                } catch (Exception e) {
                    LOG.error("Error applying code replacement", e);
                    showApplyError("应用建议时发生错误: " + e.getMessage());
                }
            });
        }

        /**
         * 打开文件并高亮指定区域
         */
        private void openFileAndHighlight(VirtualFile file, int startOffset, int endOffset) {
            ApplicationManager.getApplication().invokeLater(() -> {
                try {
                    FileEditorManager fileEditorManager = FileEditorManager.getInstance(project);

                    // 打开文件
                    FileEditor[] fileEditors = fileEditorManager.openFile(file, true);

                    if (fileEditors != null && fileEditors.length > 0) {
                        // 尝试获取文本编辑器
                        for (FileEditor fileEditor : fileEditors) {
                            if (fileEditor instanceof TextEditor) {
                                Editor editor = ((TextEditor) fileEditor).getEditor();

                                // 设置绿色高亮表示成功应用
                                JBColor highlightColor = new JBColor(new Color(41, 68, 54), new Color(41, 68, 54));
                                CodeUtils.highlighterCode(editor, startOffset, endOffset, highlightColor);

                                // 滚动到修改位置
                                editor.getCaretModel().moveToOffset(startOffset);
                                editor.getScrollingModel().scrollToCaret(com.intellij.openapi.editor.ScrollType.CENTER);
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    LOG.warn("Error opening file and highlighting", e);
                }
            });
        }

        /**
         * 复制内容到剪贴板并显示通知
         */
        private void copyToClipboardAndNotify(String content, String message) {
            try {
                StringSelection stringSelection = new StringSelection(content);
                Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stringSelection, null);

                Balloon balloon = JBPopupFactory.getInstance()
                        .createHtmlTextBalloonBuilder(message, AllIcons.General.NotificationInfo,
                            JBUI.CurrentTheme.NotificationInfo.backgroundColor(), null)
                        .setFadeoutTime(3000)
                        .createBalloon();
                balloon.show(RelativePoint.getCenterOf(this), Balloon.Position.above);

                LOG.info("Copied suggested code to clipboard for suggestion: " + suggestion.id);

            } catch (Exception e) {
                LOG.error("Error copying to clipboard", e);
                showApplyError("复制到剪贴板失败: " + e.getMessage());
            }
        }

        /**
         * 显示应用成功消息
         */
        private void showApplySuccess(String message) {
            Balloon balloon = JBPopupFactory.getInstance()
                    .createHtmlTextBalloonBuilder(message, AllIcons.General.InspectionsOK,
                        JBUI.CurrentTheme.NotificationInfo.backgroundColor(), null)
                    .setFadeoutTime(3000)
                    .createBalloon();
            balloon.show(RelativePoint.getCenterOf(this), Balloon.Position.above);
        }

        /**
         * 显示应用错误消息
         */
        private void showApplyError(String message) {
            Balloon balloon = JBPopupFactory.getInstance()
                    .createHtmlTextBalloonBuilder(message, AllIcons.General.NotificationError,
                        JBColor.RED.brighter(), null)
                    .setFadeoutTime(5000)
                    .createBalloon();
            balloon.show(RelativePoint.getCenterOf(this), Balloon.Position.above);

            LOG.warn("Apply suggestion failed: " + message + ", suggestion: " + suggestion.id);
        }

        /**
         * 执行拒绝建议的具体逻辑
         */
        private void executeRejectAction() {
            // TODO: 这里实现具体的拒绝建议逻辑
            // 可能包括：
            // 1. 调用API更新建议状态
            // 2. 更新UI显示

            // 临时实现 - 显示操作成功信息
            SwingUtilities.invokeLater(() -> {
                String mode = isRealTimeMode ? "实时" : "历史";
                JOptionPane.showMessageDialog(this,
                        mode + "模式下成功拒绝建议: " + suggestion.id,
                        "操作成功",
                        JOptionPane.INFORMATION_MESSAGE);
            });
        }

        private void toggleExpanded() {
            // 如果有其他展开的面板，先折叠（但不通知监听器）
            if (expandedPanel != null && expandedPanel != this) {
                expandedPanel.collapseInternal();
            }

            if (isExpanded) {
                collapse();
            } else {
                expand();
            }
        }

        private void expand() {
            isExpanded = true;
            expandedPanel = this;

            // 批量进行UI更新，减少重复渲染
            SwingUtilities.invokeLater(() -> {
                // 1. 如果contentPanel还没创建，先创建它
                if (contentPanel == null) {
                    createContentPanel();
                }

                // 2. 添加内容面板
                add(contentPanel, BorderLayout.CENTER);

                // 3. 更改图标
                expandButton.setIcon(AllIcons.General.ArrowDown);

                // 4. 使用固定高度，避免动态计算导致的多次布局
                int fixedHeight = JBUI.scale(280); // 使用固定的合理高度
                setPreferredSize(new Dimension(0, fixedHeight));
                setMaximumSize(new Dimension(Integer.MAX_VALUE, fixedHeight));
                setMinimumSize(new Dimension(0, fixedHeight));

                // 5. 一次性完成布局更新
                revalidate();
                repaint();

                // 6. 延迟通知监听器，避免与布局冲突
                SwingUtilities.invokeLater(() -> {
                    if (expansionListener != null) {
                        expansionListener.onExpansionChanged(true);
                    }
                });
            });
        }

        private void collapse() {
            collapseInternal();
            
            // 通知监听器没有条目展开了
            if (expansionListener != null) {
                expansionListener.onExpansionChanged(false);
            }
        }
        
        /**
         * 内部折叠方法，不通知监听器
         * 用于在切换展开项时避免触发高度变化
         */
        private void collapseInternal() {
            isExpanded = false;
            if (expandedPanel == this) {
                expandedPanel = null;
            }

            // 批量进行UI更新，减少重复渲染
            SwingUtilities.invokeLater(() -> {
                // 1. 移除内容面板（如果存在）
                if (contentPanel != null) {
                    remove(contentPanel);
                }

                // 2. 更改图标
                expandButton.setIcon(AllIcons.General.ArrowRight);

                // 3. 设置折叠高度
                setPreferredSize(new Dimension(0, ITEM_HEIGHT));
                setMaximumSize(new Dimension(Integer.MAX_VALUE, ITEM_HEIGHT));
                setMinimumSize(new Dimension(0, ITEM_HEIGHT));

                // 4. 一次性完成布局更新
                revalidate();
                repaint();
            });
        }

        private String getStatusText() {
            SuggestionState state = SuggestionState.fromStatusName(
                suggestion.status.name(), 
                suggestion.action.name()
            );
            return state.getText();
        }

        private JBColor getStatusColor() {
            SuggestionState state = SuggestionState.fromStatusName(
                suggestion.status.name(), 
                suggestion.action.name()
            );
            return state.getColor();
        }

        private String formatFileInfo() {
            String fileName = suggestion.filePath.substring(suggestion.filePath.lastIndexOf('/') + 1);
            return String.format("%s:%d-%d", fileName, suggestion.startLineNumber, suggestion.endLineNumber);
        }

        /**
         * 获取摘要文本，只显示建议的简短描述
         */
        private String getSummaryText() {
            String description = suggestion.suggestionDescription;

            // 提取描述的前60个字符作为摘要
            if (description != null && description.length() > 60) {
                return description.substring(0, 60).trim() + "...";
            } else {
                return description != null ? description.trim() : "";
            }
        }

        private JBColor getLevelColor() {
            String level = suggestion.level;
            return switch (level) {
                case "CRITICAL", "BLOCKER" ->
                    new JBColor(JBUI.CurrentTheme.NotificationError.backgroundColor(),
                            JBUI.CurrentTheme.NotificationError.backgroundColor());
                case "MAJOR" ->
                    new JBColor(JBUI.CurrentTheme.NotificationWarning.backgroundColor(),
                            JBUI.CurrentTheme.NotificationWarning.backgroundColor());
                case "MINOR" ->
                    new JBColor(JBUI.CurrentTheme.NotificationWarning.backgroundColor(),
                            JBUI.CurrentTheme.NotificationWarning.backgroundColor());
                default -> // INFO
                    new JBColor(JBUI.CurrentTheme.List.Selection.background(false),
                            JBUI.CurrentTheme.List.Selection.background(false));
            };
        }

        private boolean isPending() {
            return "SUGGESTION_STATUS_UNDONE".equals(suggestion.status.name());
        }

        /**
         * 检查是否点击在展开按钮上
         */
        private boolean isClickOnExpandButton(MouseEvent e) {
            Point buttonLocation = SwingUtilities.convertPoint(this, e.getPoint(), expandButton);
            return expandButton.contains(buttonLocation);
        }

        /**
         * 计算展开后的动态高度
         */
        private int calculateExpandedHeight() {
            // 基础高度：头部面板高度
            int baseHeight = ITEM_HEIGHT;

            // 强制重新计算内容面板的尺寸
            contentPanel.invalidate();
            contentPanel.doLayout();
            contentPanel.revalidate();

            // 计算内容面板的实际高度
            int contentHeight = contentPanel.getPreferredSize().height;

            // 调整边距以适应新的布局
            int margin = JBUI.scale(8);

            int totalHeight = baseHeight + contentHeight + margin;

            // 设置更合理的最小高度，适应增加的描述区域
            return Math.max(totalHeight, JBUI.scale(200));
        }

        /**
         * 打开diff窗口显示建议的代码变更
         */
        private void openDiffWindow() {
            LOG.debug("openDiffWindow called for suggestion: " + suggestion.id);
            
            ApplicationManager.getApplication().invokeLater(() -> {
                try {
                    LOG.debug("Processing diff window request for file: " + suggestion.filePath);
                    
                    // 获取文件路径
                    String filePath = suggestion.filePath;
                    if (filePath == null || filePath.isEmpty()) {
                        LOG.warn("File path is empty for suggestion: " + suggestion.id);
                        return;
                    }

                    VirtualFile virtualFile = null;
                    
                    // 首先尝试作为绝对路径
                    virtualFile = VirtualFileManager.getInstance().findFileByUrl("file://" + filePath);
                    
                    // 如果失败，尝试作为相对路径
                    if (virtualFile == null) {
                        String fullPath = project.getBasePath() + "/" + filePath;
                        virtualFile = VirtualFileManager.getInstance().findFileByUrl("file://" + fullPath);
                    }
                    
                    // 如果还是失败，尝试使用 LocalFileSystem
                    if (virtualFile == null) {
                        virtualFile = LocalFileSystem.getInstance().findFileByPath(filePath);
                    }
                    
                    if (virtualFile == null && project.getBasePath() != null) {
                        String relativePath = filePath.startsWith(project.getBasePath()) 
                            ? filePath.substring(project.getBasePath().length() + 1) 
                            : filePath;
                        virtualFile = LocalFileSystem.getInstance().findFileByPath(project.getBasePath() + "/" + relativePath);
                    }
                    
                    if (virtualFile == null) {
                        LOG.warn("Virtual file not found for path: " + filePath);
                        return;
                    }

                    // 读取当前文件内容
                    String currentFileContent = new String(virtualFile.contentsToByteArray(), virtualFile.getCharset());
                    
                    // 创建应用建议后的文件内容
                    String modifiedFileContent = applyCodeSuggestionToFile(currentFileContent, suggestion);
                    
                    // 创建diff内容
                    DiffContentFactory contentFactory = DiffContentFactory.getInstance();
                    DiffContent originalContent = contentFactory.create(project, currentFileContent, virtualFile.getFileType());
                    DiffContent modifiedContent = contentFactory.create(project, modifiedFileContent, virtualFile.getFileType());

                    // 创建diff请求
                    String title = String.format("代码建议 - %s (行 %d-%d)",
                            virtualFile.getName(),
                            suggestion.startLineNumber,
                            suggestion.endLineNumber);

                    // 检查是否已有该建议的diff窗口打开
                    if (DiffWindowManager.isDiffWindowOpen(project, suggestion.id)) {
                        DiffWindowManager.activateDiffWindow(project, suggestion.id);
                        return;
                    }

                    // 创建标准的 diff 请求
                    SimpleDiffRequest request = new SimpleDiffRequest(
                            title,
                            originalContent,
                            modifiedContent,
                            "当前文件",
                            "应用建议后");

                    // 创建新的diff窗口
                    DiffManager.getInstance().showDiff(project, request);

                    // 记录新创建的窗口
                    DiffWindowManager.recordDiffWindow(project, suggestion.id);

                    LOG.debug("Opened diff view for suggestion: " + suggestion.id);

                } catch (Exception e) {
                    LOG.error("Failed to open file diff view for suggestion: " + suggestion.id, e);
                }
            });
        }

        /**
         * 将建议应用到完整文件内容中
         */
        private String applyCodeSuggestionToFile(String fileContent, IntellicodeAPI.ReviewSuggestionInfo suggestion) {
            try {
                String originalCode = suggestion.originalCodeSnippet;
                String suggestedCode = suggestion.suggestedCodeSnippet;
                
                if (originalCode == null || suggestedCode == null) {
                    return fileContent;
                }
                
                // 查找原始代码在文件中的位置
                int startIndex = fileContent.indexOf(originalCode);
                if (startIndex == -1) {
                    // 如果找不到完全匹配，尝试按行号定位
                    return applyCodeSuggestionByLineNumber(fileContent, suggestion);
                }
                
                // 替换代码
                StringBuilder modifiedContent = new StringBuilder(fileContent);
                modifiedContent.replace(startIndex, startIndex + originalCode.length(), suggestedCode);
                
                return modifiedContent.toString();
                
            } catch (Exception e) {
                LOG.error("Failed to apply code suggestion to file", e);
                return fileContent;
            }
        }

        /**
         * 根据行号应用代码建议
         */
        private String applyCodeSuggestionByLineNumber(String fileContent, IntellicodeAPI.ReviewSuggestionInfo suggestion) {
            try {
                String[] lines = fileContent.split("\n");
                int startLine = suggestion.startLineNumber - 1; // 转换为0基索引
                int endLine = suggestion.endLineNumber - 1;
                
                if (startLine < 0 || endLine >= lines.length || startLine > endLine) {
                    return fileContent;
                }
                
                // 构建修改后的内容
                StringBuilder modifiedContent = new StringBuilder();
                
                // 添加建议前的行
                for (int i = 0; i < startLine; i++) {
                    modifiedContent.append(lines[i]).append("\n");
                }
                
                // 添加建议的代码
                if (suggestion.suggestedCodeSnippet != null) {
                    modifiedContent.append(suggestion.suggestedCodeSnippet);
                    if (!suggestion.suggestedCodeSnippet.endsWith("\n")) {
                        modifiedContent.append("\n");
                    }
                }
                
                // 添加建议后的行
                for (int i = endLine + 1; i < lines.length; i++) {
                    modifiedContent.append(lines[i]);
                    if (i < lines.length - 1) {
                        modifiedContent.append("\n");
                    }
                }
                
                return modifiedContent.toString();
                
            } catch (Exception e) {
                LOG.error("Failed to apply code suggestion by line number", e);
                return fileContent;
            }
        }
    }

    /**
     * 建议排序器
     * 排序规则：UNDONE > DONE+AGREE > DONE+REJECT
     */
    private static class SuggestionComparator implements Comparator<IntellicodeAPI.ReviewSuggestionInfo> {
        @Override
        public int compare(IntellicodeAPI.ReviewSuggestionInfo s1, IntellicodeAPI.ReviewSuggestionInfo s2) {
            return getStatusPriority(s1) - getStatusPriority(s2);
        }

        private int getStatusPriority(IntellicodeAPI.ReviewSuggestionInfo suggestion) {
            String statusName = suggestion.status.name();
            if ("SUGGESTION_STATUS_UNDONE".equals(statusName)) {
                return 1; // UNDONE - 最高优先级
            } else if ("SUGGESTION_STATUS_DONE".equals(statusName)) {
                String actionName = suggestion.action.name();
                if ("FEEDBACK_ACTION_AGREE".equals(actionName)) {
                    return 2; // DONE + AGREE - 中等优先级
                } else if ("FEEDBACK_ACTION_REJECT".equals(actionName)) {
                    return 3; // DONE + REJECT - 最低优先级
                } else {
                    return 4; // 未知操作 - 更低优先级
                }
            } else {
                return 5; // 未知状态 - 最低优先级
            }
        }
    }







    @Override
    public void dispose() {
        try {
            clearSuggestionPanels();

            if (suggestions != null) {
                suggestions.clear();
            }

            // 清理diff窗口管理器中已关闭的窗口
            DiffWindowManager.cleanup(project);

            LOG.debug("SuggestionListComponent disposed for reviewId: " + reviewId);
        } catch (Exception e) {
            LOG.error("Error disposing SuggestionListComponent", e);
        }
    }
}