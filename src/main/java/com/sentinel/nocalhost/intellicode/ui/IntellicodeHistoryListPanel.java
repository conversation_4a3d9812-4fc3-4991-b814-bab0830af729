package com.sentinel.nocalhost.intellicode.ui;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.ui.SimpleToolWindowPanel;
import com.intellij.openapi.ui.popup.Balloon;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.openapi.util.Disposer;
import com.intellij.ui.CollectionListModel;
import com.intellij.ui.JBColor;
import com.intellij.ui.awt.RelativePoint;
import com.intellij.ui.components.*;
import com.intellij.util.ui.JBUI;
import com.intellij.icons.AllIcons;
import org.jetbrains.annotations.NotNull;
import com.sentinel.nocalhost.intellicode.api.IntellicodeAPI;
import com.sentinel.nocalhost.intellicode.ui.handler.ReviewRecordCellRenderer;
import com.sentinel.nocalhost.intellicode.ui.model.ReviewRecordItem;
import intellicode.api.v1.Api.ReviewStatus;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Intellicode 历史记录列表面板
 * 显示代码审查的历史记录列表
 */
public class IntellicodeHistoryListPanel implements Disposable {
    
    private static final Logger LOG = Logger.getInstance(IntellicodeHistoryListPanel.class);
    
    // 常量定义
    private static final String TITLE_HISTORY = "审查历史";
    private static final String SUBTITLE_DESCRIPTION = "查看代码审查的历史记录";
    private static final int AUTO_REFRESH_INTERVAL = 10000; // 10秒
    private static final int DEFAULT_LIMIT = 10; // 默认加载10条记录
    
    private final Project project;
    private final IntellicodeAPI api;
    
    // UI 组件
    private SimpleToolWindowPanel mainPanel;
    private JPanel contentPanel;
    private JPanel headerPanel;
    private JBList<ReviewRecordItem> historyList;
    private JBScrollPane scrollPane;
    private CollectionListModel<ReviewRecordItem> listModel;
    private JBLabel titleLabel;
    private JBLabel descriptionLabel;
    private JBLabel countLabel;
    private JBLoadingPanel loadingPanel;
    
    // 数据管理
    private final Map<Integer, ReviewRecordItem> recordCache = new HashMap<>();
    private javax.swing.Timer autoRefreshTimer;
    private boolean isLoading = false;
    
    
    // 页面切换回调
    private IntellicodeHomePanel homePanel;
    
    public IntellicodeHistoryListPanel(@NotNull Project project) {
        this.project = project;
        this.api = IntellicodeAPI.getInstance();
        this.listModel = new CollectionListModel<>();
        
        try {
            initializeUI();
            setupEventHandlers();
            loadInitialData();
        } catch (Exception e) {
            LOG.error("Failed to initialize IntellicodeHistoryListPanel", e);
            createErrorPanel(e);
        }
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 创建主面板
        mainPanel = new SimpleToolWindowPanel(false, true);
        mainPanel.setProvideQuickActions(false);
        
        
        // 创建内容面板
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBorder(JBUI.Borders.empty(12, 12, 0, 12));
        contentPanel.setBackground(JBUI.CurrentTheme.DefaultTabs.background());
        
        // 创建各个UI组件
        createHeaderPanel();
        createHistoryListPanel();
        
        // 创建加载面板
        loadingPanel = new JBLoadingPanel(new BorderLayout(), this);
        loadingPanel.add(scrollPane, BorderLayout.CENTER);
        
        // 布局
        contentPanel.add(headerPanel, BorderLayout.NORTH);
        contentPanel.add(loadingPanel, BorderLayout.CENTER);
        
        mainPanel.setContent(contentPanel);
    }
    
    /**
     * 创建标题区域
     */
    private void createHeaderPanel() {
        headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBorder(JBUI.Borders.emptyBottom(16));
        headerPanel.setOpaque(false);
        
        // 左侧：标题和描述
        JPanel leftPanel = new JPanel(new GridBagLayout());
        leftPanel.setOpaque(false);
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.anchor = GridBagConstraints.WEST;
        
        // 主标题
        titleLabel = new JBLabel(TITLE_HISTORY);
        titleLabel.setFont(JBUI.Fonts.label(16).asBold());
        titleLabel.setForeground(JBUI.CurrentTheme.Label.foreground());
        gbc.gridx = 0;
        gbc.gridy = 0;
        leftPanel.add(titleLabel, gbc);
        
        // 描述文字
        descriptionLabel = new JBLabel(SUBTITLE_DESCRIPTION);
        descriptionLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        descriptionLabel.setFont(JBUI.Fonts.label(12));
        gbc.gridy = 1;
        gbc.insets = JBUI.insetsTop(4);
        leftPanel.add(descriptionLabel, gbc);
        
        // 右侧：计数标签
        countLabel = new JBLabel("共 0 条记录");
        countLabel.setFont(JBUI.Fonts.label(12));
        countLabel.setForeground(JBUI.CurrentTheme.Label.disabledForeground());
        
        headerPanel.add(leftPanel, BorderLayout.WEST);
        headerPanel.add(countLabel, BorderLayout.EAST);
    }
    
    
    /**
     * 创建历史记录列表
     */
    private void createHistoryListPanel() {
        // 创建列表
        historyList = new JBList<>(listModel);
        historyList.setCellRenderer(new ReviewRecordCellRenderer());
        historyList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        historyList.setBackground(JBUI.CurrentTheme.List.BACKGROUND);
        historyList.setFixedCellHeight(JBUI.scale(36)); // 固定单行行高
        historyList.setEmptyText("暂无审查记录，开始新的代码审查吧");
        historyList.setBorder(JBUI.Borders.empty());
        
        // 创建滚动面板
        scrollPane = new JBScrollPane(historyList);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setBorder(JBUI.Borders.customLine(
            JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground(), 1, 0, 0, 0
        ));
        scrollPane.setBackground(JBUI.CurrentTheme.List.BACKGROUND);
        scrollPane.getViewport().setBackground(JBUI.CurrentTheme.List.BACKGROUND);
        scrollPane.getVerticalScrollBar().setUnitIncrement(16);
        
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 列表选择事件
        historyList.addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    handleListSelectionChange();
                }
            }
        });
        
        // 列表点击事件（处理复制按钮点击）
        historyList.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getButton() == MouseEvent.BUTTON1) {
                    handleListClick(e);
                }
            }
        });
        
        // 启动自动刷新
        startAutoRefresh();
    }
    
    /**
     * 处理列表点击事件
     */
    private void handleListClick(@NotNull MouseEvent e) {
        int index = historyList.locationToIndex(e.getPoint());
        if (index >= 0 && index < historyList.getModel().getSize()) {
            Rectangle cellBounds = historyList.getCellBounds(index, index);
            if (cellBounds != null && cellBounds.contains(e.getPoint())) {
                ReviewRecordItem item = historyList.getModel().getElementAt(index);
                if (item != null) {
                    // 检查是否点击在复制按钮区域（右侧约30px区域）
                    int clickX = e.getPoint().x;
                    int copyButtonZoneX = cellBounds.x + cellBounds.width - 40; // 复制按钮区域
                    
                    if (clickX >= copyButtonZoneX) {
                        // 点击在复制按钮区域 - 执行复制功能
                        copySessionIdWithBalloon(item, e.getComponent(), e.getPoint());
                    } else {
                        // 点击在其他区域 - 跳转到 AgentWorkspace
                        navigateToAgentWorkspace(item);
                    }
                }
            }
        }
    }
    
    /**
     * 跳转到 AgentWorkspace 页面
     */
    private void navigateToAgentWorkspace(@NotNull ReviewRecordItem item) {
        try {
            LOG.info("Navigating to AgentWorkspace for sessionId: " + item.getSessionId() + ", reviewId: " + item.getReviewId());
            
            if (homePanel != null) {
                homePanel.showAgentWorkspaceHistoryMode(item.getSessionId(), item.getReviewId());
            } else {
                LOG.warn("Home panel reference is null, cannot navigate to AgentWorkspace");
            }
        } catch (Exception e) {
            LOG.error("Failed to navigate to AgentWorkspace", e);
            // 显示错误提示
            Messages.showErrorDialog(project, 
                "跳转到详情页面失败: " + e.getMessage(), 
                "跳转失败");
        }
    }
    
    /**
     * 复制Session ID并显示Balloon提示
     */
    private void copySessionIdWithBalloon(@NotNull ReviewRecordItem item, @NotNull Component component, @NotNull Point clickPoint) {
        try {
            String sessionId = item.getSessionId();
            StringSelection selection = new StringSelection(sessionId);
            Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);
            
            // 显示Balloon提示
            Balloon balloon = JBPopupFactory.getInstance()
                .createHtmlTextBalloonBuilder(
                    "Session ID 已复制",
                    AllIcons.Actions.Copy,
                    JBUI.CurrentTheme.NotificationInfo.backgroundColor(),
                    null)
                .setFadeoutTime(2000)
                .setAnimationCycle(200)
                .createBalloon();
            
            // 在点击位置显示
            balloon.show(new RelativePoint(component, clickPoint), Balloon.Position.above);
            
            LOG.info("Session ID copied via copy button: " + item.getShortSessionId());
            
        } catch (Exception ex) {
            LOG.warn("Failed to copy session ID via copy button", ex);
            
            // 显示错误提示
            Balloon balloon = JBPopupFactory.getInstance()
                .createHtmlTextBalloonBuilder(
                    "复制失败: " + ex.getMessage(),
                    AllIcons.General.NotificationError,
                    JBColor.RED.brighter(),
                    null)
                .setFadeoutTime(3000)
                .createBalloon();
            
            balloon.show(new RelativePoint(component, clickPoint), Balloon.Position.above);
        }
    }
    
    
    
    /**
     * 处理列表选择变化
     */
    private void handleListSelectionChange() {
        ReviewRecordItem selectedItem = historyList.getSelectedValue();
        if (selectedItem != null) {
            LOG.debug("Selected review record: " + selectedItem.getSessionId());
            // 这里可以扩展选中项的处理逻辑，比如在详情面板显示信息
        }
    }
    
    /**
     * 加载初始数据
     */
    private void loadInitialData() {
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            refreshHistoryList();
        });
    }
    
    /**
     * 刷新历史记录列表
     */
    private void refreshHistoryList() {
        if (isLoading) {
            LOG.debug("Already loading, skipping refresh request");
            return;
        }
        
        isLoading = true;
        
        SwingUtilities.invokeLater(() -> {
            loadingPanel.startLoading();
        });
        
        // 获取仓库路径
        String repoPath = project.getBasePath();
        if (repoPath == null) {
            SwingUtilities.invokeLater(() -> {
                showError("无法获取项目路径");
                stopLoading();
            });
            return;
        }
        
        // 调用API
        CompletableFuture<List<IntellicodeAPI.ReviewRecordInfo>> future = 
            api.listReviewRecords(repoPath, DEFAULT_LIMIT);
        
        future.whenComplete((records, throwable) -> {
            SwingUtilities.invokeLater(() -> {
                try {
                    if (throwable != null) {
                        LOG.warn("Failed to load review records", throwable);
                        showError("加载历史记录失败: " + throwable.getMessage());
                    } else {
                        updateHistoryList(records);
                        LOG.info("Loaded " + records.size() + " review records");
                    }
                } finally {
                    stopLoading();
                }
            });
        });
    }
    
    /**
     * 更新历史记录列表
     */
    private void updateHistoryList(@NotNull List<IntellicodeAPI.ReviewRecordInfo> records) {
        try {
            // 更新缓存
            recordCache.clear();
            List<ReviewRecordItem> items = new ArrayList<>();
            
            for (IntellicodeAPI.ReviewRecordInfo record : records) {
                ReviewRecordItem item = new ReviewRecordItem(record);
                recordCache.put(record.reviewId, item);
                items.add(item);
            }
            
            // 排序（最新的在前）
            items.sort(ReviewRecordItem::compareTo);
            
            // 更新UI
            SwingUtilities.invokeLater(() -> {
                listModel.removeAll();
                listModel.addAll(0, items);
                updateCountLabel(items.size());
            });
            
        } catch (Exception e) {
            LOG.error("Failed to update history list", e);
            showError("更新列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新计数标签
     */
    private void updateCountLabel(int count) {
        countLabel.setText("共 " + count + " 条记录");
    }
    
    
    /**
     * 显示错误信息
     */
    private void showError(@NotNull String message) {
        LOG.warn("Showing error to user: " + message);
        // 可以在这里显示错误状态的UI
        // 暂时通过对话框显示
        Messages.showWarningDialog(project, message, "加载失败");
    }
    
    /**
     * 停止加载状态
     */
    private void stopLoading() {
        isLoading = false;
        loadingPanel.stopLoading();
    }
    
    /**
     * 启动自动刷新
     */
    private void startAutoRefresh() {
        stopAutoRefresh(); // 先停止现有的定时器
        
        autoRefreshTimer = new javax.swing.Timer(AUTO_REFRESH_INTERVAL, e -> {
            if (!isLoading) {
                ApplicationManager.getApplication().executeOnPooledThread(() -> {
                    refreshHistoryList();
                });
            }
        });
        autoRefreshTimer.start();
        LOG.debug("Auto refresh started with interval: " + AUTO_REFRESH_INTERVAL + "ms");
    }
    
    /**
     * 停止自动刷新
     */
    private void stopAutoRefresh() {
        if (autoRefreshTimer != null) {
            autoRefreshTimer.stop();
            autoRefreshTimer = null;
            LOG.debug("Auto refresh stopped");
        }
    }
    
    /**
     * 暂停自动刷新（供外部调用）
     */
    public void pauseAutoRefresh() {
        if (autoRefreshTimer != null && autoRefreshTimer.isRunning()) {
            autoRefreshTimer.stop();
            LOG.debug("Auto refresh paused");
        }
    }
    
    /**
     * 恢复自动刷新（供外部调用）
     */
    public void resumeAutoRefresh() {
        if (autoRefreshTimer != null && !autoRefreshTimer.isRunning()) {
            autoRefreshTimer.start();
            LOG.debug("Auto refresh resumed");
        } else if (autoRefreshTimer == null) {
            // 如果定时器为空，重新启动自动刷新
            startAutoRefresh();
        }
    }
    
    /**
     * 创建错误面板
     */
    private void createErrorPanel(@NotNull Exception e) {
        mainPanel = new SimpleToolWindowPanel(true);
        JPanel errorPanel = new JPanel(new BorderLayout());
        errorPanel.setBorder(JBUI.Borders.empty(20));
        
        JBLabel errorLabel = new JBLabel(
            "<html><div style='text-align: center;'>" +
            "<h3>历史记录初始化失败</h3>" +
            "<p>错误信息: " + e.getMessage() + "</p>" +
            "<p>请检查网络连接或重试</p>" +
            "</div></html>"
        );
        errorLabel.setHorizontalAlignment(SwingConstants.CENTER);
        errorLabel.setForeground(JBColor.RED);
        
        errorPanel.add(errorLabel, BorderLayout.CENTER);
        mainPanel.setContent(errorPanel);
    }
    
    /**
     * 设置首页面板引用（用于页面切换）
     */
    public void setHomePanel(@Nullable IntellicodeHomePanel homePanel) {
        this.homePanel = homePanel;
    }
    
    /**
     * 返回到首页
     */
    public void backToHome() {
        if (homePanel != null) {
            homePanel.showHomePanel();
        }
    }
    
    /**
     * 获取主面板
     */
    @NotNull
    public JPanel getMainPanel() {
        return (JPanel) mainPanel;
    }
    
    /**
     * 手动刷新数据（供外部调用）
     */
    public void refreshData() {
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            refreshHistoryList();
        });
    }
    
    @Override
    public void dispose() {
        try {
            // 停止自动刷新
            stopAutoRefresh();
            
            
            // 清理数据
            if (listModel != null) {
                listModel.removeAll();
            }
            if (recordCache != null) {
                recordCache.clear();
            }
            
            // 停止加载
            if (loadingPanel != null) {
                loadingPanel.stopLoading();
            }
            
            LOG.debug("IntellicodeHistoryListPanel disposed");
        } catch (Exception e) {
            LOG.error("Error disposing IntellicodeHistoryListPanel", e);
        }
    }
    
}