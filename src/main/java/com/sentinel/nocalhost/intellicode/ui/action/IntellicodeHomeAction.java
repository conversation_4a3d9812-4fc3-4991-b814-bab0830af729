package com.sentinel.nocalhost.intellicode.ui.action;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.intellicode.ui.IntellicodeHomePanel;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

/**
 * Intellicode Home 按钮动作
 * 用于导航到 Intellicode 主页
 */
public class IntellicodeHomeAction extends DumbAwareAction {
    private final Project project;

    public IntellicodeHomeAction(Project project) {
        super("Home", StringUtils.EMPTY, AllIcons.Nodes.HomeFolder);
        this.project = project;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        try {
            // 获取 JetDev 工具窗口
            com.intellij.openapi.wm.ToolWindow toolWindow = 
                com.intellij.openapi.wm.ToolWindowManager.getInstance(project)
                    .getToolWindow("JetDev");
            
            if (toolWindow != null) {
                // 激活工具窗口
                toolWindow.activate(() -> {
                    // 获取工具窗口内容管理器
                    com.intellij.ui.content.ContentManager contentManager = toolWindow.getContentManager();
                    
                    // 查找 "Intellicode Home" 标签页
                    com.intellij.ui.content.Content intellicodeContent = null;
                    for (com.intellij.ui.content.Content content : contentManager.getContents()) {
                        if ("Intellicode Home".equals(content.getDisplayName())) {
                            intellicodeContent = content;
                            break;
                        }
                    }
                    
                    if (intellicodeContent != null) {
                        // 选中 Intellicode Home 标签页
                        contentManager.setSelectedContent(intellicodeContent);
                        
                        // 获取组件并查找 IntellicodeHomePanel
                        javax.swing.JComponent component = intellicodeContent.getComponent();
                        IntellicodeHomePanel homePanel = findHomePanelInComponent(component);
                        
                        if (homePanel != null) {
                            // 返回到 Home 页面
                            homePanel.showHomePanel();
                        } else {
                            com.intellij.openapi.ui.Messages.showWarningDialog(
                                project,
                                "无法找到 Intellicode 主面板",
                                "页面切换失败"
                            );
                        }
                    } else {
                        com.intellij.openapi.ui.Messages.showWarningDialog(
                            project,
                            "无法找到 Intellicode Home 标签页",
                            "页面切换失败"
                        );
                    }
                });
            } else {
                com.intellij.openapi.ui.Messages.showWarningDialog(
                    project,
                    "请先打开 JetDev 工具窗口",
                    "工具窗口未找到"
                );
            }
        } catch (Exception e) {
            com.intellij.openapi.diagnostic.Logger.getInstance(IntellicodeHomeAction.class)
                .error("Failed to show home panel", e);
            com.intellij.openapi.ui.Messages.showErrorDialog(
                project,
                "返回主页失败: " + e.getMessage(),
                "错误"
            );
        }
    }
    
    /**
     * 在组件树中查找 IntellicodeHomePanel 实例
     */
    private IntellicodeHomePanel findHomePanelInComponent(javax.swing.JComponent component) {
        // 检查当前组件是否包含 IntellicodeHomePanel
        if (component instanceof javax.swing.JPanel) {
            javax.swing.JPanel panel = (javax.swing.JPanel) component;
            
            // 检查是否是 SimpleToolWindowPanel，这是 IntellicodeHomePanel 的主容器
            if (panel instanceof com.intellij.openapi.ui.SimpleToolWindowPanel) {
                // 尝试通过客户端属性获取 IntellicodeHomePanel 实例
                Object homePanelRef = panel.getClientProperty("IntellicodeHomePanel");
                if (homePanelRef instanceof IntellicodeHomePanel) {
                    return (IntellicodeHomePanel) homePanelRef;
                }
            }
        }
        
        // 递归搜索子组件
        if (component instanceof java.awt.Container) {
            java.awt.Container container = (java.awt.Container) component;
            for (java.awt.Component child : container.getComponents()) {
                if (child instanceof javax.swing.JComponent) {
                    IntellicodeHomePanel found = findHomePanelInComponent((javax.swing.JComponent) child);
                    if (found != null) {
                        return found;
                    }
                }
            }
        }
        
        return null;
    }
}