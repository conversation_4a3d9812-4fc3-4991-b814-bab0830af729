package com.sentinel.nocalhost.intellicode.api;

/**
 * Intellicode API 专用异常类
 * 提供详细的错误分类和上下文信息
 */
public class IntellicodeException extends Exception {
    
    private final ErrorType errorType;
    
    public IntellicodeException(String message, ErrorType errorType) {
        super(message);
        this.errorType = errorType;
    }
    
    public IntellicodeException(String message, ErrorType errorType, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
    }
    
    public ErrorType getErrorType() {
        return errorType;
    }
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        /**
         * 网络连接错误
         */
        NETWORK_ERROR,
        
        /**
         * 认证错误（Token 无效或过期）
         */
        AUTHENTICATION_ERROR,
        
        /**
         * 请求参数错误
         */
        INVALID_REQUEST,
        
        /**
         * 服务不可用
         */
        SERVICE_UNAVAILABLE,
        
        /**
         * 服务器内部错误
         */
        SERVER_ERROR,
        
        /**
         * 请求频率限制
         */
        RATE_LIMIT_EXCEEDED,
        
        /**
         * SSE 流处理错误
         */
        SSE_ERROR,
        
        /**
         * 未知错误
         */
        UNKNOWN_ERROR
    }
    
    /**
     * 获取用户友好的错误消息
     */
    public String getUserFriendlyMessage() {
        return switch (errorType) {
            case NETWORK_ERROR -> "网络连接失败，请检查网络设置";
            case AUTHENTICATION_ERROR -> "认证失败，请重新登录";
            case INVALID_REQUEST -> "请求参数错误：" + getMessage();
            case SERVICE_UNAVAILABLE -> "Intellicode 服务暂时不可用，请稍后重试";
            case SERVER_ERROR -> "服务器内部错误，请稍后重试";
            case RATE_LIMIT_EXCEEDED -> "请求过于频繁，请稍后重试";
            case SSE_ERROR -> "实时通信连接异常";
            case UNKNOWN_ERROR -> "未知错误：" + getMessage();
        };
    }
    
    /**
     * 判断是否为可重试的错误
     */
    public boolean isRetryable() {
        return switch (errorType) {
            case NETWORK_ERROR, SERVICE_UNAVAILABLE, SERVER_ERROR, RATE_LIMIT_EXCEEDED, SSE_ERROR -> true;
            case AUTHENTICATION_ERROR, INVALID_REQUEST, UNKNOWN_ERROR -> false;
        };
    }
}