package com.sentinel.nocalhost.intellicode.api;

import com.alibaba.fastjson.JSON;
import com.google.protobuf.util.JsonFormat;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.components.Service;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.Config;
import com.sentinel.nocalhost.intellicode.sse.IntellicodeSSEClient;
import com.sentinel.nocalhost.intellicode.api.IntellicodeSSEListener;
import com.sentinel.nocalhost.intellicode.api.IntellicodeException;
import com.sentinel.nocalhost.settings.NocalhostSettings;
// Protobuf imports
import intellicode.api.v1.Api.CreateSuggestionFeedBackRequest;
import intellicode.api.v1.Api.CreateSuggestionFeedBackResponse;
import intellicode.api.v1.Api.FeedbackAction;
import intellicode.api.v1.Api.GetReviewRecordEventsRequest;
import intellicode.api.v1.Api.GetReviewRecordEventsResponse;
import intellicode.api.v1.Api.GetReviewRecordSuggestionsRequest;
import intellicode.api.v1.Api.GetReviewRecordSuggestionsResponse;
import intellicode.api.v1.Api.IDE;
import intellicode.api.v1.Api.ListReviewRecordsRequest;
import intellicode.api.v1.Api.ListReviewRecordsResponse;
import intellicode.api.v1.Api.ReviewRecord;
import intellicode.api.v1.Api.ReviewStatus;
import intellicode.api.v1.Api.ReviewSuggestion;
import intellicode.api.v1.Api.SuggestionStatus;
import intellicode.event.v1.EventOuterClass.Event;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * Intellicode API 服务
 * 负责与后端 Intellicode 服务通信
 */
@Service
public final class IntellicodeAPI {

    private static final Logger LOG = Logger.getInstance(IntellicodeAPI.class);
    private static final String DEFAULT_BASE_URL = "http://localhost:8080/api";
    private static final int DEFAULT_CONNECT_TIMEOUT = 30;
    private static final int DEFAULT_READ_TIMEOUT = 300; // SSE 长连接
    private static final int DEFAULT_WRITE_TIMEOUT = 30;

    private final OkHttpClient httpClient;
    private final String baseUrl;
    private final IntellicodeSSEClient sseClient;

    public IntellicodeAPI() {
        this.baseUrl = getConfiguredBaseUrl();
        this.httpClient = createHttpClient();
        this.sseClient = new IntellicodeSSEClient();
        LOG.info("IntellicodeAPI initialized with base URL: " + baseUrl);
    }

    /**
     * 创建配置化的 HTTP 客户端
     */
    private OkHttpClient createHttpClient() {
        return new OkHttpClient.Builder()
            .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .addInterceptor(new LoggingInterceptor())
            .build();
    }

    /**
     * HTTP 请求日志拦截器
     */
    private static class LoggingInterceptor implements Interceptor {

        @NotNull
        @Override
        public Response intercept(@NotNull Chain chain) throws IOException {
            Request request = chain.request();
            long startTime = System.currentTimeMillis();

            LOG.debug(
                "HTTP Request: " + request.method() + " " + request.url()
            );

            try {
                Response response = chain.proceed(request);
                long endTime = System.currentTimeMillis();

                LOG.debug(
                    "HTTP Response: " +
                    response.code() +
                    " in " +
                    (endTime - startTime) +
                    "ms"
                );

                return response;
            } catch (IOException e) {
                long endTime = System.currentTimeMillis();
                LOG.warn(
                    "HTTP Request failed in " +
                    (endTime - startTime) +
                    "ms: " +
                    e.getMessage()
                );
                throw e;
            }
        }
    }

    /**
     * 获取配置的基础 URL
     */
    private String getConfiguredBaseUrl() {
        String configUrl = Config.getProperty("intellicode_url");
        String baseUrl = configUrl != null ? configUrl : DEFAULT_BASE_URL;
        
        // 确保基础 URL 包含 /api 路径，与 proto 文件定义保持一致
        if (!baseUrl.endsWith("/api")) {
            baseUrl = baseUrl.endsWith("/") ? baseUrl + "api" : baseUrl + "/api";
        }
        
        return baseUrl;
    }

    public static IntellicodeAPI getInstance() {
        return ApplicationManager.getApplication().getService(
            IntellicodeAPI.class
        );
    }

    /**
     * 启动代码审查会话
     * @param project 项目实例
     * @param reviewFiles 需要审查的文件列表
     * @param listener SSE 事件监听器
     * @return 会话 ID
     */
    public CompletableFuture<String> startReview(
        @NotNull Project project,
        @NotNull List<String> reviewFiles,
        @NotNull IntellicodeSSEListener listener
    ) {
        return CompletableFuture.supplyAsync(() -> {
            String sessionId = "session-" + System.currentTimeMillis();

            try {
                LOG.info(
                    "Starting review session: " +
                    sessionId +
                    " for " +
                    reviewFiles.size() +
                    " files"
                );

                // 验证输入参数
                try {
                    validateReviewRequest(project, reviewFiles);
                } catch (IntellicodeException e) {
                    throw e;
                }

                // 获取认证 Token
                String authToken = getAuthToken();
                if (authToken == null) {
                    throw new IntellicodeException(
                        "请先登录后再使用 Intellicode 功能",
                        IntellicodeException.ErrorType.AUTHENTICATION_ERROR
                    );
                }

                // 使用 protobuf 构建请求体（不包含认证信息）
                String requestJson = buildProtobufRequestJson(
                    project.getBasePath(),
                    reviewFiles
                );
                LOG.debug("Request JSON: " + requestJson);

                RequestBody body = RequestBody.create(
                    requestJson,
                    MediaType.get("application/json; charset=utf-8")
                );

                // 使用 SSE 客户端建立连接
                sseClient.connect(
                    baseUrl + "/review",
                    authToken,
                    requestJson,
                    listener,
                    project
                );

                LOG.info("Review session completed successfully: " + sessionId);
                return sessionId;
            } catch (IntellicodeException e) {
                LOG.error(
                    "Intellicode API error in session " +
                    sessionId +
                    ": " +
                    e.getMessage(),
                    e
                );
                listener.onError(e);
                throw new RuntimeException(e);
            } catch (Exception e) {
                LOG.error(
                    "Unexpected error in session " +
                    sessionId +
                    ": " +
                    e.getMessage(),
                    e
                );
                IntellicodeException wrappedException =
                    new IntellicodeException(
                        "Unexpected error during review: " + e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    );
                listener.onError(wrappedException);
                throw new RuntimeException(wrappedException);
            }
        });
    }

    /**
     * 验证审查请求参数
     */
    private void validateReviewRequest(
        @NotNull Project project,
        @NotNull List<String> reviewFiles
    ) throws IntellicodeException {
        if (project.getBasePath() == null) {
            throw new IntellicodeException(
                "Project base path is null",
                IntellicodeException.ErrorType.INVALID_REQUEST
            );
        }

        if (reviewFiles.isEmpty()) {
            throw new IntellicodeException(
                "No files selected for review",
                IntellicodeException.ErrorType.INVALID_REQUEST
            );
        }

        if (reviewFiles.size() > 50) {
            // 限制文件数量
            throw new IntellicodeException(
                "Too many files selected (max 50)",
                IntellicodeException.ErrorType.INVALID_REQUEST
            );
        }
    }

    /**
     * 执行 HTTP 请求，支持重试机制
     */
    private Response executeRequestWithRetry(Request request, int maxRetries)
        throws IntellicodeException {
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                Response response = httpClient.newCall(request).execute();

                if (response.isSuccessful()) {
                    return response;
                }

                // 处理 HTTP 错误状态码
                String errorMessage =
                    "HTTP " + response.code() + ": " + response.message();
                IntellicodeException.ErrorType errorType =
                    mapHttpStatusToErrorType(response.code());

                response.close();

                if (
                    attempt == maxRetries || !isRetryableError(response.code())
                ) {
                    throw new IntellicodeException(errorMessage, errorType);
                }

                LOG.warn(
                    "Request failed (attempt " +
                    attempt +
                    "/" +
                    maxRetries +
                    "): " +
                    errorMessage
                );
            } catch (IOException e) {
                lastException = e;

                if (attempt == maxRetries) {
                    throw new IntellicodeException(
                        "Network error after " +
                        maxRetries +
                        " attempts: " +
                        e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    );
                }

                LOG.warn(
                    "Network error (attempt " +
                    attempt +
                    "/" +
                    maxRetries +
                    "): " +
                    e.getMessage()
                );
            }

            // 等待后重试
            try {
                Thread.sleep(1000 * attempt); // 递增延迟
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                throw new IntellicodeException(
                    "Request interrupted",
                    IntellicodeException.ErrorType.NETWORK_ERROR,
                    ie
                );
            }
        }

        throw new IntellicodeException(
            "Request failed after " + maxRetries + " attempts",
            IntellicodeException.ErrorType.NETWORK_ERROR,
            lastException
        );
    }

    /**
     * 将 HTTP 状态码映射到错误类型
     */
    private IntellicodeException.ErrorType mapHttpStatusToErrorType(
        int statusCode
    ) {
        return switch (statusCode) {
            case 400 -> IntellicodeException.ErrorType.INVALID_REQUEST;
            case
                401,
                403 -> IntellicodeException.ErrorType.AUTHENTICATION_ERROR;
            case 404 -> IntellicodeException.ErrorType.SERVICE_UNAVAILABLE;
            case 429 -> IntellicodeException.ErrorType.RATE_LIMIT_EXCEEDED;
            case
                500,
                502,
                503,
                504 -> IntellicodeException.ErrorType.SERVER_ERROR;
            default -> IntellicodeException.ErrorType.NETWORK_ERROR;
        };
    }

    /**
     * 判断是否为可重试的错误
     */
    private boolean isRetryableError(int statusCode) {
        return statusCode >= 500 || statusCode == 429; // 服务器错误和限流错误可重试
    }

    /**
     * 停止当前的 SSE 连接
     */
    public void stopReview() {
        if (sseClient.isConnected()) {
            sseClient.disconnect();
            LOG.info("SSE connection stopped");
        }
    }

    /**
     * 检查 SSE 连接状态
     */
    public boolean isReviewActive() {
        return sseClient.isConnected();
    }

    /**
     * 查询项目的所有审查记录
     * @param repoPath 仓库路径
     * @param limit 分页限制
     * @return 审查记录列表
     */
    public CompletableFuture<List<ReviewRecordInfo>> listReviewRecords(
        @NotNull String repoPath,
        int limit
    ) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                LOG.info(
                    "Listing review records for repo: " +
                    repoPath +
                    ", limit: " +
                    limit
                );

                // 构建查询参数
                java.util.Map<String, String> params =
                    new java.util.HashMap<>();
                params.put("repo_path", repoPath);
                if (limit > 0) {
                    params.put("limit", String.valueOf(limit));
                }

                String url = buildGetRequestUrl("/review/records", params);
                LOG.debug("GET URL: " + url);

                return executeGetRequest(url, json -> {
                    // 使用 protobuf JsonFormat 解析响应
                    ListReviewRecordsResponse.Builder builder =
                        ListReviewRecordsResponse.newBuilder();
                    JsonFormat.parser()
                        .ignoringUnknownFields()
                        .merge(json, builder);
                    ListReviewRecordsResponse response = builder.build();

                    // 转换为便于使用的数据结构
                    List<ReviewRecordInfo> records =
                        new java.util.ArrayList<>();
                    for (ReviewRecord record : response.getRecordsList()) {
                        records.add(new ReviewRecordInfo(record));
                    }

                    LOG.info("Retrieved " + records.size() + " review records");
                    return records;
                }).get();
            } catch (Exception e) {
                LOG.error(
                    "Failed to list review records: " + e.getMessage(),
                    e
                );
                if (e.getCause() instanceof IntellicodeException) {
                    throw new RuntimeException(e.getCause());
                }
                throw new RuntimeException(
                    new IntellicodeException(
                        "Failed to list review records: " + e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    )
                );
            }
        });
    }

    /**
     * 获取指定审查的详细事件数据
     * @param repoPath 仓库路径
     * @param sessionId 会话ID
     * @return 审查事件信息
     */
    public CompletableFuture<ReviewEventInfo> getReviewRecordEvents(
        @NotNull String repoPath,
        @NotNull String sessionId
    ) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                LOG.info(
                    "Getting review events for repo: " +
                    repoPath +
                    ", session: " +
                    sessionId
                );

                // 构建查询参数
                java.util.Map<String, String> params =
                    new java.util.HashMap<>();
                params.put("repo_path", repoPath);
                params.put("session_id", sessionId);

                String url = buildGetRequestUrl("/review/events", params);
                LOG.debug("GET URL: " + url);

                return executeGetRequest(url, json -> {
                    // 使用 protobuf JsonFormat 解析响应
                    GetReviewRecordEventsResponse.Builder builder =
                        GetReviewRecordEventsResponse.newBuilder();
                    JsonFormat.parser()
                        .ignoringUnknownFields()
                        .merge(json, builder);
                    GetReviewRecordEventsResponse response = builder.build();

                    ReviewEventInfo eventInfo = new ReviewEventInfo(response);
                    LOG.info(
                        "Retrieved " +
                        eventInfo.events.size() +
                        " events for session: " +
                        sessionId
                    );
                    return eventInfo;
                }).get();
            } catch (Exception e) {
                LOG.error("Failed to get review events: " + e.getMessage(), e);
                if (e.getCause() instanceof IntellicodeException) {
                    throw new RuntimeException(e.getCause());
                }
                throw new RuntimeException(
                    new IntellicodeException(
                        "Failed to get review events: " + e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    )
                );
            }
        });
    }

    /**
     * 获取审查建议和反馈状态
     * @param reviewId 审查记录ID
     * @return 审查建议列表
     */
    public CompletableFuture<
        List<ReviewSuggestionInfo>
    > getReviewRecordSuggestions(@NotNull String reviewId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                LOG.info(
                    "Getting review suggestions for review ID: " + reviewId
                );

                // 构建查询参数
                java.util.Map<String, String> params =
                    new java.util.HashMap<>();
                params.put("review_id", reviewId);

                String url = buildGetRequestUrl("/review/suggestions", params);
                LOG.debug("GET URL: " + url);

                return executeGetRequest(url, json -> {
                    // 使用 protobuf JsonFormat 解析响应
                    GetReviewRecordSuggestionsResponse.Builder builder =
                        GetReviewRecordSuggestionsResponse.newBuilder();
                    JsonFormat.parser()
                        .ignoringUnknownFields()
                        .merge(json, builder);
                    GetReviewRecordSuggestionsResponse response =
                        builder.build();

                    // 转换为便于使用的数据结构
                    List<ReviewSuggestionInfo> suggestions =
                        new java.util.ArrayList<>();
                    for (ReviewSuggestion suggestion : response.getSuggestionsList()) {
                        suggestions.add(new ReviewSuggestionInfo(suggestion));
                    }

                    LOG.info(
                        "Retrieved " +
                        suggestions.size() +
                        " suggestions for review: " +
                        reviewId
                    );
                    return suggestions;
                }).get();
            } catch (Exception e) {
                LOG.error(
                    "Failed to get review suggestions: " + e.getMessage(),
                    e
                );
                if (e.getCause() instanceof IntellicodeException) {
                    throw new RuntimeException(e.getCause());
                }
                throw new RuntimeException(
                    new IntellicodeException(
                        "Failed to get review suggestions: " + e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    )
                );
            }
        });
    }

    /**
     * 创建用户对建议的反馈
     * @param feedbackRequest 反馈请求
     * @return 是否成功
     */
    public CompletableFuture<Boolean> createSuggestionFeedback(
        @NotNull SuggestionFeedbackRequest feedbackRequest
    ) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                LOG.info(
                    "Creating suggestion feedback for issue: " +
                    feedbackRequest.intellicodeIssueId +
                    ", review: " +
                    feedbackRequest.intellicodeReviewId
                );

                // 构建请求JSON（不包含认证信息）
                String requestJson = buildSuggestionFeedbackJson(
                    feedbackRequest
                );
                LOG.debug("Request JSON: " + requestJson);

                String url = baseUrl + "/review/feedback";

                return executePostRequest(url, requestJson, json -> {
                    // 使用 protobuf JsonFormat 解析响应
                    CreateSuggestionFeedBackResponse.Builder builder =
                        CreateSuggestionFeedBackResponse.newBuilder();
                    JsonFormat.parser()
                        .ignoringUnknownFields()
                        .merge(json, builder);
                    CreateSuggestionFeedBackResponse response = builder.build();

                    boolean success = response.getCode() == 0; // 假设0表示成功
                    LOG.info(
                        "Suggestion feedback result: " +
                        (success ? "success" : "failed") +
                        ", message: " +
                        response.getMsg()
                    );
                    return success;
                }).get();
            } catch (Exception e) {
                LOG.error(
                    "Failed to create suggestion feedback: " + e.getMessage(),
                    e
                );
                if (e.getCause() instanceof IntellicodeException) {
                    throw new RuntimeException(e.getCause());
                }
                throw new RuntimeException(
                    new IntellicodeException(
                        "Failed to create suggestion feedback: " +
                        e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    )
                );
            }
        });
    }

    /**
     * 构建建议反馈请求JSON（不包含认证信息）
     */
    private String buildSuggestionFeedbackJson(
        SuggestionFeedbackRequest feedbackRequest
    ) {
        try {
            // 构建 CreateSuggestionFeedBackRequest（不包含BaseReq）
            CreateSuggestionFeedBackRequest.Builder requestBuilder =
                CreateSuggestionFeedBackRequest.newBuilder()
                    .setIntellicodeIssueId(feedbackRequest.intellicodeIssueId)
                    .setIntellicodeReviewId(feedbackRequest.intellicodeReviewId)
                    .setResult(feedbackRequest.result);

            if (feedbackRequest.reason != null) {
                requestBuilder.setReason(feedbackRequest.reason);
            }
            if (feedbackRequest.otherReason != null) {
                requestBuilder.setOtherReason(feedbackRequest.otherReason);
            }

            CreateSuggestionFeedBackRequest request = requestBuilder.build();

            // 手动构建 JSON（不包含base_req字段）
            StringBuilder jsonBuilder = new StringBuilder();
            jsonBuilder.append("{");

            // intellicode_issue_id 字段
            jsonBuilder
                .append("\"intellicode_issue_id\":")
                .append(request.getIntellicodeIssueId())
                .append(",");

            // intellicode_review_id 字段
            jsonBuilder
                .append("\"intellicode_review_id\":")
                .append(request.getIntellicodeReviewId())
                .append(",");

            // result 字段（使用枚举数值）
            jsonBuilder
                .append("\"result\":")
                .append(request.getResult().getNumber())
                .append(",");

            // reason 字段
            jsonBuilder
                .append("\"reason\":\"")
                .append(escapeJsonString(request.getReason()))
                .append("\",");

            // other_reason 字段
            jsonBuilder
                .append("\"other_reason\":\"")
                .append(escapeJsonString(request.getOtherReason()))
                .append("\"");

            jsonBuilder.append("}");

            String jsonString = jsonBuilder.toString();
            LOG.debug("Built feedback request JSON: " + jsonString);
            return jsonString;
        } catch (Exception e) {
            LOG.error("Failed to build feedback request JSON", e);
            throw new RuntimeException("Failed to build request JSON", e);
        }
    }

    /**
     * 使用 protobuf 构建请求 JSON（不包含认证信息）
     */
    private String buildProtobufRequestJson(
        String repoPath,
        List<String> reviewFiles
    ) {
        try {
            // 构建 protobuf ReviewRequest（不包含BaseReq）
            intellicode.api.v1.Api.ReviewRequest.Builder requestBuilder =
                intellicode.api.v1.Api.ReviewRequest.newBuilder()
                    .setRepoPath(repoPath)
                    .setIde(IDE.IDE_JETBRAINS);

            // 添加审查文件列表
            for (String file : reviewFiles) {
                requestBuilder.addReviewFiles(file);
            }

            intellicode.api.v1.Api.ReviewRequest request =
                requestBuilder.build();

            // 手动构建 JSON（不包含base_req字段）
            StringBuilder jsonBuilder = new StringBuilder();
            jsonBuilder.append("{");

            // repo_path 字段
            jsonBuilder
                .append("\"repo_path\":\"")
                .append(escapeJsonString(request.getRepoPath()))
                .append("\",");

            // review_files 字段
            jsonBuilder.append("\"review_files\":[");
            for (int i = 0; i < request.getReviewFilesList().size(); i++) {
                if (i > 0) jsonBuilder.append(",");
                jsonBuilder
                    .append("\"")
                    .append(
                        escapeJsonString(request.getReviewFilesList().get(i))
                    )
                    .append("\"");
            }
            jsonBuilder.append("],");

            // ide 字段（使用枚举数值）
            jsonBuilder.append("\"ide\":").append(request.getIde().getNumber());

            jsonBuilder.append("}");

            String jsonString = jsonBuilder.toString();

            LOG.debug("Built protobuf request JSON: " + jsonString);
            return jsonString;
        } catch (Exception e) {
            LOG.error("Failed to build protobuf request JSON", e);
            throw new RuntimeException("Failed to build request JSON", e);
        }
    }

    /**
     * 转义JSON字符串中的特殊字符
     */
    private String escapeJsonString(String input) {
        if (input == null) {
            return "";
        }
        return input
            .replace("\\", "\\\\")
            .replace("\"", "\\\"")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
            .replace("\t", "\\t");
    }

    /**
     * 将IDE字符串转换为对应的枚举数值
     * 根据proto文件中的定义：IDE_UNSPECIFIED = 0, IDE_JETBRAINS = 1, IDE_VSCODE = 2
     */
    private int getIdeEnumValue(String ide) {
        if (ide == null) {
            return 0; // IDE_UNSPECIFIED
        }

        switch (ide.toUpperCase()) {
            case "IDE_JETBRAINS":
            case "JETBRAINS":
            case "INTELLIJ":
                return 1; // IDE_JETBRAINS
            case "IDE_VSCODE":
            case "VSCODE":
            case "VS_CODE":
                return 2; // IDE_VSCODE
            default:
                LOG.warn(
                    "Unknown IDE type: " +
                    ide +
                    ", using IDE_JETBRAINS as default"
                );
                return 1; // 默认使用 IDE_JETBRAINS
        }
    }

    /**
     * 获取认证 Token
     * 参考 CodeReviewAPI 的认证方式
     */
    @Nullable
    private String getAuthToken() {
        NocalhostSettings settings =
            ApplicationManager.getApplication().getService(
                NocalhostSettings.class
            );
        if (settings != null) {
            String token = settings.getUserToken();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(token)) {
                LOG.debug("Retrieved auth token from NocalhostSettings");
                return token;
            }
        }
        LOG.warn("No valid auth token found in settings");
        return null;
    }

    /**
     * 检查认证状态
     */
    public boolean isAuthenticated() {
        String token = getAuthToken();
        return org.apache.commons.lang3.StringUtils.isNotEmpty(token);
    }

    /**
     * 获取 API 配置信息
     */
    public ApiConfig getApiConfig() {
        return new ApiConfig(
            baseUrl,
            isAuthenticated(),
            DEFAULT_CONNECT_TIMEOUT,
            DEFAULT_READ_TIMEOUT
        );
    }

    /**
     * 构建 GET 请求 URL
     */
    private String buildGetRequestUrl(
        String endpoint,
        java.util.Map<String, String> params
    ) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl).append(endpoint);

        if (params != null && !params.isEmpty()) {
            urlBuilder.append("?");
            boolean first = true;
            for (java.util.Map.Entry<
                String,
                String
            > entry : params.entrySet()) {
                if (!first) {
                    urlBuilder.append("&");
                }
                try {
                    urlBuilder
                        .append(
                            java.net.URLEncoder.encode(entry.getKey(), "UTF-8")
                        )
                        .append("=")
                        .append(
                            java.net.URLEncoder.encode(
                                entry.getValue(),
                                "UTF-8"
                            )
                        );
                } catch (java.io.UnsupportedEncodingException e) {
                    LOG.warn(
                        "Failed to encode URL parameter: " +
                        entry.getKey() +
                        "=" +
                        entry.getValue()
                    );
                    urlBuilder
                        .append(entry.getKey())
                        .append("=")
                        .append(entry.getValue());
                }
                first = false;
            }
        }

        return urlBuilder.toString();
    }

    /**
     * 执行 GET 请求
     */
    private <T> CompletableFuture<T> executeGetRequest(
        String url,
        ResponseParser<T> parser
    ) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String authToken = getAuthToken();
                if (authToken == null) {
                    throw new IntellicodeException(
                        "请先登录后再使用 Intellicode 功能",
                        IntellicodeException.ErrorType.AUTHENTICATION_ERROR
                    );
                }

                Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("Cicd-Authorization", authToken)
                    .addHeader("User-Agent", "JetDev-Plugin/1.0")
                    .addHeader("Accept", "application/json")
                    .build();

                Response response = executeRequestWithRetry(request, 3);

                try (ResponseBody body = response.body()) {
                    if (body == null) {
                        throw new IntellicodeException(
                            "Empty response body",
                            IntellicodeException.ErrorType.SERVER_ERROR
                        );
                    }

                    String responseJson = body.string();
                    LOG.debug("GET response: " + responseJson);

                    return parser.parse(responseJson);
                }
            } catch (IntellicodeException e) {
                throw new RuntimeException(e);
            } catch (Exception e) {
                throw new RuntimeException(
                    new IntellicodeException(
                        "GET request failed: " + e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    )
                );
            }
        });
    }

    /**
     * 执行 POST 请求
     */
    private <T> CompletableFuture<T> executePostRequest(
        String url,
        String requestJson,
        ResponseParser<T> parser
    ) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String authToken = getAuthToken();
                if (authToken == null) {
                    throw new IntellicodeException(
                        "请先登录后再使用 Intellicode 功能",
                        IntellicodeException.ErrorType.AUTHENTICATION_ERROR
                    );
                }

                RequestBody body = RequestBody.create(
                    requestJson,
                    MediaType.get("application/json; charset=utf-8")
                );

                Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Cicd-Authorization", authToken)
                    .addHeader("User-Agent", "JetDev-Plugin/1.0")
                    .addHeader("Accept", "application/json")
                    .addHeader("Content-Type", "application/json")
                    .build();

                Response response = executeRequestWithRetry(request, 3);

                try (ResponseBody responseBody = response.body()) {
                    if (responseBody == null) {
                        throw new IntellicodeException(
                            "Empty response body",
                            IntellicodeException.ErrorType.SERVER_ERROR
                        );
                    }

                    String responseJson = responseBody.string();
                    LOG.debug("POST response: " + responseJson);

                    return parser.parse(responseJson);
                }
            } catch (IntellicodeException e) {
                throw new RuntimeException(e);
            } catch (Exception e) {
                throw new RuntimeException(
                    new IntellicodeException(
                        "POST request failed: " + e.getMessage(),
                        IntellicodeException.ErrorType.NETWORK_ERROR,
                        e
                    )
                );
            }
        });
    }

    /**
     * 响应解析器接口
     */
    @FunctionalInterface
    private interface ResponseParser<T> {
        T parse(String json) throws Exception;
    }

    /**
     * API 配置信息
     */
    public static class ApiConfig {

        public final String baseUrl;
        public final boolean authenticated;
        public final int connectTimeout;
        public final int readTimeout;

        public ApiConfig(
            String baseUrl,
            boolean authenticated,
            int connectTimeout,
            int readTimeout
        ) {
            this.baseUrl = baseUrl;
            this.authenticated = authenticated;
            this.connectTimeout = connectTimeout;
            this.readTimeout = readTimeout;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public boolean isAuthenticated() {
            return authenticated;
        }

        public int getConnectTimeout() {
            return connectTimeout;
        }

        public int getReadTimeout() {
            return readTimeout;
        }

        public int getTimeoutSeconds() {
            return readTimeout; // 返回读取超时作为主要超时时间
        }
    }

    /**
     * 请求数据结构
     */
    private static class ReviewRequest {

        final String token;
        final String repoPath;
        final List<String> reviewFiles;
        final String ide;

        ReviewRequest(
            String token,
            String repoPath,
            List<String> reviewFiles,
            String ide
        ) {
            this.token = token;
            this.repoPath = repoPath;
            this.reviewFiles = reviewFiles;
            this.ide = ide;
        }
    }

    /**
     * 审查记录信息
     */
    public static class ReviewRecordInfo {

        public final int reviewId;
        public final String sessionId;
        public final String repoPath;
        public final ReviewStatus status;
        public final String triggerBy;
        public final String email;
        public final long createdAt;

        public ReviewRecordInfo(ReviewRecord record) {
            this.reviewId = record.getReviewId();
            this.sessionId = record.getSessionId();
            this.repoPath = record.getRepoPath();
            this.status = record.getStatus();
            this.triggerBy = record.getTriggerBy();
            this.email = record.getEmail();
            this.createdAt = record.getCreatedAt();
        }

        public ReviewRecordInfo(
            int reviewId,
            String sessionId,
            String repoPath,
            ReviewStatus status,
            String triggerBy,
            String email,
            long createdAt
        ) {
            this.reviewId = reviewId;
            this.sessionId = sessionId;
            this.repoPath = repoPath;
            this.status = status;
            this.triggerBy = triggerBy;
            this.email = email;
            this.createdAt = createdAt;
        }
    }

    /**
     * 审查事件信息
     */
    public static class ReviewEventInfo {

        public final List<Event> events;
        public final String sessionId;
        public final String repoPath;
        public final long createdAt;
        public final List<String> reviewFiles;

        public ReviewEventInfo(GetReviewRecordEventsResponse response) {
            this.events = response.getEventsList();
            this.sessionId = response.getSessionId();
            this.repoPath = response.getRepoPath();
            this.createdAt = response.getCreatedAt();
            this.reviewFiles = response.getReviewFilesList();
        }

        public ReviewEventInfo(
            List<Event> events,
            String sessionId,
            String repoPath,
            long createdAt,
            List<String> reviewFiles
        ) {
            this.events = events;
            this.sessionId = sessionId;
            this.repoPath = repoPath;
            this.createdAt = createdAt;
            this.reviewFiles = reviewFiles;
        }
    }

    /**
     * 审查建议信息
     */
    public static class ReviewSuggestionInfo {

        public final int id;
        public final int intellicodeReviewId;
        public final String filePath;
        public final int startLineNumber;
        public final int endLineNumber;
        public final String level;
        public final String originalCodeSnippet;
        public final String suggestedCodeSnippet;
        public final String suggestionCategory;
        public final String suggestionDescription;
        public final SuggestionStatus status;
        public final FeedbackAction action;

        public ReviewSuggestionInfo(ReviewSuggestion suggestion) {
            this.id = suggestion.getId();
            this.intellicodeReviewId = suggestion.getIntellicodeReviewId();
            this.filePath = suggestion.getFilePath();
            this.startLineNumber = suggestion.getStartLineNumber();
            this.endLineNumber = suggestion.getEndLineNumber();
            this.level = suggestion.getLevel();
            this.originalCodeSnippet = suggestion.getOriginalCodeSnippet();
            this.suggestedCodeSnippet = suggestion.getSuggestedCodeSnippet();
            this.suggestionCategory = suggestion.getSuggestionCategory();
            this.suggestionDescription = suggestion.getSuggestionDescription();
            this.status = suggestion.getStatus();
            this.action = suggestion.getAction();
        }
    }

    /**
     * 建议反馈请求
     */
    public static class SuggestionFeedbackRequest {

        public final int intellicodeIssueId;
        public final int intellicodeReviewId;
        public final FeedbackAction result;
        public final String reason;
        public final String otherReason;

        public SuggestionFeedbackRequest(
            int intellicodeIssueId,
            int intellicodeReviewId,
            FeedbackAction result,
            String reason,
            String otherReason
        ) {
            this.intellicodeIssueId = intellicodeIssueId;
            this.intellicodeReviewId = intellicodeReviewId;
            this.result = result;
            this.reason = reason;
            this.otherReason = otherReason;
        }
    }
}
