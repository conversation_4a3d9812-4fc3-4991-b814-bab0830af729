package com.sentinel.nocalhost.intellicode.api;

import intellicode.event.v1.EventOuterClass.Suggestion;
import java.util.List;

/**
 * Intellicode SSE 事件监听器接口
 * 定义处理各种 SSE 事件的回调方法
 */
public interface IntellicodeSSEListener {
    
    /**
     * 会话开始事件
     * @param sessionId 会话 ID
     * @param reviewFiles 审查文件列表
     */
    void onSessionStart(String sessionId, List<String> reviewFiles);
    
    /**
     * 会话结束事件
     * @param sessionId 会话 ID
     * @param success 是否成功完成
     */
    void onSessionEnd(String sessionId, boolean success);
    
    /**
     * 工具开始执行事件
     * @param toolName 工具名称
     * @param input 输入参数
     */
    void onToolStart(String toolName, String input);
    
    /**
     * 工具执行完成事件
     * @param toolName 工具名称
     * @param output 执行结果
     * @param success 是否执行成功
     */
    void onToolEnd(String toolName, String output, boolean success);
    
    /**
     * LLM 助手消息事件
     * @param chunk Markdown 内容片段
     */
    void onAssistantMessage(String chunk);
    
    /**
     * 修复建议事件
     * @param suggestions 建议列表
     */
    void onSuggestions(List<Suggestion> suggestions);
    
    /**
     * 错误事件
     * @param error 错误信息
     */
    void onError(Throwable error);
}