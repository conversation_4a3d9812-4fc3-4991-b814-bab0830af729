package com.sentinel.nocalhost.intellicode.service;

import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.openapi.vfs.VirtualFileManager;
import com.intellij.openapi.project.ProjectUtil;
import com.intellij.dvcs.DvcsUtil;
import com.intellij.openapi.vcs.changes.Change;
import com.intellij.openapi.vcs.changes.ChangeListManager;
import com.intellij.openapi.vcs.FilePath;
import com.intellij.openapi.vcs.ProjectLevelVcsManager;
import com.intellij.openapi.vcs.VcsRoot;
import com.intellij.vcs.log.VcsCommitMetadata;
import git4idea.GitCommit;
import git4idea.GitUtil;
import git4idea.GitVcs;
import git4idea.history.GitHistoryUtils;
import git4idea.repo.GitRepository;
import git4idea.repo.GitRepositoryManager;
import git4idea.branch.GitBranchUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Intellicode Git 服务
 * 使用 IntelliJ IDEA Git4Idea API 提供可靠的Git操作
 */
public class IntellicodeGitService {
    private final Project project;
    
    /**
     * 最新提交信息数据类
     */
    public static class LatestCommitInfo {
        private final String branchName;
        private final String commitMessage;
        private final String commitHash;
        private final Date commitDate;
        private final List<String> files;
        
        public LatestCommitInfo(String branchName, String commitMessage, String commitHash, Date commitDate, List<String> files) {
            this.branchName = branchName;
            this.commitMessage = commitMessage;
            this.commitHash = commitHash;
            this.commitDate = commitDate;
            this.files = files;
        }
        
        public String getBranchName() { return branchName; }
        public String getCommitMessage() { return commitMessage; }
        public String getCommitHash() { return commitHash; }
        public Date getCommitDate() { return commitDate; }
        public List<String> getFiles() { return files; }
        
        public String getFormattedCommitDate() {
            if (commitDate != null) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return formatter.format(commitDate);
            }
            return "Unknown";
        }
        
        public String getShortCommitHash() {
            if (commitHash != null && commitHash.length() > 7) {
                return commitHash.substring(0, 7);
            }
            return commitHash;
        }
    }
    
    public IntellicodeGitService(@NotNull Project project) {
        this.project = project;
    }
    
    /**
     * 获取 Git 仓库（动态获取，增强版）
     */
    @Nullable
    private GitRepository getGitRepository() {
        try {
            // 1. 首先尝试从 GitRepositoryManager 获取
            GitRepositoryManager repositoryManager = GitRepositoryManager.getInstance(project);
            List<GitRepository> repositories = repositoryManager.getRepositories();
            
            if (!repositories.isEmpty()) {
                return repositories.get(0);
            }
            
            // 2. 检查 VCS 是否已配置
            ProjectLevelVcsManager vcsManager = ProjectLevelVcsManager.getInstance(project);
            if (!vcsManager.hasActiveVcss()) {
                return null;
            }
            
            // 3. 尝试通过项目根目录获取仓库
            VirtualFile projectRoot = project.getBaseDir();
            if (projectRoot != null) {
                GitRepository repo = repositoryManager.getRepositoryForRoot(projectRoot);
                if (repo != null) {
                    return repo;
                }
            }
            
            // 4. 遍历 VCS 根目录查找 Git 仓库
            VcsRoot[] vcsRoots = vcsManager.getAllVcsRoots();
            
            for (VcsRoot vcsRoot : vcsRoots) {
                if (vcsRoot.getVcs() instanceof GitVcs) {
                    VirtualFile root = vcsRoot.getPath();
                    GitRepository repo = repositoryManager.getRepositoryForRoot(root);
                    if (repo != null) {
                        return repo;
                    }
                }
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
        
        return null;
    }
    
    /**
     * 检查是否为Git仓库
     */
    public boolean isGitRepository() {
        GitRepository gitRepository = getGitRepository();
        return gitRepository != null;
    }
    
    /**
     * 检查是否有历史提交
     */
    public boolean hasCommitHistory() {
        GitRepository gitRepository = getGitRepository();
        if (gitRepository == null) {
            return false;
        }
        
        try {
            // 检查当前分支是否有提交
            return gitRepository.getCurrentRevision() != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取git status文件列表
     * 包括所有修改、新增、删除、重命名等状态的文件，自动展开未跟踪目录中的文件
     */
    public List<String> getUncommittedFiles() {
        List<String> files = new ArrayList<>();
        GitRepository gitRepository = getGitRepository();
        if (gitRepository == null) {
            return files;
        }
        
        try {
            ChangeListManager changeListManager = ChangeListManager.getInstance(project);
            VirtualFile projectRoot = gitRepository.getRoot();
            
            // 获取所有变更（已修改、已暂存等）
            Collection<Change> changes = changeListManager.getAllChanges();
            for (Change change : changes) {
                FilePath filePath = null;
                if (change.getAfterRevision() != null) {
                    filePath = change.getAfterRevision().getFile();
                } else if (change.getBeforeRevision() != null) {
                    filePath = change.getBeforeRevision().getFile();
                }
                
                if (filePath != null) {
                    String relativePath = getRelativePathFromString(projectRoot.getPath(), filePath.getPath());
                    if (relativePath != null && !files.contains(relativePath)) {
                        files.add(relativePath);
                    }
                }
            }
            
            // 获取所有未跟踪文件（自动展开目录）
            List<FilePath> unversionedFilePaths = changeListManager.getUnversionedFilesPaths();
            for (FilePath filePath : unversionedFilePaths) {
                String relativePath = getRelativePathFromString(projectRoot.getPath(), filePath.getPath());
                if (relativePath != null && !files.contains(relativePath)) {
                    files.add(relativePath);
                }
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
        
        // 如果 API 方式没有返回结果，尝试使用命令行作为备用方案
        if (files.isEmpty()) {
            return getUncommittedFilesViaCommand(gitRepository);
        }
        
        return files;
    }
    
    /**
     * 获取文件相对于项目根目录的路径
     */
    private String getRelativePath(VirtualFile projectRoot, VirtualFile file) {
        if (file == null || projectRoot == null) {
            return null;
        }
        
        return getRelativePathFromString(projectRoot.getPath(), file.getPath());
    }
    
    /**
     * 从字符串路径计算相对路径
     */
    private String getRelativePathFromString(String projectPath, String filePath) {
        if (filePath == null || projectPath == null) {
            return null;
        }
        
        if (filePath.startsWith(projectPath)) {
            String relativePath = filePath.substring(projectPath.length());
            // 移除开头的斜杠或反斜杠
            if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                relativePath = relativePath.substring(1);
            }
            return relativePath;
        }
        
        return null;
    }
    
    /**
     * 使用命令行方式获取未提交文件（备用方案）
     */
    private List<String> getUncommittedFilesViaCommand(GitRepository gitRepository) {
        List<String> files = new ArrayList<>();
        if (gitRepository == null) {
            return files;
        }
        
        try {
            VirtualFile projectRoot = gitRepository.getRoot();
            ProcessBuilder pb = new ProcessBuilder("git", "status", "--porcelain");
            pb.directory(new java.io.File(projectRoot.getPath()));
            Process process = pb.start();
            
            java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.length() >= 3) {
                    String filePath = line.substring(3).trim();
                    
                    // 处理重命名情况 (格式: "old_name -> new_name")
                    if (filePath.contains(" -> ")) {
                        String[] parts = filePath.split(" -> ");
                        if (parts.length == 2) {
                            filePath = parts[1]; // 使用新文件名
                        }
                    }
                    
                    if (!filePath.isEmpty()) {
                        files.add(filePath);
                    }
                }
            }
            
            process.waitFor();
        } catch (Exception e) {
            // 忽略异常
        }
        
        return files;
    }
    
    /**
     * 获取最近一次commit的文件列表
     */
    public List<String> getLatestCommitFiles() {
        List<String> files = new ArrayList<>();
        GitRepository gitRepository = getGitRepository();
        if (gitRepository == null) {
            return files;
        }
        
        try {
            VirtualFile root = gitRepository.getRoot();
            List<GitCommit> commits = GitHistoryUtils.history(project, root, "--max-count=1");
            
            if (!commits.isEmpty()) {
                GitCommit latestCommit = commits.get(0);
                Collection<FilePath> affectedPaths = latestCommit.getAffectedPaths();
                
                for (FilePath filePath : affectedPaths) {
                    String relativePath = getRelativePath(root, filePath.getVirtualFile());
                    if (relativePath != null) {
                        files.add(relativePath);
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        return files;
    }
    
    /**
     * 获取最新提交的完整信息
     */
    public LatestCommitInfo getLatestCommitInfo() {
        String branchName = getCurrentBranch();
        String commitMessage = "No commits found";
        String commitHash = "";
        Date commitDate = null;
        List<String> files = getLatestCommitFiles();
        
        GitRepository gitRepository = getGitRepository();
        if (gitRepository == null) {
            return new LatestCommitInfo(branchName, commitMessage, commitHash, commitDate, files);
        }
        
        try {
            VirtualFile root = gitRepository.getRoot();
            List<GitCommit> commits = GitHistoryUtils.history(project, root, "--max-count=1");
            
            if (!commits.isEmpty()) {
                GitCommit latestCommit = commits.get(0);
                commitHash = latestCommit.getId().toString();
                commitMessage = latestCommit.getFullMessage();
                commitDate = new Date(latestCommit.getCommitTime());
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        return new LatestCommitInfo(branchName, commitMessage, commitHash, commitDate, files);
    }
    
    /**
     * 获取当前分支名称
     */
    private String getCurrentBranch() {
        GitRepository gitRepository = getGitRepository();
        if (gitRepository == null) {
            return "Unknown";
        }
        
        try {
            String branchName = GitBranchUtil.getBranchNameOrRev(gitRepository);
            return branchName != null ? branchName : "Unknown";
        } catch (Exception e) {
            return "Unknown";
        }
    }
    
    /**
     * 在编辑器中打开指定文件
     */
    public void openFileInEditor(String filePath) {
        GitRepository gitRepository = getGitRepository();
        if (gitRepository == null) {
            return;
        }
        
        try {
            VirtualFile projectRoot = gitRepository.getRoot();
            VirtualFile file = projectRoot.findFileByRelativePath(filePath);
            
            if (file != null) {
                FileEditorManager.getInstance(project).openFile(file, true);
            } else {
                // 尝试绝对路径
                VirtualFile absoluteFile = VirtualFileManager.getInstance().findFileByUrl("file://" + filePath);
                if (absoluteFile != null) {
                    FileEditorManager.getInstance(project).openFile(absoluteFile, true);
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }
}