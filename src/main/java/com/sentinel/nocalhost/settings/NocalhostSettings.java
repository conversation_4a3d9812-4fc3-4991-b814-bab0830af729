package com.sentinel.nocalhost.settings;

import com.intellij.openapi.components.*;
import com.intellij.util.xmlb.XmlSerializerUtil;
import lombok.Getter;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;

@State(
        name = "NocalhostSettings",
        storages = {@Storage(value = StoragePathMacros.NON_ROAMABLE_FILE, roamingType = RoamingType.DISABLED)}
)
@Getter
@Setter
@Service(Service.Level.APP)
public final class NocalhostSettings implements PersistentStateComponent<NocalhostSettings> {

    private String userToken; // cicd token

    private String intelliforgeToken; // 神机token

    @Getter
    @Setter
    private String selectedModel;


    public synchronized String getIntelliforgeToken() {
        return intelliforgeToken;
    }

    public synchronized String getUserToken() {
        return userToken;
    }

    public synchronized void cleanUserToken() {
        userToken = "";
        intelliforgeToken = "";
    }

    @Override
    public @NotNull NocalhostSettings getState() {
        return this;
    }

    @Override
    public void loadState(@NotNull NocalhostSettings state) {
        XmlSerializerUtil.copyBean(state, this);
    }
}
