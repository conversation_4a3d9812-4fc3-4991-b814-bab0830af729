package com.sentinel.nocalhost;

import com.intellij.openapi.diagnostic.Logger;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class Config {
    private static final Logger LOG = Logger.getInstance(Config.class);
    private static final Properties properties = new Properties();

    static {
        try (InputStream input = Config.class.getClassLoader().getResourceAsStream("config.properties")) {
            if (input == null) {
                LOG.error("Sorry, unable to find config.properties");
            }
            properties.load(input);
        } catch (IOException e) {
            LOG.error(e);
        }
    }

    public static String getProperty(String key) {
        return properties.getProperty(key);
    }
}
