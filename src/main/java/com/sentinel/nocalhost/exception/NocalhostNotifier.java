package com.sentinel.nocalhost.exception;

import com.intellij.notification.*;
import com.intellij.openapi.extensions.PluginId;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.NlsContexts;
import com.intellij.openapi.wm.ToolWindowManager;
import com.sentinel.nocalhost.topic.NocalhostExceptionPrintNotifier;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.event.HyperlinkEvent;
import java.util.Objects;

public class NocalhostNotifier {

    public static final String NOCALHOST_NOTIFICATION_ID = "Nocalhost.Notification";
    public static final String NOCALHOST_ERROR_NOTIFICATION_ID = "Nocalhost.Notification.Error";

    public static final NotificationGroup NOCALHOST_NOTIFICATION =
            NotificationGroup.create(
                    "Nocalhost.Notification", NotificationDisplayType.BALLOON, false, "JetDev",
                    "JetDev", PluginId.getId("com.sentinel.JetDev"));
    public static final NotificationGroup NOCALHOST_ERROR_NOTIFICATION =
            NotificationGroup.create(
                    "Nocalhost.Notification.Error", NotificationDisplayType.STICKY_BALLOON, true, "JetDev",
                    "JetDev", PluginId.getId("com.sentinel.JetDev"));

    private final Project project;

    public NocalhostNotifier(Project project) {
        this.project = project;
    }

    public static NocalhostNotifier getInstance(Project project) {
        return project.getService(NocalhostNotifier.class);
    }

    public void notify(@NotNull Notification notification) {
        notification.notify(project);
    }

    public void notifyError(@NlsContexts.NotificationTitle @NotNull String title,
                            @NlsContexts.NotificationContent @NotNull String message) {
        notify(NOCALHOST_ERROR_NOTIFICATION, NOCALHOST_ERROR_NOTIFICATION_ID, title, message, NotificationType.ERROR, null);
    }

    public void notifyError(@NlsContexts.NotificationTitle @NotNull String title,
                            @NlsContexts.NotificationContent @NotNull String message,
                            @NotNull String eMessage) {
        String content = String.format("<html>%s <a href=\"nocalhost.show\">Show More</a></html>", message);
        notify(NOCALHOST_ERROR_NOTIFICATION, NOCALHOST_ERROR_NOTIFICATION_ID, title, content, NotificationType.ERROR, new NotificationListener.Adapter() {
            @Override
            protected void hyperlinkActivated(@NotNull Notification notification, @NotNull HyperlinkEvent e) {
                Objects.requireNonNull(ToolWindowManager.getInstance(project).getToolWindow("JetDev Console")).activate(() -> project.getMessageBus().syncPublisher(NocalhostExceptionPrintNotifier.NOCALHOST_EXCEPTION_PRINT_NOTIFIER_TOPIC)
                        .action(title, message, eMessage));
            }
        });
    }

    public void notifySuccess(@NlsContexts.NotificationTitle @NotNull String title,
                              @NlsContexts.NotificationContent @NotNull String message) {
        notify(NOCALHOST_NOTIFICATION, NOCALHOST_NOTIFICATION_ID, title, message, NotificationType.INFORMATION, null);
    }

    private void notify(@NotNull NotificationGroup notificationGroup,
                        @NonNls @Nullable String displayId,
                        @NlsContexts.NotificationTitle @NotNull String title,
                        @NlsContexts.NotificationContent @NotNull String message,
                        @NotNull NotificationType type,
                        @Nullable NotificationListener listener) {
        Notification notification = createNotification(notificationGroup, displayId, title, message, type, listener);
        notify(notification);
    }

    private static Notification createNotification(@NotNull NotificationGroup notificationGroup,
                                                   @NonNls @Nullable String displayId,
                                                   @NlsContexts.NotificationTitle @NotNull String title,
                                                   @NlsContexts.NotificationContent @NotNull String message,
                                                   @NotNull NotificationType type,
                                                   @Nullable NotificationListener listener) {

        if (StringUtils.isBlank(message)) {
            message = title;
            title = "";
        }
        Notification notification = notificationGroup.createNotification(title, message, type);
        if (listener != null) {
            notification.setListener(listener);
        }
        notification.setDisplayId(StringUtils.trimToEmpty(displayId));
        return notification;
    }
}
