package com.sentinel.nocalhost.ui.action.toolbar;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;


public class BackHomeAction extends DumbAwareAction {
    private final Project project;

    public BackHomeAction(Project project) {
        super("历史记录", StringUtils.EMPTY, AllIcons.Vcs.History);
        this.project =project;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {

        CodeReviewTaskManager codeReviewTaskManager = CodeReviewTaskManager.getInstance(project);
        if (null != codeReviewTaskManager) {
            codeReviewTaskManager.set(null);
        }

        ApplicationManager.getApplication().invokeLater(
                () -> ApplicationManager.getApplication().getMessageBus()
                        .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refresh());
    }
}
