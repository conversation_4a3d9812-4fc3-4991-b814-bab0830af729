package com.sentinel.nocalhost.ui.action.toolbar;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.DumbAwareAction;

import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;


public class RefreshAction extends DumbAwareAction {

    public RefreshAction() {
        super("刷新列表", StringUtils.EMPTY, AllIcons.Actions.Refresh);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        ApplicationManager.getApplication().getMessageBus().syncPublisher(
                NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC).action(Constants.HUMAN_OPERATE, null);
    }
}
