package com.sentinel.nocalhost.ui.action.contextmenu;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.task.RemoteStopTask;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import org.jetbrains.annotations.NotNull;


public class RemoteStopAction extends BaseAction {

    public RemoteStopAction(Project project, UserAppNode userAppNode) {
        super(project, userAppNode,"Stop", AllIcons.Actions.Suspend);

    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        if (checkUserAppStatus()) {
            String title = "Ending: " + userAppNode.getUserApp().getName() + "/" + userAppNode.getUserApp().getCluster();
            ProgressManager.getInstance().run(new RemoteStopTask(project, title, userAppNode));
        }
    }

}
