package com.sentinel.nocalhost.ui.action.toolbar;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.ui.dialog.TrafficMarkDialog;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;


public class SetTrafficMarkAction extends DumbAwareAction {

    private final Project project;

    public SetTrafficMarkAction(Project project) {
        super("设置流量标记", StringUtils.EMPTY, AllIcons.General.Settings);
        this.project = project;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        TrafficMarkDialog toggleMarkDialog =  new TrafficMarkDialog(project);
        toggleMarkDialog.show();
    }
}
