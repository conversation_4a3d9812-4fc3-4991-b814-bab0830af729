package com.sentinel.nocalhost.ui.action.contextmenu;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.task.ExecutionTask;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import org.jetbrains.annotations.NotNull;

public class RemoteDebugAction extends BaseAction {

    public RemoteDebugAction(Project project, UserAppNode userAppNode) {
        super(project,userAppNode, "Remote Debug",  AllIcons.Actions.StartDebugger);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        if (checkUserAppStatus() && checkWorkSpaceDir()) {
            ProgressManager.getInstance().run(new ExecutionTask(project, ExecutionTask.kDebug, userAppNode));
        }
    }

}
