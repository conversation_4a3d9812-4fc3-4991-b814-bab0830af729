package com.sentinel.nocalhost.ui.action.contextmenu;

import com.google.common.collect.Lists;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.Config;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import com.sentinel.nocalhost.utils.*;
import org.jetbrains.annotations.NotNull;
import com.sentinel.nocalhost.constants.Constants;

public class TerminalAction extends BaseAction {

    public TerminalAction(Project project, UserAppNode userAppNode) {
        super(project, userAppNode, "Remote Terminal", AllIcons.Debugger.Console);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent event) {
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                if (Constants.DEBUG_STATUS_SUCCESSFUL.equals(userAppNode.getUserApp().getStatus())) {
                    openDevTerminal();
                }
            } catch (Exception e) {
                ErrorUtil.dealWith(project, "Loading service status error",
                        "Error occurs while loading service status", e);
            }
        });
    }

    private void openDevTerminal() {
        ApplicationManager.getApplication().invokeLater(() ->
                TerminalUtil.openTerminal(
                        project,
                        String.format(
                                "%s %s",
                                userAppNode.getUserApp().getName(),
                                "Dev Terminal"
                        ),
                        new GeneralCommandLine(Lists.newArrayList(
                                PathsUtil.backslash(NhctlUtil.binaryPath()),
                                "cicd",
                                "terminal",
                                "--app", String.valueOf(userAppNode.getUserApp().getId()),
                                "--cluster", userAppNode.getUserApp().getCluster(),
                                "--namespace", userAppNode.getUserApp().getNamespace(),
                                "--client=jetDev",
                                "--ide=" + Tools.getIdeaName(),
                                "--pluginVersion="+ Config.getProperty("report_version")
                        ))
                )
        );
    }

}
