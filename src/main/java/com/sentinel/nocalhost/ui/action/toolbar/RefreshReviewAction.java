package com.sentinel.nocalhost.ui.action.toolbar;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;


public class RefreshReviewAction extends DumbAwareAction {
    private final Project project;

    public RefreshReviewAction(Project project) {
        super("刷新内容", StringUtils.EMPTY, AllIcons.Actions.Refresh);
        this.project = project;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {

        ProgressManager.getInstance().run(new Task.Backgroundable(project, "Refreshing", false) {
            @Override
            public void run(@NotNull ProgressIndicator indicator) {
                indicator.setText("Refreshing");
                CodeReviewTaskManager codeReviewTaskManager = CodeReviewTaskManager.getInstance(project);
                if (null == codeReviewTaskManager || null == codeReviewTaskManager.get()) {

                    ApplicationManager.getApplication().invokeLater(
                            () -> ApplicationManager.getApplication().getMessageBus()
                                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refresh());
                } else {
                    ApplicationManager.getApplication().invokeLater(
                            () -> ApplicationManager.getApplication().getMessageBus()
                                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC)
                                    .refreshSuggestionPanel(-1));
                }
                try {
                    Thread.sleep(500L);
                } catch (InterruptedException ignored) {
                }
                indicator.cancel();
                indicator.stop();
            }
        });


    }

}
