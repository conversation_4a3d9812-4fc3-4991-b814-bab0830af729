package com.sentinel.nocalhost.ui.action.toolbar;

import com.intellij.notification.NotificationType;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.util.ui.UIUtil;
import com.sentinel.nocalhost.Config;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.exception.NocalhostLoginException;
import com.sentinel.nocalhost.service.NocalhostBinService;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import com.sentinel.nocalhost.topic.RefreshToolWindowNotifier;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.utils.NhctlUtil;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

import static icons.NocalhostIcons.Login;
import static icons.NocalhostIcons.LoginDark;


public class LoginAccountsAction extends DumbAwareAction implements Disposable {
    private final NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);

    private final NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);

    private HttpServer httpServer;
    private static boolean serverRunning = false;
    private final Project project;

    public LoginAccountsAction(Project project) {
        super("登录", StringUtils.EMPTY, UIUtil.isUnderDarcula() ? Login : LoginDark);
        this.project = project;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        try {
            int serverPort = 33556;
            if (!serverRunning) {
                startHttpServer(serverPort);
            }
            openAuthenticationPage(serverPort);
        } catch (Exception ex) {
            ApplicationManager.getApplication().invokeLater(() ->
                    Messages.showErrorDialog(ex.getMessage(), Constants.LOGIN_Failed));
        }
    }

    private void openAuthenticationPage(int serverPort) throws URISyntaxException, IOException {
        String authUrl = Config.getProperty("login_url") + "?type=jb&callback=" + "http://localhost:" + serverPort + "/callback";
        java.awt.Desktop.getDesktop().browse(new URI(authUrl));
    }


    private class CallbackHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) {

            exchange.getResponseHeaders().add("Access-Control-Allow-Origin", "*");
            exchange.getResponseHeaders().add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            exchange.getResponseHeaders().add("Access-Control-Allow-Headers", "Content-Type");

            String requestMethod = exchange.getRequestMethod();
            try {
                if (requestMethod.equalsIgnoreCase("GET")
                        || requestMethod.equalsIgnoreCase("OPTIONS")) {

                    URI uri = exchange.getRequestURI();
                    String query = uri.getQuery();
                    Map<String, String> params = parseQuery(query);
                    String cicdToken = params.get("cicdToken");
                    String ifToken = params.get("ifToken");

                    if (StringUtils.isNotEmpty(cicdToken)) {
                        settings.getState().setUserToken(cicdToken);
                        settings.getState().setIntelliforgeToken(ifToken);

                        ApplicationManager.getApplication().invokeLater(
                                () -> ApplicationManager.getApplication().getMessageBus()
                                        .syncPublisher(RefreshToolWindowNotifier.REFRESH_TOOL_WINDOW_TOPIC).refresh());

                        ApplicationManager.getApplication().invokeLater(
                                () -> ApplicationManager.getApplication().getMessageBus()
                                        .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refresh());

                        Notifications.showNotification(project, Constants.LOGIN_NOTIFICATION_GROUP,
                                Constants.LOGIN_SUCCESS, StringUtils.EMPTY, NotificationType.INFORMATION);

                        updateJetDevCommandTool();

                    } else {
                        throw new NocalhostLoginException(Constants.TOKEN_IS_EMPTY);
                    }
                }
            } catch (Exception e) {
                ApplicationManager.getApplication().invokeLater(() ->
                        Messages.showErrorDialog(e.getMessage(), Constants.LOGIN_Failed));
            } finally {
                if (serverRunning) {
                    httpServer.stop(0);
                    serverRunning = false;
                }
            }
        }

        private void updateJetDevCommandTool() {
            ApplicationManager.getApplication().invokeLater(() -> {
                try {
                    if (StringUtils.isNotEmpty(settings.getUserToken())) {
                        NocalhostBinService nocalhostBinService = new NocalhostBinService();
                        nocalhostBinService.checkBin(project);
                        nocalhostBinService.checkVersion(project);
                        nhctlCommand.setToken(settings.getUserToken());
                    }
                } catch (Exception ignored) {
                }
            });
        }

        private Map<String, String> parseQuery(String query) {
            Map<String, String> parameters = new HashMap<>();
            if (query != null) {
                String[] pairs = query.split("&");
                for (String pair : pairs) {
                    int idx = pair.indexOf("=");
                    String key = idx > 0 ? pair.substring(0, idx) : pair;
                    String value = idx > 0 && pair.length() > idx + 1 ? pair.substring(idx + 1) : null;
                    parameters.put(key, value);
                }
            }
            return parameters;
        }
    }

    private void startHttpServer(int serverPort) throws IOException {
        httpServer = HttpServer.create(new InetSocketAddress(serverPort), 0);
        httpServer.createContext("/callback", new CallbackHandler());
        httpServer.setExecutor(null);
        httpServer.start();
        serverRunning = true;
    }

    @Override
    public void dispose() {
        if (serverRunning) {
            httpServer.stop(0);
            serverRunning = false;
        }
    }


}
