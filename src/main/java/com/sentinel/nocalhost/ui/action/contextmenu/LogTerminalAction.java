package com.sentinel.nocalhost.ui.action.contextmenu;

import com.google.common.collect.Lists;
import com.intellij.execution.configurations.GeneralCommandLine;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.Config;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import com.sentinel.nocalhost.utils.*;
import org.jetbrains.annotations.NotNull;

public class LogTerminalAction extends BaseAction {

    public LogTerminalAction(Project project, UserAppNode userAppNode) {
        super(project, userAppNode, "Log Terminal", AllIcons.Debugger.Console);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent event) {
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                openLogTerminal();
            } catch (Exception e) {
                ErrorUtil.dealWith(project, "open log terminal error",
                        "open log terminal error", e);
            }
        });
    }

    private void openLogTerminal() {
        ApplicationManager.getApplication().invokeLater(() ->
                TerminalUtil.openTerminal(
                        project,
                        String.format(
                                "%s %s",
                                userAppNode.getUserApp().getName(),
                                "Log Terminal"
                        ),
                        new GeneralCommandLine(Lists.newArrayList(
                                PathsUtil.backslash(NhctlUtil.binaryPath()),
                                "cicd",
                                "terminal",
                                "--action", "Enterlog",
                                "--app", String.valueOf(userAppNode.getUserApp().getId()),
                                "--cluster", userAppNode.getUserApp().getCluster(),
                                "--namespace", userAppNode.getUserApp().getNamespace(),
                                "--client=jetDev",
                                "--ide=" + Tools.getIdeaName(),
                                "--pluginVersion="+ Config.getProperty("report_version")
                        ))
                )
        );
    }

}
