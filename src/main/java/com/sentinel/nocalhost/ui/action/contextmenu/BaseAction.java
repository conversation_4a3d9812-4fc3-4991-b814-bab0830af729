package com.sentinel.nocalhost.ui.action.contextmenu;

import com.intellij.notification.NotificationType;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.IdeHubAPI;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.api.data.UserAppConfigs;
import com.sentinel.nocalhost.api.data.UserAppConfigsRequest;
import com.sentinel.nocalhost.api.data.UserProject;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostAppListManager;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;
import com.sentinel.nocalhost.ui.dialog.AppConfigDialog;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;

public class BaseAction extends DumbAwareAction {
    final IdeHubAPI ideHubAPI = ApplicationManager.getApplication().getService(IdeHubAPI.class);
    final Project project;
    final UserAppNode userAppNode;

    public BaseAction(Project project, UserAppNode userAppNode, String text, Icon icon) {
        super(text, "", icon);
        this.project = project;
        this.userAppNode = userAppNode;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {

    }

    UserAppConfigs getUserAppConfigs() {
        UserAppConfigsRequest appConfigsRequest = new UserAppConfigsRequest();
        appConfigsRequest.setAppId(userAppNode.getUserApp().getId());
        appConfigsRequest.setAppName(userAppNode.getUserApp().getName());
        appConfigsRequest.setCluster(userAppNode.getUserApp().getCluster());
        appConfigsRequest.setNamespace(userAppNode.getUserApp().getNamespace());
        return ideHubAPI.queryUserAppConfigs(project, appConfigsRequest);
    }


    public boolean checkWorkSpaceDir() {
        UserAppConfigs userAppConfigs = getUserAppConfigs();
        if (userAppConfigs != null) {
            if (StringUtils.isNotEmpty(userAppConfigs.getConfig().getLocalDir())) {

                String localDirPath = Paths.get(userAppConfigs.getConfig().getLocalDir()).normalize().toString();
                String basePathPath = Paths.get(Objects.requireNonNull(project.getBasePath())).normalize().toString();

                if (!localDirPath.equals(basePathPath)) {
                    appRunningNotice(Constants.LOCAL_DIR_NOT_MATCH, StringUtils.EMPTY, NotificationType.ERROR);
                    ApplicationManager.getApplication().getMessageBus()
                            .syncPublisher(NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC)
                            .action(Constants.HUMAN_OPERATE, null);
                    return false;
                }
            } else {
                AppConfigDialog appConfigDialog = new AppConfigDialog(project, userAppNode, userAppConfigs);
                appConfigDialog.show();
                return false;
            }
        } else {
            appRunningNotice("服务没有配置信息，不能运行", StringUtils.EMPTY, NotificationType.ERROR);
            return false;
        }

        if (!Constants.DEBUG_STATUS_SUCCESSFUL.equals(userAppNode.getUserApp().getStatus())) {
            NocalhostAppListManager nocalhostAppListManager = NocalhostAppListManager.getInstance(project);
            List<UserProject> userProjectList = nocalhostAppListManager.get();
            if (null != userProjectList) {
                for (UserProject userProject : userProjectList) {
                    for (UserApp app : userProject.getApps()) {
                        if (app.getId() == userAppNode.getUserApp().getId()) {
                            NocalhostAppListManager.getInstance(project).addRunningApp(app);
                            break;
                        }
                    }
                }
                nocalhostAppListManager.set(userProjectList);

                ApplicationManager.getApplication().getMessageBus()
                        .syncPublisher(NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC)
                        .action(Constants.HUMAN_OPERATE, userProjectList);
            }
        }
        return true;
    }

    public boolean checkUserAppStatus() {
        UserApp runningApp = NocalhostAppListManager.getInstance(project).getRunningApp(userAppNode.getUserApp().getId());
        if (null != runningApp) {
            appRunningNotice("服务正在部署中，禁止操作", "请等待服务部署完成，如遇异常情况无法完成部署，请等待两分钟手动停止。", NotificationType.INFORMATION);
            return false;
        }
        return true;
    }

    private void appRunningNotice(String title, String message, NotificationType information) {
        ApplicationManager.getApplication().invokeLater(
                () -> Notifications.showNotification(
                        project,
                        Constants.NHCTL_NOTIFICATION_GROUP,
                        title,
                        message,
                        information
                ));
    }


}
