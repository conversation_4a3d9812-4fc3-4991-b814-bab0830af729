package com.sentinel.nocalhost.ui.action.toolbar;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.DumbAwareAction;
import com.sentinel.nocalhost.Config;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;
import java.net.URI;


public class ReviewHelpAction extends DumbAwareAction {

    public ReviewHelpAction() {
        super("帮助文档", StringUtils.EMPTY, AllIcons.Actions.Help);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        String help_url = Config.getProperty("review_help_url");
        // 检查Desktop API是否支持浏览操作
        if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
            try {
                // 使用Desktop API打开默认浏览器访问指定URL
                Desktop.getDesktop().browse(new URI(help_url));
            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "无法打开浏览器，请手动访问: " + help_url, "错误", JOptionPane.ERROR_MESSAGE);
            }
        } else {
            JOptionPane.showMessageDialog(null, "当前系统不支持自动打开浏览器，请手动访问: " + help_url, "警告", JOptionPane.WARNING_MESSAGE);
        }
    }
}
