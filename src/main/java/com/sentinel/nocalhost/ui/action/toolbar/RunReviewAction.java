package com.sentinel.nocalhost.ui.action.toolbar;

import com.intellij.icons.AllIcons;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.api.data.CodeReviewTaskInfo;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import com.sentinel.nocalhost.ui.notification.Notifications;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class RunReviewAction extends DumbAwareAction {
    private final CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);
    private final Project project;

    private boolean isTaskInProgress = false;

    public RunReviewAction(Project project) {
        super("开始分析", StringUtils.EMPTY, AllIcons.Actions.RunAll);
        this.project = project;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent e) {
        // 注释：暂时禁用服务状态预检查
        // if (codeReviewAPI.queryReviewServerStatus(project)) {
        //     Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
        //             "CodeReview服务未启动，请等待自动启动", StringUtils.EMPTY, NotificationType.WARNING);
        //     return;
        // }

        if (isTaskInProgress) {
            return;
        }
        isTaskInProgress = true;
        String result = codeReviewAPI.queryReviewingTask(project);
        if (StringUtils.isEmpty(result) || "failed".equals(result)) {

            ProgressManager.getInstance().run(new Task.Backgroundable(project, "Submitting", false) {
                @Override
                public void run(@NotNull ProgressIndicator indicator) {

                    indicator.setText("Submitting");
                    try {
                        Thread.sleep(1500L);
                    } catch (InterruptedException ignored) {
                    }
                    indicator.stop();
                }
            });

            int reviewId = codeReviewAPI.executeCodeReview(project);
            List<CodeReviewTaskInfo> codeReviewTaskInfoList = codeReviewAPI.queryCodeReviewTaskList(project);
            for (CodeReviewTaskInfo codeReviewTaskInfo : codeReviewTaskInfoList) {
                if (codeReviewTaskInfo.getId() == reviewId) {
                    CodeReviewTaskManager codeReviewTaskManager = CodeReviewTaskManager.getInstance(project);
                    codeReviewTaskManager.set(codeReviewTaskInfo);
                    break;
                }
            }

            ApplicationManager.getApplication().getMessageBus()
                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC)
                    .refresh();

        } else if ("done".equals(result)) {
            Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                    "代码未发生变更，无法触发分析", StringUtils.EMPTY, NotificationType.WARNING);
            List<CodeReviewTaskInfo> reviewTaskList = codeReviewAPI.queryCodeReviewTaskList(project);
            CodeReviewTaskManager codeReviewTaskManager = CodeReviewTaskManager.getInstance(project);
            codeReviewTaskManager.set(reviewTaskList.get(0));
            ApplicationManager.getApplication().getMessageBus()
                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC)
                    .refresh();
        } else if ("checking".equals(result)) {
            Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                    "存在正在Checking任务，请稍后", StringUtils.EMPTY, NotificationType.WARNING);
        } else if ("undone".equals(result)) {
            Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                    "存在待处理Review任务，请处理", StringUtils.EMPTY, NotificationType.WARNING);
        } else if ("noChange".equals(result)) {
            Notifications.showNotification(project, Constants.REVIEW_NOTIFICATION_GROUP,
                    "代码未发生变更，无法触发分析", StringUtils.EMPTY, NotificationType.WARNING);
        }
        isTaskInProgress = false;
    }
}
