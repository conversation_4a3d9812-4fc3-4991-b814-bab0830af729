package com.sentinel.nocalhost.ui.action.contextmenu;

import com.intellij.icons.AllIcons;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.UserAppConfigs;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.ui.dialog.AppConfigDialog;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

public class ConfigAction extends BaseAction {

    public ConfigAction(Project project, UserAppNode userAppNode) {
        super(project, userAppNode,"Config", AllIcons.Nodes.ConfigFolder);
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent anActionEvent) {
        if (null == userAppNode.getUserApp()) {
            Notifications.showNotification(project, Constants.API_NOTIFICATION_GROUP,
                    "本地开发配置失败", "服务节点未关联服务", NotificationType.ERROR);
            return;
        }
        int appId = userAppNode.getUserApp().getId();
        if (appId == 0) {
            Notifications.showNotification(project, Constants.API_NOTIFICATION_GROUP,
                    "本地开发配置失败", "服务ID不存在", NotificationType.ERROR);
            return;
        }
        String cluster = userAppNode.getUserApp().getCluster();
        if (StringUtils.isEmpty(cluster)) {
            Notifications.showNotification(project, Constants.API_NOTIFICATION_GROUP,
                    "本地开发配置失败", "服务未关联集群", NotificationType.ERROR);
            return;
        }
        String namespace = userAppNode.getUserApp().getNamespace();
        if (StringUtils.isEmpty(namespace)) {
            Notifications.showNotification(project, Constants.API_NOTIFICATION_GROUP,
                    "本地开发配置失败", "服务未关联命名空间", NotificationType.ERROR);
            return;
        }
        String appName = userAppNode.getUserApp().getName();
        if (StringUtils.isEmpty(appName)) {
            Notifications.showNotification(project, Constants.API_NOTIFICATION_GROUP,
                    "本地开发配置失败", "服务名称为空", NotificationType.ERROR);
            return;
        }

        UserAppConfigs userAppConfigs = getUserAppConfigs();
        if (null != userAppConfigs) {
            AppConfigDialog appConfigDialog = new AppConfigDialog(project, userAppNode, userAppConfigs);
            appConfigDialog.show();
        }
    }

}
