package com.sentinel.nocalhost.ui.action.toolbar;

import com.intellij.openapi.actionSystem.*;
import com.intellij.openapi.actionSystem.ex.ComboBoxAction;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.util.List;

public class ModelSelectorAction extends ComboBoxAction {
    private JComponent button;
    private final NocalhostSettings settings;
    private final List<String> models;
    private String selectedModel = "claude-3.5-sonnet";

    public ModelSelectorAction(Project project) {
        CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);
        models = codeReviewAPI.queryReviewModelList(project);

        this.settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
        if (settings.getSelectedModel() != null && !settings.getSelectedModel().isEmpty()) {
            selectedModel = settings.getSelectedModel();
        } else {
            settings.setSelectedModel(selectedModel);
        }
        Presentation presentation = getTemplatePresentation();
        presentation.setText(selectedModel);
    }

    @Override
    public @NotNull DefaultActionGroup createPopupActionGroup(@NotNull JComponent button, @NotNull DataContext dataContext) {
        DefaultActionGroup group = new DefaultActionGroup();

        for (String model : models) {
            group.add(new SelectModelAction(model));
        }
        this.button = button;
        return group;
    }


    private class SelectModelAction extends AnAction {
        private final String model;

        public SelectModelAction(String model) {
            super(model);
            this.model = model;

            if (model.equals(selectedModel)) {
                Presentation presentation = getTemplatePresentation();
                presentation.setIcon(com.intellij.icons.AllIcons.Actions.Checked);
            }

        }

        @Override
        public void actionPerformed(@NotNull AnActionEvent e) {
            selectedModel = model;
            settings.setSelectedModel(model);
            JComponent component = button;
            if (component instanceof JButton) {
                ((JButton) component).setText(model);
            }
        }
    }
}

