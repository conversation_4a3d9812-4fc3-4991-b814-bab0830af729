package com.sentinel.nocalhost.ui.tree;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.ui.treeStructure.Tree;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;

import javax.swing.tree.TreeSelectionModel;


public class NocalhostTree extends Tree {

    private final Project project;
    private final NocalhostTreeModel model;

    public NocalhostTree(Project project) {
        this.project = project;
        this.model = new NocalhostTreeModel(project, this);
        init();
    }

    private void init() {
        this.setRootVisible(false);
        this.getSelectionModel().setSelectionMode(TreeSelectionModel.SINGLE_TREE_SELECTION);
        this.setCellRenderer(new TreeNodeRenderer(project));
        this.addMouseListener(new TreeMouseListener(this, project));

        ApplicationManager.getApplication().getMessageBus().connect()
                .subscribe(NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC, model);

    }

}