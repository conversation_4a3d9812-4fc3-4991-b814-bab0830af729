package com.sentinel.nocalhost.ui.tree;

import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostAppListManager;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import com.sentinel.nocalhost.ui.tree.node.UserProjectNode;
import com.intellij.ui.ColoredTreeCellRenderer;
import com.intellij.ui.LoadingNode;
import com.intellij.ui.scale.JBUIScale;
import com.intellij.util.ui.EmptyIcon;
import com.intellij.util.ui.UIUtil;
import icons.NocalhostIcons;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;


public class TreeNodeRenderer extends ColoredTreeCellRenderer {
    private final Project project;

    public TreeNodeRenderer(Project project) {
        this.project = project;
    }

    @Override
    public void customizeCellRenderer(@NotNull JTree tree,
                                      Object value,
                                      boolean selected,
                                      boolean expanded,
                                      boolean leaf,
                                      int row,
                                      boolean hasFocus) {
        if (value instanceof LoadingNode) {
            if (!selected) setForeground(UIUtil.getInactiveTextColor());
            setIcon(JBUIScale.scaleIcon(EmptyIcon.create(8, 16)));
            setToolTipText("loading...");
            return;
        }

        if (value instanceof UserAppNode node) {
            append(node.getUserApp().getName());

            UserApp runningApp = NocalhostAppListManager.getInstance(project).getRunningApp(node.getUserApp().getId());

            if (null == runningApp) {
                switch (node.getUserApp().getStatus()) {
                    case Constants.DEBUG_STATUS_PENDING -> setIcon(NocalhostIcons.Status.Normal);
                    case Constants.DEBUG_STATUS_DEPLOYING -> setIcon(NocalhostIcons.Status.Deploying);
                    case Constants.DEBUG_STATUS_SUCCESSFUL -> setIcon(NocalhostIcons.Status.Running);
                    case Constants.DEBUG_STATUS_FAILED -> setIcon(NocalhostIcons.Status.Failed);
                    default -> setIcon(NocalhostIcons.Status.Unknown);
                }
                setToolTipText(node.getUserApp().getStatus());
            } else {
                setIcon(NocalhostIcons.Status.Deploying);
                setToolTipText(Constants.DEBUG_STATUS_DEPLOYING);
            }
        }

        if (value instanceof UserProjectNode node) {
            append(node.getUserProject().getProjectName());
            setToolTipText(node.getUserProject().getProjectName());
        }
    }

}