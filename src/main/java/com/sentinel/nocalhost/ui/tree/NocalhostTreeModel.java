package com.sentinel.nocalhost.ui.tree;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.ui.treeStructure.Tree;
import com.sentinel.nocalhost.api.IdeHubAPI;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.api.data.UserProject;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import com.sentinel.nocalhost.ui.tree.node.UserProjectNode;
import com.sentinel.nocalhost.utils.ErrorUtil;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreeNode;
import javax.swing.tree.TreePath;
import java.util.*;
import java.util.stream.Collectors;

public class NocalhostTreeModel extends DefaultTreeModel implements NocalhostTreeUpdateNotifier {
    private final IdeHubAPI ideHubAPI;
    private final Project project;
    private final Tree tree;

    public NocalhostTreeModel(Project project, Tree tree) {
        super(new DefaultMutableTreeNode("ROOT"));
        this.ideHubAPI = ApplicationManager.getApplication().getService(IdeHubAPI.class);
        this.project = project;
        this.tree = tree;
        this.tree.setModel(this);
    }

    @Override
    public synchronized void action(String type, final List<UserProject> projectList) {
        DefaultMutableTreeNode root = (DefaultMutableTreeNode) this.getRoot();
        if (root.getChildCount() == 0) {
            newTreeNodes();
        } else {
            if (Constants.HUMAN_OPERATE.equals(type)) {
                ProgressManager.getInstance().run(new Task.Backgroundable(project, Constants.APP_REFRESH, false) {
                    final List<UserProject> userProjectList = projectList;
                    @Override
                    public void run(@NotNull ProgressIndicator indicator) {
                        indicator.setText(Constants.APP_REFRESH);
                        indicator.setIndeterminate(true);
                        refresh(userProjectList);
                    }
                });
            } else {
                refresh(projectList);
            }
        }
    }

    private void refresh(List<UserProject> userProjectList) {
        if (null == userProjectList) {
            userProjectList = ideHubAPI.queryUserProjectAppList(project);
        }
        refreshTree(userProjectList);
    }

    private void newTreeNodes() {
        try {
            DefaultMutableTreeNode root = (DefaultMutableTreeNode) this.getRoot();
            List<UserProject> userProjectList = ideHubAPI.queryUserProjectAppList(project);
            if (null != userProjectList) {
                addProjectsToRoot(root, userProjectList, new ArrayList<>(), new ArrayList<>());
                reloadTreeWithExpandedPaths(root, new HashMap<>());
            }
        } catch (Exception ex) {
            ErrorUtil.dealWith(project, "Load App Tree Error", "Load App Tree Error", ex);
        }
    }

    private void refreshTree(List<UserProject> userProjectList) {
        try {
            // Ensure this code runs on the EDT
            SwingUtilities.invokeLater(() -> {
                DefaultMutableTreeNode root = (DefaultMutableTreeNode) this.getRoot();
                if (userProjectList == null || userProjectList.isEmpty()) {
                    clearTree(root);
                } else {
                    List<Integer> expandedProjectIds = getExpandedProjectIds(root);
                    List<Object> selectedNodes = getTreeSelectNodes();
                    root.removeAllChildren(); // Remove all children regardless of the count
                    Map<String, List<TreePath>> pathMap = addProjectsToRoot(root, userProjectList, expandedProjectIds, selectedNodes);
                    reloadTreeWithExpandedPaths(root, pathMap);
                }
            });
        } catch (Exception ex) {
            ErrorUtil.dealWith(project, "refreshTree Error", "refreshTree Error.", ex);
        }
    }


    private List<Object> getTreeSelectNodes() {
        List<Object> selectedNodes = new ArrayList<>();
        TreePath[] selectedPaths = tree.getSelectionPaths();
        if (null != selectedPaths) {
            for (TreePath selectedPath : selectedPaths) {
                if (selectedPath.getLastPathComponent() instanceof UserProjectNode userProjectNode) {
                    selectedNodes.add(userProjectNode.getUserProject());
                } else if (selectedPath.getLastPathComponent() instanceof UserAppNode userAppNode) {
                    selectedNodes.add(userAppNode.getUserApp());
                }
            }
        }
        return selectedNodes;
    }

    private void clearTree(DefaultMutableTreeNode root) {
        if (root.getChildCount() > 0) {
            root.removeAllChildren();
        }
        ApplicationManager.getApplication().invokeLater(this::reload);
    }

    private List<Integer> getExpandedProjectIds(DefaultMutableTreeNode root) {
        List<Integer> expandedProjectIds = new ArrayList<>();
        Enumeration<TreePath> enumeration = tree.getExpandedDescendants(new TreePath(root));
        if (enumeration != null) {
            while (enumeration.hasMoreElements()) {
                TreePath treePath = enumeration.nextElement();
                if (treePath.getLastPathComponent() instanceof UserProjectNode userProjectNode) {
                    expandedProjectIds.add(userProjectNode.getUserProject().getProjectId());
                }
            }
        }
        return expandedProjectIds;
    }

    private Map<String, List<TreePath>> addProjectsToRoot(DefaultMutableTreeNode root,
                                                          List<UserProject> userProjectList,
                                                          List<Integer> expandedProjectIds,
                                                          List<Object> selectedNodes) {

        Map<String, List<TreePath>> pathMap = new HashMap<>();

        List<TreePath> expandedTreePaths = new ArrayList<>();
        List<TreePath> selectTreePaths = new ArrayList<>();

        for (UserProject project : userProjectList) {
            UserProjectNode projectNode = new UserProjectNode(project);
            for (UserApp userApp : project.getApps()) {
                UserAppNode appNode = new UserAppNode(userApp);
                projectNode.add(appNode);
            }
            root.add(projectNode);
            if (expandedProjectIds.contains(project.getProjectId())) {
                expandedTreePaths.add(new TreePath(projectNode.getPath()));
            }

            for (Object selectedNode : selectedNodes) {
                if (selectedNode instanceof UserProject userProject && userProject.getProjectId() == project.getProjectId()) {
                    selectTreePaths.add(new TreePath(projectNode.getPath()));
                } else if (selectedNode instanceof UserApp userApp) {
                    if (projectNode.getChildCount() > 0) {
                        Enumeration<TreeNode> children = projectNode.children();
                        while (children.hasMoreElements()) {
                            UserAppNode userAppNode = (UserAppNode) children.nextElement();
                            if (userAppNode.getUserApp().getId() == userApp.getId()) {
                                selectTreePaths.add(new TreePath(userAppNode.getPath()));
                            }
                        }
                    }
                }
            }
        }

        selectTreePaths = selectTreePaths.stream().filter(item -> expandedTreePaths.stream().anyMatch(treePath -> {
            if (treePath.getLastPathComponent() instanceof UserProjectNode userProjectNode
                    && null != item.getParentPath()
                    && item.getParentPath().getLastPathComponent() instanceof UserProjectNode parentUserNode) {
                return userProjectNode.getUserProject().getProjectId() == parentUserNode.getUserProject().getProjectId();
            }
            return false;
        })).collect(Collectors.toList());

        pathMap.put("expandedTreePaths", expandedTreePaths);
        pathMap.put("selectTreePaths", selectTreePaths);
        return pathMap;
    }

    private void reloadTreeWithExpandedPaths(DefaultMutableTreeNode root, Map<String, List<TreePath>> pathMap) {
        ApplicationManager.getApplication().invokeLater(() -> {
            this.reload(root);

            List<TreePath> expandedTreePaths = pathMap.get("expandedTreePaths");
            if (null != expandedTreePaths) {
                for (TreePath path : expandedTreePaths) {
                    tree.expandPath(path);
                }
            }

            List<TreePath> selectTreePaths = pathMap.get("selectTreePaths");
            if (selectTreePaths != null) {
                for (TreePath path : selectTreePaths) {
                    tree.addSelectionPath(path);
                }
            }

        });
    }
}
