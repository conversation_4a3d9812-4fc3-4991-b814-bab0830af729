package com.sentinel.nocalhost.ui.tree;

import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostAppListManager;
import com.intellij.openapi.actionSystem.Separator;
import com.sentinel.nocalhost.ui.action.contextmenu.*;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.ActionPopupMenu;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.JBPopupMenu;
import com.intellij.ui.treeStructure.Tree;

import javax.swing.*;
import javax.swing.tree.TreePath;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

public class TreeMouseListener extends MouseAdapter {
    private static final Separator SEPARATOR = new Separator();
    private final Project project;
    private final Tree tree;

    public TreeMouseListener(Tree tree, Project project) {
        this.tree = tree;
        this.project = project;
    }

    @Override
    public void mousePressed(MouseEvent event) {
        if (SwingUtilities.isRightMouseButton(event)) {
            TreePath treePath = tree.getClosestPathForLocation(event.getX(), event.getY());
            if (treePath == null) {
                return;
            }
            Object object = treePath.getLastPathComponent();

            if (object instanceof UserAppNode userAppNode) {
                // 延迟显示菜单
                SwingUtilities.invokeLater(() -> renderClusterAction(event, userAppNode));
            }
        }
    }

    private void renderClusterAction(MouseEvent event, UserAppNode userAppNode) {
        DefaultActionGroup actionGroup = new DefaultActionGroup();
        UserApp runningApp = NocalhostAppListManager.getInstance(project).getRunningApp(userAppNode.getUserApp().getId());
        if (Constants.DEBUG_STATUS_SUCCESSFUL.equals(userAppNode.getUserApp().getStatus())
                || Constants.DEBUG_STATUS_DEPLOYING.equals(userAppNode.getUserApp().getStatus())
                || Constants.DEBUG_STATUS_FAILED.equals(userAppNode.getUserApp().getStatus())
                || null != runningApp) {
            actionGroup.add(new RemoteStopAction(project, userAppNode));
            actionGroup.add(SEPARATOR);
        }
        actionGroup.add(new RemoteRunAction(project, userAppNode));
        actionGroup.add(SEPARATOR);
        actionGroup.add(new RemoteDebugAction(project, userAppNode));

        if (Constants.DEBUG_STATUS_SUCCESSFUL.equals(userAppNode.getUserApp().getStatus())) {
            actionGroup.add(SEPARATOR);
            actionGroup.add(new TerminalAction(project, userAppNode));
            actionGroup.add(SEPARATOR);
            actionGroup.add(new LogTerminalAction(project, userAppNode));
        }
        actionGroup.add(SEPARATOR);
        actionGroup.add(new ConfigAction(project, userAppNode));

        // 创建菜单之前，调整事件坐标
        MouseEvent adjustedEvent = new MouseEvent(
                event.getComponent(),
                event.getID(),
                event.getWhen(),
                event.getModifiersEx(),
                event.getX() + 15,
                event.getY() + 10,
                event.getClickCount(),
                event.isPopupTrigger()
        );

        ActionPopupMenu menu = ActionManager.getInstance().createActionPopupMenu("UserApp.Actions", actionGroup);
        JBPopupMenu.showByEvent(adjustedEvent, menu.getComponent());
    }

}
