package com.sentinel.nocalhost.ui.widget;

import com.intellij.dvcs.ui.LightActionGroup;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.actionSystem.ActionGroup;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.popup.ListPopup;
import com.intellij.openapi.util.Disposer;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.StatusBarWidget;
import com.intellij.util.Consumer;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.api.data.UserProject;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.commands.data.NhctlSyncOptions;
import com.sentinel.nocalhost.commands.data.NhctlSyncStatus;
import com.sentinel.nocalhost.commands.data.NhctlSyncStatusOptions;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostAppListManager;
import com.sentinel.nocalhost.service.NocalhostContextManager;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.ui.sync.NocalhostSyncPopup;
import com.sentinel.nocalhost.ui.sync.ServiceActionGroup;
import com.sentinel.nocalhost.utils.DataUtils;
import icons.NocalhostIcons;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

public class SyncStatusPresentation implements StatusBarWidget.MultipleTextValuesPresentation, StatusBarWidget.Multiframe {

    private static final Logger LOG = Logger.getInstance(SyncStatusPresentation.class);

    private final StatusBar statusBar;
    private final Project project;
    private final Disposable widget;

    private final NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);
    private final NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
    private final AtomicReference<NhctlSyncStatus> nhctlSyncStatus = new AtomicReference<>();


    public SyncStatusPresentation(Project project, StatusBar statusBar, Disposable widget) {
        this.widget = widget;
        this.project = project;
        this.statusBar = statusBar;

        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            while (!project.isDisposed()) {
                try {
                    if (StringUtils.isNotEmpty(settings.getUserToken())) {

                        List<UserApp> results = new ArrayList<>();
                        // 使用 List 代替数组来存储 CompletableFuture 对象
                        List<CompletableFuture<Void>> futures = new ArrayList<>();

                        NocalhostAppListManager nocalhostAppListManager = NocalhostAppListManager.getInstance(project);
                        if (null != nocalhostAppListManager) {
                            List<UserProject> userProjectList = nocalhostAppListManager.getContext();
                            if (userProjectList != null) {
                                for (UserProject userProject : userProjectList) {
                                    for (UserApp app : userProject.getApps()) {
                                        if (Constants.DEBUG_STATUS_SUCCESSFUL.equals(app.getStatus())) {
                                            if (results.stream().noneMatch(x -> x.getId() == app.getId())) {
                                                results.add(app);
                                                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> updateAppStatus(project, app, userProjectList));
                                                futures.add(future);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 等待所有任务完成
                        if (!futures.isEmpty()) {
                            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                            allOf.get();
                        }
                        NocalhostContextManager nocalhostContextManager = NocalhostContextManager.getInstance(project);
                        if (null != nocalhostContextManager) {
                            UserApp context = nocalhostContextManager.getContext();
                            if (null != context) {
                                NhctlSyncStatus syncStatus = getNhctlSyncStatus(context);
                                nhctlSyncStatus.set(syncStatus);
                                context.setSyncStatus(syncStatus);
                                NocalhostContextManager.getInstance(project).setContext(context);
                            } else {
                                List<UserProject> projectList = NocalhostAppListManager.getInstance(project).getContext();
                                if (null != projectList) {
                                    Optional<UserApp> appOptional = projectList.stream()
                                            .flatMap(userProject -> userProject.getApps().stream())
                                            .filter(app -> Constants.DEBUG_STATUS_SUCCESSFUL.equals(app.getStatus()))
                                            .findFirst();
                                    if (appOptional.isPresent()) {
                                        nhctlSyncStatus.set(appOptional.get().getSyncStatus());
                                    } else {
                                        nhctlSyncStatus.set(null);
                                    }
                                } else {
                                    nhctlSyncStatus.set(null);
                                }
                            }
                        }

                    }
                    Thread.sleep(3000);
                } catch (Exception ex) {
                    LOG.error("Failed to get sync status", ex);
                }
            }
        });

    }

    private void updateAppStatus(Project project, UserApp app, List<UserProject> userProjectList) {
        NhctlSyncStatus syncStatus = getNhctlSyncStatus(app);
        app.setSyncStatus(syncStatus);
        for (UserProject userProject : userProjectList) {
            for (UserApp userApp : userProject.getApps()) {
                if (userApp.getId() == app.getId()) {
                    userApp.setSyncStatus(syncStatus);
                }
            }
        }
        NocalhostAppListManager.getInstance(project).set(userProjectList);
    }

    private NhctlSyncStatus getNhctlSyncStatus(UserApp userApp) {
        try {
            NhctlSyncStatusOptions nhctlSyncStatusOptions =
                    new NhctlSyncStatusOptions(String.valueOf(userApp.getId()), userApp.getCluster(), userApp.getNamespace());
            String status = nhctlCommand.syncStatus(nhctlSyncStatusOptions);
            NhctlSyncStatus syncStatus = DataUtils.GSON.fromJson(status, NhctlSyncStatus.class);
            if ("disconnected".equals(syncStatus.getStatus())) {
                NhctlSyncOptions nhctlSyncOptions = new NhctlSyncOptions(String.valueOf(userApp.getId()), userApp.getCluster(), userApp.getNamespace());
                nhctlSyncOptions.setResume(true);
                nhctlCommand.sync(nhctlSyncOptions);
            }
            return syncStatus;
        } catch (Exception ignore) {
        }
        return null;
    }

    @Override
    public @NotNull StatusBarWidget copy() {
        return new SyncStatusWidget(project);
    }

    @Override
    public @NonNls
    @NotNull String ID() {
        return "File Sync Status";
    }

    @Override
    public void install(@NotNull StatusBar statusBar) {
    }

    @Override
    public void dispose() {
        Disposer.dispose(widget);
    }

    @Override
    public @Nullable String getTooltipText() {
        if (nhctlSyncStatus.get() != null) {
            if (StringUtils.isNoneBlank(nhctlSyncStatus.get().getOutOfSync())) {
                return nhctlSyncStatus.get().getOutOfSync();
            }
            return nhctlSyncStatus.get().getTips();
        }
        return "";
    }

    @Override
    public @Nullable Consumer<MouseEvent> getClickConsumer() {
        return mouseEvent -> statusBar.updateWidget("File Sync Status");
    }

    @NotNull
    private ActionGroup createActions() {
        var actions = new LightActionGroup();
        var context = NocalhostContextManager.getInstance(project).getContext();
        List<UserApp> results = getRunningUserApps();

        if (!results.isEmpty()) {
            actions.addSeparator("同步列表");
            if (context != null  && nhctlSyncStatus.get() != null) {
                actions.add(new ServiceActionGroup(project, context));
                results = results.stream().filter(x -> x.getId() != context.getId()).toList();
            }
            results.forEach(x -> actions.add(new ServiceActionGroup(project, x)));
        }
        return actions;
    }

    @NotNull
    private List<UserApp> getRunningUserApps() {
        List<UserApp> results = new ArrayList<>();
        List<UserProject> userProjectList = NocalhostAppListManager.getInstance(project).getContext();
        if (userProjectList != null) {
            for (UserProject userProject : userProjectList) {
                for (UserApp app : userProject.getApps()) {
                    if (Constants.DEBUG_STATUS_SUCCESSFUL.equals(app.getStatus())) {
                        if (results.stream().noneMatch(x -> x.getId() == app.getId())) {
                            results.add(app);
                        }
                    }
                }
            }
        }
        return results;
    }


    @Override
    public @Nullable("null means the widget is unable to show the popup") ListPopup getPopup() {
        return NocalhostSyncPopup.getInstance(project, createActions()).asListPopup();
    }

    @Override
    public @Nullable String getSelectedValue() {
        if (nhctlSyncStatus.get() != null) {
            return " " + nhctlSyncStatus.get().getMsg();
        }
        return "";
    }

    @Override
    public @Nullable Icon getIcon() {
        try {
            if (nhctlSyncStatus.get() == null) {
                return NocalhostIcons.ConfigurationLogo;
            }
            String status = nhctlSyncStatus.get().getStatus();
            return switch (status) {
                case "outOfSync" -> AllIcons.General.Warning;
                case "disconnected" -> AllIcons.Nodes.Pluginnotinstalled;
                case "error" -> AllIcons.Actions.Cancel;
                case "scanning", "syncing" -> NocalhostIcons.CloudUpload;
                case "idle" -> AllIcons.Nodes.DoneMark;
                case "end" -> AllIcons.Actions.Exit;
                default -> null;
            };
        } catch (Exception ignored) {

        }
        return AllIcons.General.Balloon;
    }
}
