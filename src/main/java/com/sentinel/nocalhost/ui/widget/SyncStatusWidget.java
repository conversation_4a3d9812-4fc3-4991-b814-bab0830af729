package com.sentinel.nocalhost.ui.widget;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.StatusBar;
import com.intellij.openapi.wm.StatusBarWidget;
import com.intellij.openapi.wm.WindowManager;
import org.jetbrains.annotations.NonNls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class SyncStatusWidget implements StatusBarWidget {

    private final SyncStatusPresentation syncStatusPresentation;
    private Thread updateThread = null;
    private boolean forceExit = false;


    public SyncStatusWidget(Project project) {
        StatusBar statusBar = WindowManager.getInstance().getStatusBar(project);
        syncStatusPresentation = new SyncStatusPresentation(project, statusBar, this);
    }

    @Override
    public @NonNls
    @NotNull String ID() {
        return "File Sync Status";
    }

    @Override
    public @Nullable StatusBarWidget.WidgetPresentation getPresentation() {
        return syncStatusPresentation;
    }

    @Override
    public void install(@NotNull StatusBar statusBar) {
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            updateThread = Thread.currentThread();
            while (!forceExit) {
                statusBar.updateWidget("File Sync Status");
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ignore) {}
            }
        });
    }

    @Override
    public void dispose() {
        forceExit = true;
        if (updateThread != null && !updateThread.isInterrupted()) {
            updateThread.interrupt();
        }
    }
}
