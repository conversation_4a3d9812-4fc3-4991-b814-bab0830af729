package com.sentinel.nocalhost.ui.dialog;

import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.fileChooser.FileChooser;
import com.intellij.openapi.fileChooser.FileChooserDescriptor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.ui.TextFieldWithBrowseButton;
import com.intellij.openapi.vfs.VirtualFile;
import com.sentinel.nocalhost.api.IdeHubAPI;
import com.sentinel.nocalhost.api.data.UserAppConfigs;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import com.intellij.openapi.vfs.LocalFileSystem;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.File;

public class AppConfigDialog extends DialogWrapper {
    private final IdeHubAPI ideHubAPI = ApplicationManager.getApplication().getService(IdeHubAPI.class);
    private final UserAppConfigs userAppConfigs;
    private final Project project;
    private final UserAppNode userAppNode;
    private TextFieldWithBrowseButton directoryField;

    public AppConfigDialog(@Nullable Project project, UserAppNode userAppNode, UserAppConfigs userAppConfigs) {
        super(project, true);
        this.project = project;
        this.userAppConfigs = userAppConfigs;
        this.userAppNode = userAppNode;

        setTitle(userAppNode.getUserApp().getName() + "本地开发配置: 请选择服务在本机的根目录");
        setSize(600, 120);

        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        int screenWidth = screenSize.width;
        int screenHeight = screenSize.height;

        int dialogWidth = getWindow().getWidth();

        setLocation((screenWidth - dialogWidth) / 2, screenHeight / 6);

        init();
    }

    @Override
    protected @Nullable JComponent createCenterPanel() {
        directoryField = new TextFieldWithBrowseButton();

        String localDir = userAppConfigs.getConfig().getLocalDir();
        if (StringUtils.isNotEmpty(localDir)) {
            File initialDirectory = new File(localDir);
            if (initialDirectory.exists() && initialDirectory.isDirectory()) {
                directoryField.setText(localDir);
            }
        }

        directoryField.setEditable(false);
        directoryField.setText(localDir);
        directoryField.addActionListener(e -> chooseDirectory());

        return directoryField;
    }

    @Override
    protected Action @NotNull [] createActions() {
        return new Action[]{
                new AbstractAction("保存") {
                    @Override
                    public void actionPerformed(ActionEvent e) {

                        if (StringUtils.isNotEmpty(directoryField.getText())) {
                            userAppConfigs.getConfig().setLocalDir(directoryField.getText());
                            userAppConfigs.setAppName(userAppNode.getUserApp().getName());
                            userAppConfigs.setCluster(userAppNode.getUserApp().getCluster());
                            userAppConfigs.setNamespace(userAppNode.getUserApp().getNamespace());

                            ideHubAPI.updateUserAppConfigs(project, userAppConfigs);

                            doOKAction();
                        } else {
                            Notifications.showNotification(project, Constants.NHCTL_NOTIFICATION_GROUP
                                    , "本地代码根目录不能为空", StringUtils.EMPTY, NotificationType.ERROR);
                        }
                    }
                },
                new AbstractAction("取消") {
                    @Override
                    public void actionPerformed(ActionEvent e) {
                        doCancelAction();
                    }
                }
        };
    }


    private void chooseDirectory() {
        FileChooserDescriptor descriptor = new FileChooserDescriptor(false, true, false, false, false, false);
        descriptor.setTitle(userAppNode.getUserApp().getName() + "本地开发配置: 请选择服务在本机的根目录");
        LocalFileSystem localFileSystem = LocalFileSystem.getInstance();
        VirtualFile initialFile = directoryField.getText().isEmpty() ? null : localFileSystem.findFileByPath(directoryField.getText());

        VirtualFile selectedDirectory = FileChooser.chooseFile(descriptor, project, initialFile);

        if (selectedDirectory != null) {
            directoryField.setText(selectedDirectory.getPath());
        }
    }

}
