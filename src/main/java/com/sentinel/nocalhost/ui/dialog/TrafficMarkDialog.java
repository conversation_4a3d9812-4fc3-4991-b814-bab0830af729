package com.sentinel.nocalhost.ui.dialog;

import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.ComboBox;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.ui.Messages;
import com.intellij.util.ui.JBUI;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.api.data.TrafficMark;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.exception.NocalhostExecuteCmdException;
import com.sentinel.nocalhost.ui.notification.Notifications;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class TrafficMarkDialog extends DialogWrapper {
    private final NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);

    private JComboBox<String> comboBox;
    private JTextField customInputField;
    private final Project project;
    private static final String CUSTOM_OPTION = "自定义";
    private static final String PREFIX = "JetDev-";

    public TrafficMarkDialog(@Nullable Project project) {
        super(project, true);
        this.project = project;
        setTitle("设置流量标记");
        setSize(500, 100);

        // Calculate screen dimensions
        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        int screenWidth = screenSize.width;
        int screenHeight = screenSize.height;

        // Calculate dialog dimensions
        int dialogWidth = getWindow().getWidth();

        // Set location to center top
        setLocation((screenWidth - dialogWidth) / 2, screenHeight / 8);

        init();
    }

    @Override
    protected Action @NotNull [] createActions() {
        return new Action[]{getOKAction(), getCancelAction()};
    }

    @Override
    protected @Nullable JComponent createCenterPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.insets = JBUI.insets(5);

        CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);
        java.util.List<TrafficMark> trafficMarks = codeReviewAPI.queryTrafficMarks(project);
        if (trafficMarks != null && !trafficMarks.isEmpty()) {
            // 使用 ArrayList 动态构建选项列表
            List<String> optionsList = new ArrayList<>(trafficMarks.size() + 1);
            for (TrafficMark mark : trafficMarks) {
                optionsList.add(mark.getMarkName());
            }
            optionsList.add(CUSTOM_OPTION);
            // 将 List 转换为数组
            comboBox = new ComboBox<>(optionsList.toArray(new String[0]));
        } else {
            // 简化为空集合的情况
            comboBox = new ComboBox<>(new String[]{CUSTOM_OPTION});
        }

        panel.add(comboBox, gbc);

        // 创建一个面板来包含前缀标签和输入框
        JPanel customInputPanel = new JPanel(new BorderLayout());

        // 创建前缀标签
        JLabel prefixLabel = new JLabel(PREFIX);
        prefixLabel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createMatteBorder(1, 1, 1, 0, UIManager.getColor("Component.borderColor")),
                BorderFactory.createEmptyBorder(0, 5, 0, 5)
        ));
        prefixLabel.setBackground(UIManager.getColor("TextField.background"));
        prefixLabel.setOpaque(true);

        // 创建输入框
        customInputField = new JTextField(15);

        // 将前缀标签和输入框添加到面板
        customInputPanel.add(prefixLabel, BorderLayout.WEST);
        customInputPanel.add(customInputField, BorderLayout.CENTER);
        customInputPanel.setVisible(false);

        gbc.gridy++;
        panel.add(customInputPanel, gbc);

        comboBox.addActionListener(e -> {
            String selected = (String) comboBox.getSelectedItem();
            customInputPanel.setVisible(CUSTOM_OPTION.equals(selected));
            pack();
        });

        try {
            String trafficTag = nhctlCommand.getTrafficMark();
            if (StringUtils.isNotEmpty(trafficTag)) {
                trafficTag = trafficTag.trim();
                boolean found = false;
                for (int i = 0; i < comboBox.getItemCount(); i++) {
                    String item = comboBox.getItemAt(i);
                    if (item.equals(trafficTag) || (trafficTag.startsWith(PREFIX) && item.equals(trafficTag.substring(PREFIX.length())))) {
                        comboBox.setSelectedIndex(i);
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    comboBox.setSelectedItem(CUSTOM_OPTION);
                    if (trafficTag.startsWith(PREFIX)) {
                        customInputField.setText(trafficTag.substring(PREFIX.length()));
                    } else {
                        customInputField.setText(trafficTag);
                    }
                    customInputPanel.setVisible(true);
                }
            }
        } catch (Exception e) {
            ApplicationManager.getApplication().invokeLater(() ->
                    Messages.showErrorDialog(e.getMessage(), Constants.GET_TRAFFIC_MARK_ERROR));
        }

        return panel;
    }

    @Override
    protected void doOKAction() {
        String selectedValue = (String) comboBox.getSelectedItem();
        String trafficMark;
        if (CUSTOM_OPTION.equals(selectedValue)) {
            // 只需要获取输入框的值，前缀已经在UI上显示
            String userInput = customInputField.getText();
            trafficMark = PREFIX + userInput;
        } else {
            assert selectedValue != null;
            if (selectedValue.startsWith(PREFIX)) {
                trafficMark = selectedValue;
            } else {
                trafficMark = PREFIX + selectedValue;
            }
        }

        if (StringUtils.isEmpty(trafficMark) || trafficMark.equals(PREFIX)) {
            Messages.showErrorDialog("Please enter a valid traffic mark", "Invalid Input");
            return;
        }

        try {
            String result = nhctlCommand.setTrafficMark(trafficMark);
            if (StringUtils.isEmpty(result)) {
                Notifications.showNotification(project, Constants.NHCTL_NOTIFICATION_GROUP,
                        Constants.TRAFFIC_MARK_SET_SUCCESS, trafficMark, NotificationType.INFORMATION);
                close(OK_EXIT_CODE);
            } else {
                ApplicationManager.getApplication().invokeLater(() ->
                        Messages.showErrorDialog(result, Constants.SET_TRAFFIC_MARK_ERROR));
            }
        } catch (NocalhostExecuteCmdException | IOException | InterruptedException ex) {
            ApplicationManager.getApplication().invokeLater(() ->
                    Messages.showErrorDialog(ex.getMessage(), Constants.SET_TRAFFIC_MARK_ERROR));
        }
    }

}