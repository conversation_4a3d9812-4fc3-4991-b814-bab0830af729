package com.sentinel.nocalhost.ui.dialog;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.ComboBox;
import com.intellij.openapi.ui.DialogWrapper;
import com.intellij.openapi.ui.Messages;
import com.intellij.util.ui.JBUI;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import com.sentinel.nocalhost.ui.codereview.event.RejectSuggestionEvent;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.awt.*;
import java.util.List;
import java.util.Map;

public class RejectSuggestionDialog extends DialogWrapper {
    private final Project project;
    private JComboBox<String> reasonsComboBox;
    private JTextField otherReasonField;
    private JLabel otherReasonLabel;
    private final List<CodeReviewSuggestionInfo> suggestionInfos;
    private int currentSuggestionIndex;
    private final Map<String, String> rejectReasonMap;

    public RejectSuggestionDialog(@Nullable Project project, List<CodeReviewSuggestionInfo> suggestionInfos, int currentSuggestionIndex) {
        super(project, true);
        this.currentSuggestionIndex = currentSuggestionIndex;
        this.project = project;
        this.suggestionInfos = suggestionInfos;

        // Initialize the reject reason map
        CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);
        rejectReasonMap = codeReviewAPI.queryRejectReasonList(project);

        setTitle("Reject Suggestion");
        setSize(300, 150);

        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        int screenWidth = screenSize.width;
        int screenHeight = screenSize.height;

        int dialogWidth = getWindow().getWidth();

        setLocation((screenWidth - dialogWidth) / 2, screenHeight / 8);

        init();
    }

    @Override
    protected Action @NotNull [] createActions() {
        return new Action[]{getOKAction(), getCancelAction()};
    }

    @Override
    protected @Nullable JComponent createCenterPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = JBUI.insets(5);

        // Create ComboBox with the new reject reasons
        reasonsComboBox = new ComboBox<>(rejectReasonMap.values().toArray(new String[0]));
        panel.add(new JLabel("Reject reason:"), gbc);
        gbc.gridx = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        panel.add(reasonsComboBox, gbc);

        // Create "Other Reason" label and text field
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.weightx = 0;
        otherReasonLabel = new JLabel("Other Reason:");
        panel.add(otherReasonLabel, gbc);

        gbc.gridx = 1;
        otherReasonField = new JTextField();
        panel.add(otherReasonField, gbc);

        // Listen for ComboBox selection changes
        reasonsComboBox.addItemListener(e -> {
            boolean isOtherSelected = "其他".equals(e.getItem());
            otherReasonLabel.setVisible(isOtherSelected);
            otherReasonField.setVisible(isOtherSelected);
            if (isOtherSelected) {
                otherReasonField.requestFocusInWindow();
            }
        });

        // Initially hide the "Other Reason" label and text field
        otherReasonLabel.setVisible(false);
        otherReasonField.setVisible(false);

        return panel;
    }

    @Override
    protected void doOKAction() {
        String selectedReason = (String) reasonsComboBox.getSelectedItem();
        String reasonKey = getKeyByValue(rejectReasonMap, selectedReason);
        String otherReason = StringUtils.EMPTY;

        if ("OTHER".equals(reasonKey)) {
            otherReason = otherReasonField.getText();
            if (otherReason.isEmpty()) {
                Messages.showErrorDialog(project, "Please provide a reason for rejection.", "Error");
                return;
            }
        }

        new RejectSuggestionEvent(project, suggestionInfos.get(currentSuggestionIndex)).execute(reasonKey, otherReason);

        if (currentSuggestionIndex < suggestionInfos.size() - 1) {
            currentSuggestionIndex++;
        }
        ApplicationManager.getApplication().invokeLater(
                () -> ApplicationManager.getApplication().getMessageBus()
                        .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refreshSuggestionPanel(currentSuggestionIndex));
        super.doOKAction();
    }

    private String getKeyByValue(Map<String, String> map, String value) {
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(value)) {
                return entry.getKey();
            }
        }
        return null;
    }
}
