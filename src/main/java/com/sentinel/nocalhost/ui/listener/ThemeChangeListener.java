package com.sentinel.nocalhost.ui.listener;

import com.intellij.ide.ui.UISettings;
import com.intellij.ide.ui.UISettingsListener;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.ui.toolwindow.ToolWindowIconUpdater;
import org.jetbrains.annotations.NotNull;

public class ThemeChangeListener implements UISettingsListener {

    private final Project project;

    public ThemeChangeListener(Project project) {
        this.project = project;
    }

    @Override
    public void uiSettingsChanged(@NotNull UISettings uiSettings) {
        ToolWindowIconUpdater.updateToolWindowIcons(project);
    }


}
