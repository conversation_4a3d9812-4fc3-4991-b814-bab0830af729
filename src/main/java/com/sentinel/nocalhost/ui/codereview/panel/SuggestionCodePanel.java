package com.sentinel.nocalhost.ui.codereview.panel;

import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorFactory;
import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.editor.colors.EditorColorsScheme;
import com.intellij.openapi.editor.ex.EditorEx;
import com.intellij.openapi.editor.markup.HighlighterLayer;
import com.intellij.openapi.editor.markup.TextAttributes;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.TextRange;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBLabel;
import com.intellij.ui.components.JBScrollPane;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.utils.CodeUtils;

import javax.swing.*;
import java.awt.*;

public class SuggestionCodePanel {
    private final Project project;
    private final CodeReviewSuggestionInfo suggestion;

    public SuggestionCodePanel(Project project, CodeReviewSuggestionInfo suggestions) {
        this.project = project;
        this.suggestion = suggestions;
    }

    public JScrollPane createEditor() {
        String diffCode = CodeUtils.getDiff(suggestion.getOriginal_code_snippet(), suggestion.getSuggested_code_snippet());
        Editor editor = createEditor(diffCode);
        JScrollPane scrollPane = new JBScrollPane(editor.getComponent());
        scrollPane.setBorder(BorderFactory.createTitledBorder(""));
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
        return scrollPane;
    }

    private Editor createEditor(String code) {
        EditorFactory editorFactory = EditorFactory.getInstance();
        Document document = editorFactory.createDocument(code);
        Editor editor = editorFactory.createEditor(document, project, getFileType(), true);
        // 设置到编辑器头部
        editor.setHeaderComponent(getEditorHeader());
        editor.getComponent().setFocusable(false);
        editor.getContentComponent().setFocusable(false);
        editor.getSettings().setLineMarkerAreaShown(false);
        editor.getSettings().setIndentGuidesShown(false);
        editor.getSettings().setLineNumbersShown(false);
        editor.getSettings().setFoldingOutlineShown(false);
        editor.getSettings().setAdditionalColumnsCount(0);
        editor.getSettings().setAdditionalLinesCount(0);
        editor.getSettings().setRightMarginShown(false);
        editor.getSettings().setCaretRowShown(false);
        editor.getSettings().setUseSoftWraps(false);
        editor.getComponent().setBorder(BorderFactory.createEmptyBorder());
        editor.getContentComponent().setBorder(BorderFactory.createEmptyBorder());
        PsiDocumentManager.getInstance(project).commitDocument(document);

        int lineCount = document.getLineCount();
        int lineHeight = editor.getLineHeight();
        int editorHeight = Math.max((lineCount * lineHeight + 50), 550);
        editor.getComponent().setPreferredSize(new Dimension(-1, editorHeight));
        // 高亮显示代码
        highlightLines(editor, document);

        setColorScheme((EditorEx) editor);

        editor.getDocument().setReadOnly(true);
        return editor;
    }

    private void setColorScheme(EditorEx editor) {
        try {
            EditorColorsScheme colorScheme = EditorColorsManager.getInstance().getGlobalScheme();
            editor.setVerticalScrollbarVisible(false);
            editor.setColorsScheme(colorScheme);
        } catch (Exception ignored) {

        }
    }

    private FileType getFileType() {
        String filePath = suggestion.getFile_path();
        String fileExtension = filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase();
        return FileTypeManager.getInstance().getFileTypeByExtension(fileExtension);
    }

    private JPanel getEditorHeader() {
        JPanel headerPanel = new JPanel(new BorderLayout());
        headerPanel.setBorder(BorderFactory.createEmptyBorder());
        JBLabel label = getJbLabel(headerPanel);
        headerPanel.add(label, BorderLayout.WEST);
        JLabel jLabel = CodeUtils.getJLabel(suggestion.getSuggested_code_snippet());
        headerPanel.add(jLabel, BorderLayout.EAST);
        return headerPanel;
    }

    private JBLabel getJbLabel(JPanel headerPanel) {
        JBLabel label = new JBLabel();
        headerPanel.setPreferredSize(new Dimension(headerPanel.getPreferredSize().width, 30));

        // 获取文件路径的扩展名
        String filePath = suggestion.getFile_path();
        String fileExtension = filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase();

        // 根据文件扩展名设置标题
        String title = switch (fileExtension) {
            case Constants.CODE_FILE_SUFFIX_GO -> "Go";
            case Constants.CODE_FILE_SUFFIX_PYTHON -> "Python";
            case Constants.CODE_FILE_SUFFIX_JAVA -> "Java";
            default -> fileExtension.isEmpty() ? "" : capitalize(fileExtension);
        };

        // 设置标签文本
        label.setText(" " + title);
        label.setFont(new Font("Microsoft YaHei", Font.PLAIN, 14));

        return label;
    }

    // 帮助方法：首字母大写
    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }


    private void highlightLines(Editor editor, Document document) {
        TextAttributes lightRedBackground = new TextAttributes();
        lightRedBackground.setBackgroundColor(new JBColor(new Color(87, 47, 57), new Color(87, 47, 57)));

        TextAttributes lightGreenBackground = new TextAttributes();
        lightGreenBackground.setBackgroundColor(new JBColor(new Color(41, 68, 54), new Color(41, 68, 54)));

        int lineCount = document.getLineCount();

        for (int i = 0; i < lineCount; i++) {
            int startOffset = document.getLineStartOffset(i);
            int endOffset = document.getLineEndOffset(i);
            String lineText = document.getText(new TextRange(startOffset, endOffset));
            if (lineText.startsWith(Constants.SUGGESTION_DELETE_CODE_PREFIX)) {
                editor.getMarkupModel().addLineHighlighter(i, HighlighterLayer.SYNTAX, lightRedBackground);
                WriteCommandAction.runWriteCommandAction(editor.getProject(), () -> {
                    String updatedLineText = lineText.substring(Constants.SUGGESTION_DELETE_CODE_PREFIX.length());
                    document.replaceString(startOffset, endOffset, updatedLineText);
                });
            } else if (lineText.startsWith(Constants.SUGGESTION_ADD_CODE_PREFIX)) {
                editor.getMarkupModel().addLineHighlighter(i, HighlighterLayer.SYNTAX, lightGreenBackground);
                WriteCommandAction.runWriteCommandAction(editor.getProject(), () -> {
                    String updatedLineText = lineText.substring(Constants.SUGGESTION_ADD_CODE_PREFIX.length());
                    document.replaceString(startOffset, endOffset, updatedLineText);
                });
            }
        }
    }

}
