package com.sentinel.nocalhost.ui.codereview.panel;

import com.intellij.ui.Gray;
import com.intellij.ui.jcef.JBCefBrowser;
import com.intellij.ui.jcef.JBCefBrowserBuilder;
import com.sentinel.nocalhost.utils.CodeUtils;

import javax.swing.*;
import java.awt.*;

public class ReviewMermaidPanel {
    private final String content;


    public ReviewMermaidPanel(String content) {
        this.content = content;
    }


    public JPanel getPanel() {
        // 创建Cef浏览器并加载Mermaid图表
        JBCefBrowser browser = new JBCefBrowserBuilder().build();
        String htmlContent = createMermaidHtml(content.replace("mermaid", "").replaceAll("`", ""));
        browser.loadHTML(htmlContent);

        JComponent jComponent = browser.getComponent();

        // 创建外部卡片面板
        JPanel cardPanel = new JPanel();
        cardPanel.setLayout(new BorderLayout());

        // 创建头部面板
        JPanel headPanel = CodeUtils.getEditorHeader("Mermaid", content);
        headPanel.setBorder(BorderFactory.createLineBorder(Gray._50));
        // 将头部面板添加到卡片面板
        cardPanel.add(headPanel, BorderLayout.NORTH);

        // 创建内容区域，将Mermaid图表组件添加进去
        JPanel contentArea = new JPanel(new BorderLayout());
        contentArea.setBackground(UIManager.getColor("Panel.background"));
        contentArea.add(jComponent, BorderLayout.CENTER);
        contentArea.setPreferredSize(new Dimension(-1, 400));

        // 将内容区域添加到卡片面板
        cardPanel.add(contentArea, BorderLayout.CENTER);
        return cardPanel;
    }

    public static String createMermaidHtml(String mermaidContent) {
        // 将流程图内容替换到 HTML 模板中
        return """
                <!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'dark',
            flowchart: {
                curve: 'basis'
            }
        });

        window.onload = function() {
            // 获取Mermaid图表的容器
            const mermaidContainer = document.querySelector('.mermaid');

            // 监听Mermaid图表渲染完成事件
            mermaid.init(undefined, mermaidContainer).then(() => {
                // 获取渲染后的图表高度
                const mermaidHeight = mermaidContainer.getBoundingClientRect().height;

                // 设置容器的高度
                mermaidContainer.style.height = `${mermaidHeight}px`;
            });
        };
    </script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            margin: 0;
            padding: 0;
            justify-content: center;
            align-items: flex-start; /* 从顶部对齐 */
        }
        .mermaid {
            height: auto; /* 高度随内容撑开 */
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            min-height: 100px; /* 设置一个最小高度 */
        }
    </style>
</head>
<body>
    <div class="mermaid">
            %%{init: {'theme': 'dark', 'themeVariables': { 'fontSize': '12px'}}}%%

            """ + mermaidContent + """
    </div>
</body>
</html>
                """;
    }


}
