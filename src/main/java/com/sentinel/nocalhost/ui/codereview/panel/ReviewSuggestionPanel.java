package com.sentinel.nocalhost.ui.codereview.panel;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.application.WriteAction;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.ui.components.JBScrollPane;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import com.sentinel.nocalhost.api.data.CodeReviewTaskInfo;
import com.sentinel.nocalhost.service.CodeReviewSuggestionListManager;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.utils.CodeUtils;

import javax.swing.*;
import java.awt.*;
import java.util.List;

public class ReviewSuggestionPanel {
    private final CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);
    private final Project project;
    private Editor fileEditor;
    private int currentSuggestionIndex = 0;
    private final JPanel centerPanel;
    private JPanel bottomPanel;
    private CodeReviewSuggestionInfo lastSuggestion;

    public ReviewSuggestionPanel(Project project) {
        this.project = project;
        centerPanel = new JPanel();
        centerPanel.setLayout(new BorderLayout());
        centerPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        refresh(-3);
    }

    public JPanel getSuggestionPanel() {
        return centerPanel;
    }

    public void refreshSuggestionPanel() {
        ApplicationManager.getApplication().invokeLater(() -> WriteAction.run(() -> {
            codeReviewAPI.queryCodeReviewTaskList(project);
            codeReviewAPI.querySuggestionList(project);

            CodeReviewTaskManager codeReviewTaskManager = CodeReviewTaskManager.getInstance(project);
            if (null == codeReviewTaskManager) {
                return;
            }

            CodeReviewTaskInfo taskInfo = codeReviewTaskManager.get();
            if (null == taskInfo) {
                return;
            }

            if ("checking".equals(taskInfo.getStatus())) {
                setLabelPanel("代码正在Review中, 请耐心等待。");
            } else if ("undone".equals(taskInfo.getStatus()) || "done".equals(taskInfo.getStatus())) {
                List<CodeReviewSuggestionInfo> suggestionInfos = CodeReviewSuggestionListManager.getInstance(project).get();
                if (null != suggestionInfos && !suggestionInfos.isEmpty()) {
                    CodeReviewSuggestionInfo suggestion = suggestionInfos.get(currentSuggestionIndex);
                    if (null != lastSuggestion && lastSuggestion.getId() == suggestion.getId()) {
                        centerPanel.remove(bottomPanel);
                        SuggestionBottomPanel suggestionBottomPanel =
                                new SuggestionBottomPanel(project, fileEditor, suggestionInfos, currentSuggestionIndex);
                        bottomPanel = suggestionBottomPanel.getBottomPanel();
                        centerPanel.add(bottomPanel, BorderLayout.SOUTH);
                    } else {
                        centerPanel.removeAll();
                        if (null != fileEditor) {
                            fileEditor.getMarkupModel().removeAllHighlighters();
                        }
                        fileEditor = CodeUtils.openSuggestionFile(project, suggestion);
                        setContentPanel(suggestion);
                        SuggestionBottomPanel suggestionBottomPanel =
                                new SuggestionBottomPanel(project, fileEditor, suggestionInfos, currentSuggestionIndex);
                        bottomPanel = suggestionBottomPanel.getBottomPanel();
                        centerPanel.add(bottomPanel, BorderLayout.SOUTH);
                        lastSuggestion = suggestion;
                    }
                } else {
                    setLabelPanel("恭喜你！经过 Review 没有发现问题。");
                }
            }
            centerPanel.revalidate();
            centerPanel.repaint();
        }));
    }

    private void setLabelPanel(String text) {
        JPanel noIssuePanel = new JPanel();
        noIssuePanel.setLayout(new BorderLayout());

        JLabel noIssueLabel = new JLabel(text, SwingConstants.CENTER);
        noIssueLabel.setFont(new Font("Microsoft YaHei", Font.PLAIN, 16));
        noIssuePanel.add(noIssueLabel, BorderLayout.CENTER);

        centerPanel.removeAll();
        centerPanel.add(noIssuePanel, BorderLayout.CENTER);
    }

    private void setContentPanel(CodeReviewSuggestionInfo suggestion) {
        JPanel contentPanel = new JPanel();
        contentPanel.setBorder(BorderFactory.createEmptyBorder());
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));

        JPanel analysisPanel = new SuggestionAnalysisPanel(suggestion);
        analysisPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        contentPanel.add(analysisPanel);

        JScrollPane codePanel = new SuggestionCodePanel(project, suggestion).createEditor();
        codePanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        contentPanel.add(codePanel);

        JScrollPane scrollPane = new JBScrollPane(contentPanel);
        scrollPane.setBorder(BorderFactory.createEmptyBorder());
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);

        centerPanel.setLayout(new BorderLayout());
        centerPanel.add(scrollPane, BorderLayout.CENTER);
    }

    public void refresh(int index) {
        if (index > -1) {
            currentSuggestionIndex = index;
            ApplicationManager.getApplication().invokeLater(this::refreshSuggestionPanel);
        } else if (index == -1) {
            ApplicationManager.getApplication().invokeLater(this::refreshSuggestionPanel);
        } else if (index == -2) {
            refreshBottom();
        } else if (index == -3) {
            ApplicationManager.getApplication().invokeLater(this::refreshSuggestionPanel);
        }
    }

    private void refreshBottom() {
        ApplicationManager.getApplication().invokeLater(() -> WriteAction.run(() -> {
            List<CodeReviewSuggestionInfo> suggestionInfos = CodeReviewSuggestionListManager.getInstance(project).get();
            centerPanel.remove(bottomPanel);

            if (null != suggestionInfos && !suggestionInfos.isEmpty()) {
                SuggestionBottomPanel suggestionBottomPanel =
                        new SuggestionBottomPanel(project, fileEditor, suggestionInfos, currentSuggestionIndex);
                bottomPanel = suggestionBottomPanel.getBottomPanel();
                centerPanel.add(bottomPanel, BorderLayout.SOUTH);
            }
            centerPanel.revalidate();
            centerPanel.repaint();
        }));
    }
}

