package com.sentinel.nocalhost.ui.codereview.event;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import com.sentinel.nocalhost.api.data.FeedbackCodeReviewRequest;
import com.sentinel.nocalhost.service.CodeReviewSuggestionListManager;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class RejectSuggestionEvent {
    private final Project project;
    private final CodeReviewSuggestionInfo suggestion;
    private final CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);

    public RejectSuggestionEvent(Project project, CodeReviewSuggestionInfo suggestion) {
        this.project = project;
        this.suggestion = suggestion;
    }

    public void execute(String reason, String other_reason) {

        ProgressManager.getInstance().run(new Task.Backgroundable(project, "Rejecting", false) {
            @Override
            public void run(@NotNull ProgressIndicator indicator) {
                indicator.setText("Rejecting");
                FeedbackCodeReviewRequest feedbackCodeReviewRequest = new FeedbackCodeReviewRequest();
                feedbackCodeReviewRequest.setIntellicode_issue_id(suggestion.getId());
                feedbackCodeReviewRequest.setIntellicode_review_id(suggestion.getIntellicode_review_id());
                feedbackCodeReviewRequest.setResult("reject");
                feedbackCodeReviewRequest.setReason(reason);
                feedbackCodeReviewRequest.setOther_reason(other_reason);
                codeReviewAPI.checkCodeReview(project, feedbackCodeReviewRequest);

                List<CodeReviewSuggestionInfo> suggestionInfos = CodeReviewSuggestionListManager.getInstance(project).get();
                for (CodeReviewSuggestionInfo suggestionInfo : suggestionInfos) {
                    if (suggestionInfo.getId() == suggestion.getId()) {
                        suggestionInfo.setStatus("done");
                        break;
                    }
                }

                boolean done = suggestionInfos.stream().allMatch(suggestionInfo -> suggestionInfo.getStatus().equals("done"));
                if (done) {
                    CodeReviewTaskManager.getInstance(project).set(null);
                    CodeReviewSuggestionListManager.getInstance(project).set(null);

                    ApplicationManager.getApplication().invokeLater(
                            () -> ApplicationManager.getApplication().getMessageBus()
                                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refresh());
                } else {
                    CodeReviewSuggestionListManager.getInstance(project).set(suggestionInfos);
                }

                indicator.cancel();
                indicator.stop();
            }
        });

    }

}
