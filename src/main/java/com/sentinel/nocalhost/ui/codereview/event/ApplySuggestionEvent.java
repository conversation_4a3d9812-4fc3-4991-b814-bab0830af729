package com.sentinel.nocalhost.ui.codereview.event;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.ProgressManager;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.popup.Balloon;
import com.intellij.openapi.ui.popup.JBPopupFactory;
import com.intellij.openapi.vfs.LocalFileSystem;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiDocumentManager;
import com.intellij.psi.PsiFile;
import com.intellij.psi.PsiManager;
import com.intellij.ui.JBColor;
import com.intellij.ui.awt.RelativePoint;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import com.sentinel.nocalhost.api.data.FeedbackCodeReviewRequest;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.CodeReviewSuggestionListManager;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import com.sentinel.nocalhost.utils.CodeUtils;
import org.jetbrains.annotations.NotNull;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.util.List;

public class ApplySuggestionEvent {
    private final CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);

    public ApplySuggestionEvent(Project project, JButton applyButton, Editor fileEditor, CodeReviewSuggestionInfo suggestion) {
        // 获取文件的 VirtualFile
        VirtualFile virtualFile = LocalFileSystem.getInstance().findFileByPath(project.getBasePath() + "/" + suggestion.getFile_path());
        if (virtualFile == null) {
            notice(applyButton, project, suggestion);
            return;
        }
        // 获取 PsiFile
        PsiFile psiFile = PsiManager.getInstance(project).findFile(virtualFile);
        if (psiFile == null) {
            notice(applyButton, project, suggestion);
            return;
        }
        // 获取并返回关联的 Document
        Document document = PsiDocumentManager.getInstance(project).getDocument(psiFile);
        if (document == null) {
            return;
        }
        // 计算开始和结束行对应的字符偏移
        int startOffset = document.getText().indexOf(suggestion.getOriginal_code_snippet());
        if (startOffset > -1) {
            int endOffset = startOffset + suggestion.getOriginal_code_snippet().length();
            // 使用 WriteCommandAction 来执行文档修改
            WriteCommandAction.runWriteCommandAction(project, () -> {
                // 删除原有内容
                document.deleteString(startOffset, endOffset);
                // 插入新内容
                document.insertString(startOffset, suggestion.getSuggested_code_snippet());

                int suggestionEndOffset = startOffset + suggestion.getSuggested_code_snippet().length();

                JBColor jbColor = new JBColor(new Color(41, 68, 54), new Color(41, 68, 54));
                CodeUtils.highlighterCode(fileEditor, startOffset, suggestionEndOffset, jbColor);
                updateSuggestionStatus(project, suggestion, "");
            });
        } else {
            notice(applyButton, project, suggestion);
        }

    }

    private void notice(JButton applyButton, Project project, CodeReviewSuggestionInfo suggestion) {
        StringSelection stringSelection = new StringSelection(suggestion.getSuggested_code_snippet());
        Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stringSelection, null);
        Balloon balloon = JBPopupFactory.getInstance()
                .createHtmlTextBalloonBuilder(Constants.NOT_FOUND_SOURCE_CODE_TITLE + "\n" + Constants.NOT_FOUND_SOURCE_CODE_CONTENT,
                        AllIcons.General.NotificationInfo, JBColor.PanelBackground, null)
                .setTitle(null)
                .setFadeoutTime(3000)
                .createBalloon();
        balloon.show(RelativePoint.getCenterOf(applyButton), Balloon.Position.above);
        updateSuggestionStatus(project, suggestion, "SEARCH_NOT_MATCH");
    }

    private void updateSuggestionStatus(Project project, CodeReviewSuggestionInfo suggestion, String message) {

        ProgressManager.getInstance().run(new Task.Backgroundable(project, "Applying", false) {
            @Override
            public void run(@NotNull ProgressIndicator indicator) {
                indicator.setText("Applying");
                FeedbackCodeReviewRequest feedbackCodeReviewRequest = new FeedbackCodeReviewRequest();
                feedbackCodeReviewRequest.setIntellicode_review_id(suggestion.getIntellicode_review_id());
                feedbackCodeReviewRequest.setIntellicode_issue_id(suggestion.getId());
                feedbackCodeReviewRequest.setUnimplemented_reason(message);
                feedbackCodeReviewRequest.setResult("agree");
                codeReviewAPI.checkCodeReview(project, feedbackCodeReviewRequest);
                List<CodeReviewSuggestionInfo> suggestionInfos = CodeReviewSuggestionListManager.getInstance(project).get();
                for (CodeReviewSuggestionInfo suggestionInfo : suggestionInfos) {
                    if (suggestionInfo.getId() == suggestion.getId()) {
                        suggestionInfo.setStatus("done");
                        break;
                    }
                }
                boolean done = suggestionInfos.stream().allMatch(suggestionInfo -> suggestionInfo.getStatus().equals("done"));
                if (done) {
                    CodeReviewTaskManager.getInstance(project).set(null);
                    CodeReviewSuggestionListManager.getInstance(project).set(null);

                    ApplicationManager.getApplication().invokeLater(
                            () -> ApplicationManager.getApplication().getMessageBus()
                                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refresh());
                } else {
                    CodeReviewSuggestionListManager.getInstance(project).set(suggestionInfos);

                    ApplicationManager.getApplication().invokeLater(
                            () -> ApplicationManager.getApplication().getMessageBus()
                                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refreshSuggestionPanel(-2));
                }
                indicator.stop();
            }
        });

    }
}
