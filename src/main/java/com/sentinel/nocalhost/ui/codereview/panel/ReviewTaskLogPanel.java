package com.sentinel.nocalhost.ui.codereview.panel;

import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.editor.Document;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.EditorFactory;
import com.intellij.openapi.editor.colors.EditorColorsManager;
import com.intellij.openapi.editor.colors.EditorColorsScheme;
import com.intellij.openapi.editor.ex.EditorEx;
import com.intellij.openapi.fileTypes.FileType;
import com.intellij.openapi.fileTypes.FileTypeManager;
import com.intellij.openapi.project.Project;
import com.intellij.ui.components.JBScrollPane;
import com.sentinel.nocalhost.api.data.CodeReviewTaskInfo;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.utils.CodeUtils;
import org.apache.commons.lang3.StringUtils;

import javax.swing.*;
import java.awt.*;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ReviewTaskLogPanel {
    private final Project project;
    private final JPanel contentPanel;
    private boolean isInCodeBlock = false;
    private Editor currentEditor = null;
    private JEditorPane currentTextPane = null;
    private String currentLanguage = "";

    public ReviewTaskLogPanel(Project project) {
        this.project = project;

        contentPanel = new JPanel();
        contentPanel.setLayout(new BoxLayout(contentPanel, BoxLayout.Y_AXIS));
        new Thread(this::processMessages).start();
    }

    public JPanel getContentPanel() {
        JPanel jPanel = new JPanel();
        jPanel.setLayout(new BorderLayout());
        jPanel.add(contentPanel, BorderLayout.CENTER);
        return jPanel;
    }


    private void processMessages() {
        // 查询日志信息
        CodeReviewTaskManager manager = CodeReviewTaskManager.getInstance(project);
        if (null != manager) {
            StringBuilder tempMessage = new StringBuilder();
            StringBuilder codeBlock = new StringBuilder();

            String message = getStrings();

            for (int i = 0; i < message.length(); i++) {
                String chunk = message.substring(i, Math.min(i + 1, message.length()));
                tempMessage.append(chunk);

                if (tempMessage.toString().endsWith("`") || tempMessage.toString().endsWith("``")) {
                    if (!tempMessage.toString().contains("```")) {
                        continue;
                    }
                }

                if (isInCodeBlock) {
                    codeBlock.append(chunk);
                    updateCurrentEditor(codeBlock.toString());
                    if (tempMessage.toString().contains("```")) {
                        String codeContent = codeBlock.toString();
                        updateCurrentEditor(codeContent);
                        isInCodeBlock = false;
                        tempMessage.setLength(0);
                        codeBlock.setLength(0);
                        currentTextPane = null;
                    }
                    continue;
                }

                if (tempMessage.toString().contains("```")) {
                    int index = tempMessage.indexOf("```");
                    String textContent = tempMessage.substring(0, index).trim();
                    if (!textContent.isEmpty()) {
                        updateCurrentTextPane(textContent);
                    }
                    String code = tempMessage.substring(index);
                    if (code.contains("\n")) {
                        if (code.startsWith("```mermaid")) {
                            String regex = "(?s)```mermaid\\s(.*?)\\s```";
                            Pattern pattern = Pattern.compile(regex);
                            Matcher matcher = pattern.matcher(code);
                            while (matcher.find()) {
                                String data = tempMessage.substring(index + 3);
                                createNewEditor(data);
                                tempMessage.delete(0, tempMessage.length());
                                codeBlock.delete(0, tempMessage.length());
                            }
                        } else {
                            String data = tempMessage.substring(index + 3);
                            createNewEditor(data);
                            codeBlock.append(data);
                            tempMessage.delete(0, tempMessage.length());
                            isInCodeBlock = true;
                        }
                    }
                } else {
                    updateCurrentTextPane(tempMessage.toString());
                }
            }

            CodeReviewTaskInfo codeReviewTaskInfo = manager.get();
            if (null != codeReviewTaskInfo) {
                codeReviewTaskInfo.setStatus("success");
                CodeReviewTaskManager.getInstance(project).set(codeReviewTaskInfo);
            }
        }

    }

    private void updateCurrentTextPane(String text) {
        if (currentTextPane == null) {
                currentTextPane = new JEditorPane();
                currentTextPane.setContentType("text/html");
                currentTextPane.setEditable(false);
                currentTextPane.setBorder(BorderFactory.createEmptyBorder());
                contentPanel.add(currentTextPane);
            }
            String htmlContent = CodeUtils.convertMarkdownToHtml(text);
            CodeUtils.setStyledText(currentTextPane, htmlContent);
            contentPanel.revalidate();
            contentPanel.repaint();
    }


    private void createNewEditor(String content) {
        String extension;
        try {
            extension = content.split("\\n")[0].toLowerCase(Locale.ROOT);
        } catch (Exception e) {
            extension = "Empty";
        }
        if ("mermaid".equals(extension)) {
            createMermaidPanel(content);
        } else {
            String finalExtension = extension;
            WriteCommandAction.runWriteCommandAction(project, () -> {
                EditorFactory editorFactory = EditorFactory.getInstance();
                Document document = editorFactory.createDocument("");
                FileType fileType = FileTypeManager.getInstance().getFileTypeByExtension("Empty".equals(finalExtension) ? "go" : finalExtension);
                currentEditor = editorFactory.createEditor(document, project, fileType, true);
                configureEditor(currentEditor);
                setColorScheme((EditorEx) currentEditor);
                String language = finalExtension.substring(0, 1).toUpperCase() + finalExtension.substring(1);
                currentLanguage = ("Empty".equals(language) ? "" : language).toLowerCase(Locale.ROOT);
                currentEditor.setHeaderComponent(CodeUtils.getEditorHeader(language, content));
                JScrollPane scrollPane = new JBScrollPane(currentEditor.getComponent());
                scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
                contentPanel.add(scrollPane);
            });
        }
    }

     private void updateCurrentEditor(String content) {
        content = content.replaceAll("`", "");
        if (StringUtils.isNotEmpty(currentLanguage)) {
            content = content.replaceFirst("(?i)^" + currentLanguage + "\\s*", "");
        }
        String finalContent = content;
        WriteCommandAction.runWriteCommandAction(currentEditor.getProject(), () -> {
            Document document = currentEditor.getDocument();
            document.replaceString(0, document.getText().length(), finalContent);
            int lineCount = currentEditor.getDocument().getLineCount();
            int lineHeight = currentEditor.getLineHeight();
            int editorHeight = (lineCount * lineHeight + 50);
            currentEditor.getComponent().setPreferredSize(new Dimension(-1, editorHeight));
        });
    }

    private void createMermaidPanel(String content) {
        // 创建Cef浏览器并加载Mermaid图表
        JPanel cardPanel = new ReviewMermaidPanel(content).getPanel();
        // 将卡片面板添加到父容器中
        contentPanel.add(cardPanel);
        contentPanel.revalidate();
        contentPanel.repaint();
    }

    private void configureEditor(Editor editor) {
        editor.getComponent().setFocusable(false);
        editor.getContentComponent().setFocusable(false);
        editor.getSettings().setIndentGuidesShown(false);
        editor.getSettings().setLineMarkerAreaShown(false);
        editor.getSettings().setLineNumbersShown(false);
        editor.getSettings().setAdditionalLinesCount(0);
        editor.getSettings().setFoldingOutlineShown(false);
        editor.getSettings().setAdditionalColumnsCount(0);
        editor.getSettings().setRightMarginShown(false);
        editor.getSettings().setCaretRowShown(false);
        editor.getSettings().setUseSoftWraps(false);
        editor.getComponent().setBorder(BorderFactory.createEmptyBorder());
        editor.getContentComponent().setBorder(BorderFactory.createEmptyBorder());
        int lineCount = editor.getDocument().getLineCount();
        int lineHeight = editor.getLineHeight();
        int editorHeight = Math.max((lineCount * lineHeight + 50), 100);
        editor.getComponent().setPreferredSize(new Dimension(-1, editorHeight));
    }

    private void setColorScheme(EditorEx editor) {
        try {
            EditorColorsScheme colorScheme = EditorColorsManager.getInstance().getGlobalScheme();
            editor.setVerticalScrollbarVisible(false);
            editor.setColorsScheme(colorScheme);
        } catch (Exception ignored) {

        }
    }

    private static String getStrings() {
        return """
                                在 Java 中，将字符串的首字母转换为大写有多种方法。以下是几种常见的方法：

                ### 方法一：使用 `substring` 和 `toUpperCase` 方法

                ```java
                public class StringUtil {
                    public static String capitalizeFirstLetter(String input) {
                        if (input == null || input.isEmpty()) {
                            return input;
                        }
                        return input.substring(0, 1).toUpperCase() + input.substring(1);
                    }

                    public static void main(String[] args) {
                        String input = "hello world";
                        String result = capitalizeFirstLetter(input);
                        System.out.println(result); // 输出: Hello world
                    }
                }
                ```


                ### 方法二：使用 `Character` 类和 `StringBuilder`

                ```java
                public class StringUtil {
                    public static String capitalizeFirstLetter(String input) {
                        if (input == null || input.isEmpty()) {
                            return input;
                        }
                        char firstChar = Character.toUpperCase(input.charAt(0));
                        StringBuilder sb = new StringBuilder(input.length());
                        sb.append(firstChar).append(input.substring(1));
                        return sb.toString();
                    }

                    public static void main(String[] args) {
                        String input = "hello world";
                        String result = capitalizeFirstLetter(input);
                        System.out.println(result); // 输出: Hello world
                    }
                }
                ```


                ### 方法三：使用 Apache Commons Lang 库

                如果你的项目中已经使用了 Apache Commons Lang 库，可以使用 `WordUtils.capitalize` 方法。

                首先，添加 Maven 依赖：

                ```xml
                <dependency>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                    <version>3.12.0</version>
                </dependency>
                ```


                然后，使用 `WordUtils.capitalize` 方法：

                ```java
                import org.apache.commons.lang3.text.WordUtils;

                public class StringUtil {
                    public static String capitalizeFirstLetter(String input) {
                        if (input == null || input.isEmpty()) {
                            return input;
                        }
                        return WordUtils.capitalize(input, new char[]{' '});
                    }

                    public static void main(String[] args) {
                        String input = "hello world";
                        String result = capitalizeFirstLetter(input);
                        System.out.println(result); // 输出: Hello world
                    }
                }
                ```


                                 ```mermaid
                                   flowchart TD
                                       A[开始] --> B{建议列表不为空？}
                                       B -->|否| F[结束]
                                       B -->|是| C[获取当前建议]
                                       C --> D{当前建议与上次相同？}
                                       D -->|是| E[移除旧底部面板，创建新底部面板]
                                       D -->|否| G[移除所有旧面板，清除高亮标记]
                                       G --> H[打开新建议文件]
                                       H --> I[设置内容面板]
                                       I --> J[创建新底部面板]
                                       J --> K[设置底部面板]
                                       K --> L[更新界面]
                                       E --> L
                                       L --> M[结束]
                                   ```

                ### 结合上下文

                假设你在 `ReviewTaskLogPanel.java` 文件的第 130 行需要将某个字符串的首字母大写，可以使用上述任意一种方法。以下是一个示例：

                ```java
                // ReviewTaskLogPanel.java
                public class ReviewTaskLogPanel {
                    // 其他代码...

                    public void someMethod() {
                        String logMessage = "review task started";
                        String capitalizedLogMessage = capitalizeFirstLetter(logMessage);
                        System.out.println(capitalizedLogMessage); // 输出: Review task started
                    }

                    private String capitalizeFirstLetter(String input) {
                        if (input == null || input.isEmpty()) {
                            return input;
                        }
                        return input.substring(0, 1).toUpperCase() + input.substring(1);
                    }

                    // 其他代码...
                }
                ```


                在这个示例中，`capitalizeFirstLetter` 方法被定义为一个私有方法，用于将传入的字符串首字母大写。你可以在 `someMethod` 方法中调用这个方法来处理日志消息。
                通过以上步骤，你可以在 IntelliJ IDEA 插件中实现一个展示 Mermaid 语法流程图的功能。""";
    }

}
