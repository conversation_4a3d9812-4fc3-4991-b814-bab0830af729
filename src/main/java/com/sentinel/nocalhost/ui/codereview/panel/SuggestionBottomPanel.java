package com.sentinel.nocalhost.ui.codereview.panel;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.project.Project;
import com.intellij.ui.Gray;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import com.sentinel.nocalhost.ui.codereview.event.ApplySuggestionEvent;
import com.sentinel.nocalhost.ui.dialog.RejectSuggestionDialog;

import javax.swing.*;
import java.awt.*;
import java.util.List;

public class SuggestionBottomPanel {
    private final List<CodeReviewSuggestionInfo> suggestionInfos;
    private int currentSuggestionIndex;
    private final Project project;
    private final Editor fileEditor;

    public SuggestionBottomPanel(Project project, Editor fileEditor, List<CodeReviewSuggestionInfo> suggestionInfoList, int index) {
        this.project = project;
        this.fileEditor = fileEditor;
        this.currentSuggestionIndex = index;
        this.suggestionInfos = suggestionInfoList;
    }

    public JPanel getBottomPanel() {
        JPanel jPanel = new JPanel();
        jPanel.setLayout(new FlowLayout(FlowLayout.CENTER, 10, 5));

        JButton prevButton = new JButton("Pre");
        JButton nextButton = new JButton("Next");
        JButton rejectButton = new JButton("Reject");
        JButton applyButton = new JButton("Apply");

        JLabel paginationLabel = new JLabel((currentSuggestionIndex + 1) + "/" + (suggestionInfos.isEmpty() ? 1 : suggestionInfos.size()));
        paginationLabel.setForeground(Gray._187);

        prevButton.addActionListener(e -> gotoPreSuggestion());
        nextButton.addActionListener(e -> gotoNextSuggestion());
        applyButton.addActionListener(e -> new ApplySuggestionEvent(project, applyButton, fileEditor, suggestionInfos.get(currentSuggestionIndex)));

        rejectButton.addActionListener(e -> {
            RejectSuggestionDialog rejectSuggestionDialog = new RejectSuggestionDialog(project, suggestionInfos, currentSuggestionIndex);

            Point buttonLocation = rejectButton.getLocationOnScreen();

            Dimension buttonSize = rejectButton.getSize();
            Dimension dialogSize = rejectSuggestionDialog.getPreferredSize();

            int x = buttonLocation.x + (buttonSize.width - dialogSize.width) / 2;
            int y = buttonLocation.y - dialogSize.height - 20;

            rejectSuggestionDialog.setLocation(x, y);

            rejectSuggestionDialog.show();
        });

        jPanel.add(prevButton);
        jPanel.add(paginationLabel);
        jPanel.add(nextButton);

        CodeReviewSuggestionInfo suggestionInfo = suggestionInfos.get(currentSuggestionIndex);
        if ("done".equals(suggestionInfo.getStatus())) {
            rejectButton.setEnabled(false);
            applyButton.setEnabled(false);
        }
        if (currentSuggestionIndex == suggestionInfos.size() -1) {
            nextButton.setEnabled(false);
        }
        if (currentSuggestionIndex == 0) {
            prevButton.setEnabled(false);
        }
        jPanel.add(rejectButton);
        jPanel.add(applyButton);

        return jPanel;
    }

    private void gotoPreSuggestion() {
        currentSuggestionIndex--;
        ApplicationManager.getApplication().invokeLater(
                () -> ApplicationManager.getApplication().getMessageBus()
                        .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refreshSuggestionPanel(currentSuggestionIndex));
    }

    private void gotoNextSuggestion() {
        currentSuggestionIndex++;
        ApplicationManager.getApplication().invokeLater(
                () -> ApplicationManager.getApplication().getMessageBus()
                        .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC).refreshSuggestionPanel(currentSuggestionIndex));
    }

}
