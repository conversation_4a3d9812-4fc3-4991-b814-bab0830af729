package com.sentinel.nocalhost.ui.codereview.panel;

import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.ActionToolbar;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.actionSystem.Separator;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.editor.SelectionModel;
import com.intellij.openapi.editor.markup.MarkupModel;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.SimpleToolWindowPanel;
import com.intellij.ui.Gray;
import com.sentinel.nocalhost.api.CodeReviewAPI;
import com.sentinel.nocalhost.api.data.CodeReviewTaskInfo;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import com.sentinel.nocalhost.ui.action.toolbar.*;
import com.sentinel.nocalhost.utils.CodeUtils;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import javax.swing.*;
import java.awt.*;
import java.util.List;

@Getter
public class ReviewWorkspacePanel implements RefreshReviewToolWindowNotifier {
    private final CodeReviewAPI codeReviewAPI = ApplicationManager.getApplication().getService(CodeReviewAPI.class);
    private final NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
    private final SimpleToolWindowPanel mainPanel;
    private final Project project;
    private ReviewSuggestionPanel reviewSuggestionPanel;
    private boolean isChecking = false;

    public ReviewWorkspacePanel(Project project) {
        this.project = project;
        mainPanel = new SimpleToolWindowPanel(true);
        initToolWindowsPanel();

        Timer timer = new Timer(10000, e -> {
            CodeReviewTaskInfo codeReviewTaskInfo = CodeReviewTaskManager.getInstance(project).get();
            if (null != codeReviewTaskInfo) {
                if (codeReviewTaskInfo.getStatus().equals("checking")) {
                    ApplicationManager.getApplication().invokeLater(
                            () -> ApplicationManager.getApplication().getMessageBus()
                                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC)
                                    .refreshSuggestionPanel(-1));
                }
                if (codeReviewTaskInfo.getStatus().equals("failed")) {
                    CodeReviewTaskManager.getInstance(project).set(null);
                    ApplicationManager.getApplication().invokeLater(
                            () -> ApplicationManager.getApplication().getMessageBus()
                                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC)
                                    .refresh());
                }
            } else {
                List<CodeReviewTaskInfo> reviewTaskList = codeReviewAPI.queryCodeReviewTaskList(project);
                if (null != reviewTaskList && !reviewTaskList.isEmpty()) {
                    if (reviewTaskList.stream().anyMatch(taskInfo -> taskInfo.getStatus().equals("checking"))) {
                        isChecking = true;
                        ApplicationManager.getApplication().invokeLater(() -> ApplicationManager.getApplication().getMessageBus()
                                .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC)
                                .refresh());
                    } else {
                        if (isChecking) {
                            ApplicationManager.getApplication().invokeLater(() -> ApplicationManager.getApplication().getMessageBus()
                                    .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC)
                                    .refresh());
                        }
                        isChecking = false;
                    }
                }
            }
        });
        timer.start();
    }

    private void initToolWindowsPanel() {
        if (StringUtils.isNotEmpty(settings.getUserToken())) {
            List<CodeReviewTaskInfo> reviewTaskList = codeReviewAPI.queryCodeReviewTaskList(project);
            if (null == reviewTaskList || reviewTaskList.isEmpty()) {
                setEmptyPanel();
            } else {
                CodeReviewTaskInfo codeReviewTaskInfo = CodeReviewTaskManager.getInstance(project).get();
                if (null == codeReviewTaskInfo) {
                    ReviewTaskPanel reviewTaskPanel = new ReviewTaskPanel(project, reviewTaskList);
                    mainPanel.add(reviewTaskPanel.getPanel(), BorderLayout.CENTER);
                } else {
                    reviewSuggestionPanel = new ReviewSuggestionPanel(project);
                    mainPanel.add(reviewSuggestionPanel.getSuggestionPanel(), BorderLayout.CENTER);
                }
            }
        } else {
            noticeLogin();
        }
        setToolbar(getActionGroup());
    }

    private void setToolbar(DefaultActionGroup actionGroup) {
        // 创建并设置 ActionToolbar
        ActionToolbar actionToolbar = ActionManager.getInstance().createActionToolbar("JetDev.Toolbar", actionGroup, false);
        actionToolbar.setTargetComponent(mainPanel);
        // 添加 ActionToolbar 到主面板
        mainPanel.setToolbar(actionToolbar.getComponent());
        // 创建一个分隔线
        JSeparator separator = new JSeparator();
        separator.setPreferredSize(new Dimension(0, 2)); // 设置横线的高度
        // 创建一个面板来存放 ActionToolbar 和分隔线
        JPanel toolbarPanel = new JPanel(new BorderLayout());
        toolbarPanel.add(actionToolbar.getComponent(), BorderLayout.CENTER);
        toolbarPanel.add(separator, BorderLayout.SOUTH); // 将分隔线放在工具栏下方
        // 将工具栏面板添加到 mainPanel
        mainPanel.add(toolbarPanel, BorderLayout.NORTH);
    }

    private DefaultActionGroup getActionGroup() {
        DefaultActionGroup actionGroup = new DefaultActionGroup();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(settings.getUserToken())) {
            actionGroup.add(new RunReviewAction(project));
            actionGroup.add(new Separator());
            actionGroup.add(new BackHomeAction(project));
            actionGroup.add(new Separator());
            actionGroup.add(new RefreshReviewAction(project));
            actionGroup.add(new Separator());
            actionGroup.add(new ReviewHelpAction());
            actionGroup.add(new Separator());
            actionGroup.add(new ModelSelectorAction(project));
        } else {
            actionGroup.add(new LoginAccountsAction(project));
        }
        return actionGroup;
    }

    private void noticeLogin() {
        mainPanel.setLayout(new BorderLayout());
        JPanel topPanel = new JPanel();
        topPanel.setLayout(new BoxLayout(topPanel, BoxLayout.Y_AXIS));
        JLabel textLabel = new JLabel("Please login first");
        textLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        topPanel.add(Box.createVerticalStrut(20));
        topPanel.add(textLabel);
        mainPanel.add(topPanel, BorderLayout.CENTER);
    }

    private void setEmptyPanel() {
        JPanel centerPanel = new JPanel();
        centerPanel.setLayout(new BorderLayout());
        centerPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        JEditorPane editorPane = new JEditorPane();
        editorPane.setEditable(false);

        Color textColor = Gray._200;

        centerPanel.setBorder(BorderFactory.createTitledBorder(""));

        String htmlContent = CodeUtils.convertMarkdownToHtml("""
            <div style='color: %s'>
            欢迎来到啄木鸟，我可以为你<span style='color: #007ACC'>Review</span>代码，给出建议。

            功能说明：
            - 点击上方 <span style='color: #007ACC'>开始分析</span> 按钮，即可对当前仓库中【未提交】代码进行 <span style='color: #007ACC'>Review</span>。
            - <span style='color: #007ACC'>Review</span> 结果可以点击<span style='color: #007ACC'>Apply</span>按钮，直接应用到当前仓库中。
            </div>""".formatted(String.format("#%02x%02x%02x", textColor.getRed(), textColor.getGreen(), textColor.getBlue())));

        try {
            editorPane.setContentType("text/html");
            editorPane.setForeground(textColor);
            CodeUtils.setStyledText(editorPane, htmlContent);
        } catch (Exception ignored) {
        }

        centerPanel.add(editorPane);
        centerPanel.setPreferredSize(new Dimension(-1, 100));
        mainPanel.add(centerPanel, BorderLayout.CENTER);
    }

    @Override
    public void refresh() {
        SwingUtilities.invokeLater(() -> {
            Editor editor = FileEditorManager.getInstance(project).getSelectedTextEditor();
            if (null != editor) {
                MarkupModel markupModel = editor.getMarkupModel();
                markupModel.removeAllHighlighters();
                SelectionModel selectionModel = editor.getSelectionModel();
                selectionModel.removeSelection();
            }
        });
        SwingUtilities.invokeLater(() -> {
            mainPanel.removeAll();
            initToolWindowsPanel();
        });
    }

    @Override
    public void refreshSuggestionPanel(int index) {
        SwingUtilities.invokeLater(() -> {
            reviewSuggestionPanel.refresh(index);
            mainPanel.revalidate();
            mainPanel.repaint();
        });
    }

}
