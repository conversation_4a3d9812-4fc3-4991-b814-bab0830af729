package com.sentinel.nocalhost.ui.codereview.panel;

import com.intellij.icons.AllIcons;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.util.ui.JBUI;
import com.sentinel.nocalhost.api.data.CodeReviewTaskInfo;
import com.sentinel.nocalhost.service.CodeReviewTaskManager;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import com.sentinel.nocalhost.utils.CodeUtils;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class ReviewTaskPanel {
    private final JPanel centerPanel;
    private final Project project;
    private final List<CodeReviewTaskInfo> reviewTaskList;

    public ReviewTaskPanel(Project project, List<CodeReviewTaskInfo> reviewTaskList) {
        this.project = project;
        this.reviewTaskList = reviewTaskList;
        centerPanel = new JPanel();
        centerPanel.setLayout(new BorderLayout());
        centerPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
    }

    public JPanel getPanel() {
        JPanel taskPanel = new JPanel();
        taskPanel.setLayout(new BoxLayout(taskPanel, BoxLayout.Y_AXIS));
        for (int i = 0; i < reviewTaskList.size(); i++) {
            JPanel panel = createTaskPanel(i);
            taskPanel.add(panel);
        }
        JBScrollPane scrollPane = new JBScrollPane(taskPanel);
        scrollPane.setBorder(BorderFactory.createTitledBorder("历史记录"));
        centerPanel.add(scrollPane, BorderLayout.CENTER);

        return centerPanel;
    }

    private JPanel createTaskPanel(int index) {
        CodeReviewTaskInfo task = reviewTaskList.get(index);
        JPanel panel = new JPanel(new BorderLayout());
        panel.setPreferredSize(new Dimension(-1, 70));
        panel.setMaximumSize(new Dimension(Integer.MAX_VALUE, 70));
        panel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createLineBorder(new JBColor(0xE0E0E0, 0x404040)),
                BorderFactory.createEmptyBorder(0, 0, 1, 0)));

        JPanel taskInfoPanel = new JPanel(new GridBagLayout());
        taskInfoPanel.setBackground(panel.getBackground());

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.fill = GridBagConstraints.HORIZONTAL;

        JLabel taskNameLabel = new JLabel(task.getReview_md5());
        taskNameLabel.setIcon(getTaskStatusIcon(task.getStatus()));
        taskNameLabel.setIconTextGap(8);

        JLabel jLabel = CodeUtils.getJLabel(task.getReview_md5());

        // 创建一个 JPanel，将 taskNameLabel 和 jLabel 放在一起
        JPanel labelPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 2, 0)); // 左对齐，2px 间距
        labelPanel.setOpaque(false); // 让背景透明
        labelPanel.add(taskNameLabel);
        labelPanel.add(jLabel);

        gbc.gridx = 0;
        gbc.weightx = 1;
        gbc.insets = JBUI.insets(0, 5, 0, 20);
        taskInfoPanel.add(labelPanel, gbc);

        String status = task.getStatus();
        JBColor backgroundColor = new JBColor(Color.decode("#519b52"), Color.decode("#519b52"));
        if ("done".equals(status)) {
            status = "已处理";
            backgroundColor = new JBColor(Color.decode("#519b52"), Color.decode("#519b52"));
        } else if ("checking".equals(status)) {
            status = "检查中";
            backgroundColor = new JBColor(Color.decode("#2e94c6"), Color.decode("#2e94c6"));
        } else if ("undone".equals(status)) {
            status = "待处理";
            backgroundColor = new JBColor(Color.decode("#A7C7E7"), Color.decode("#A7C7E7"));
        } else if ("failed".equals(status)) {
            status = "失败";
            backgroundColor = new JBColor(Color.decode("#FF3B30"), Color.decode("#FF3B30"));
        }

        JLabel taskStatusLabel = new JLabel(status);
        taskStatusLabel.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        taskStatusLabel.setForeground(JBColor.WHITE);
        taskStatusLabel.setOpaque(true);
        taskStatusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        taskStatusLabel.setVerticalAlignment(SwingConstants.CENTER);
        taskStatusLabel.setPreferredSize(new Dimension(60, taskStatusLabel.getPreferredSize().height));
        taskStatusLabel.setHorizontalAlignment(SwingConstants.CENTER);
        taskStatusLabel.setBackground(backgroundColor);
        taskStatusLabel.setForeground(new JBColor(Color.decode("#FFFFFF"), Color.decode("#FFFFFF")));

        gbc.gridx = 1;
        gbc.weightx = 0;
        gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.CENTER;
        gbc.insets = JBUI.insets(0, 5);
        taskInfoPanel.add(taskStatusLabel, gbc);

        gbc.fill = GridBagConstraints.HORIZONTAL;

        JLabel createTimeLabel = new JLabel(getTimeAgo(task.getCreated_at()));
        createTimeLabel.setForeground(new JBColor(0xA0A0A0, 0x707070));
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.weightx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = JBUI.insets(5, 5, 5, 20);
        gbc.gridwidth = 2;
        taskInfoPanel.add(createTimeLabel, gbc);

        panel.add(taskInfoPanel, BorderLayout.CENTER);

        if ("checking".equals(task.getStatus())) {
            JProgressBar progressBar = new JProgressBar();
            progressBar.setIndeterminate(true);
            progressBar.setPreferredSize(new Dimension(panel.getPreferredSize().width, 3));
            progressBar.setForeground(new JBColor(Color.decode("#2e94c6"), Color.decode("#2e94c6")));
            progressBar.setBorderPainted(false);
            panel.add(progressBar, BorderLayout.SOUTH);
        }

        panel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                ApplicationManager.getApplication().invokeLater(() -> {
                    if (!"failed".equals(task.getStatus())) {
                        CodeReviewTaskManager.getInstance(project).set(task);
                        ApplicationManager.getApplication().getMessageBus()
                                .syncPublisher(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC)
                                .refresh();
                    }
                });
            }

            @Override
            public void mouseEntered(MouseEvent e) {
                panel.setCursor(Cursor.getPredefinedCursor(Cursor.HAND_CURSOR));
            }

            @Override
            public void mouseExited(MouseEvent e) {
                panel.setCursor(Cursor.getDefaultCursor());
            }
        });

        panel.setOpaque(true);
        panel.setFocusable(true);
        panel.setRequestFocusEnabled(true);
        panel.setBackground(new JBColor(0xFFFFFF, 0x2B2B2B));
        return panel;
    }

    private Icon getTaskStatusIcon(String taskStatus) {
        if ("checking".equalsIgnoreCase(taskStatus)) {
            return AllIcons.General.Information;
        } else if ("done".equalsIgnoreCase(taskStatus)) {
            return AllIcons.Actions.Commit;
        } else if ("undone".equalsIgnoreCase(taskStatus)) {
            return AllIcons.Actions.Show;
        } else if ("failed".equalsIgnoreCase(taskStatus)) {
            return AllIcons.Status.FailedInProgress;
        } else {
            return null;
        }
    }

    private static String getTimeAgo(String createTime) {

        long diffInMillis;
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
            OffsetDateTime createTimeParsed = OffsetDateTime.parse(createTime, formatter);
            // Calculate the difference in milliseconds
            diffInMillis = System.currentTimeMillis() - createTimeParsed.toInstant().toEpochMilli();
        } catch (Exception e) {
            return createTime;
        }

        long diffInSeconds = TimeUnit.MILLISECONDS.toSeconds(diffInMillis);
        long diffInMinutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillis);
        long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);
        long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);
        long diffInWeeks = diffInDays / 7;
        long diffInMonths = diffInDays / 30;
        long diffInYears = diffInDays / 365;

        if (diffInSeconds < 60) {
            return diffInSeconds + "秒前";
        } else if (diffInMinutes < 60) {
            return diffInMinutes + "分钟前";
        } else if (diffInHours < 24) {
            return diffInHours + "小时前";
        } else if (diffInDays < 7) {
            return diffInDays + "天前";
        } else if (diffInWeeks < 4) {
            return diffInWeeks + "周前";
        } else if (diffInMonths < 12) {
            return diffInMonths + "个月前";
        } else if (diffInYears < 2) {
            return "1年前";
        } else {
            return diffInYears + "年前";
        }
    }

}

