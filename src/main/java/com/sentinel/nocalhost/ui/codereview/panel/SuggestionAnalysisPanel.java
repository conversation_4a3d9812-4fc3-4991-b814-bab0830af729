package com.sentinel.nocalhost.ui.codereview.panel;

import com.intellij.ui.JBColor;
import com.intellij.ui.components.JBPanel;
import com.sentinel.nocalhost.api.data.CodeReviewSuggestionInfo;
import com.sentinel.nocalhost.utils.CodeUtils;
import org.apache.commons.lang3.StringUtils;

import javax.swing.*;
import java.awt.*;

public class SuggestionAnalysisPanel extends JBPanel<SuggestionAnalysisPanel> {

    public SuggestionAnalysisPanel(CodeReviewSuggestionInfo suggestion) {
        setLayout(new BorderLayout());
        setBorder(BorderFactory.createTitledBorder(StringUtils.EMPTY));
        setIssueSummary(suggestion);

        JEditorPane editorPane = new JEditorPane();
        editorPane.setEditable(false);

        String htmlContent = CodeUtils.convertMarkdownToHtml(suggestion.getSuggestion_description());

        try {
            editorPane.setContentType("text/html");
            CodeUtils.setStyledText(editorPane, htmlContent);
        } catch (Exception ignored) {
        }

        add(editorPane, BorderLayout.CENTER);
    }

    private void setIssueSummary(CodeReviewSuggestionInfo suggestion) {
        JPanel labelJPanel = new JPanel();
        labelJPanel.setBorder(BorderFactory.createEmptyBorder());
        labelJPanel.setLayout(new BoxLayout(labelJPanel, BoxLayout.Y_AXIS));  // 使用垂直布局

        // 创建 Severity 标签并添加样式
        JBColor backgroundColor = getBackgroundColor(suggestion);
        JLabel severityLabel = getLabel(suggestion.getLevel(), backgroundColor);
        JLabel categoryLabel = getLabel(suggestion.getSuggestion_category(), backgroundColor);

        // 创建一个新的面板，确保标签在一行显示
        JPanel tagsPanel = new JPanel();
        tagsPanel.setLayout(new FlowLayout(FlowLayout.LEFT, 5, 5));
        tagsPanel.add(severityLabel);
        tagsPanel.add(categoryLabel);
        labelJPanel.add(tagsPanel);

        labelJPanel.setPreferredSize(new Dimension(-1, 30));
        labelJPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        add(labelJPanel, BorderLayout.NORTH);
    }

    private JBColor getBackgroundColor(CodeReviewSuggestionInfo suggestion) {
        JBColor backgroundColor = new JBColor(Color.decode("#A7C7E7"), Color.decode("#A7C7E7"));
        if ("BLOCKER".equals(suggestion.getLevel())) {
            backgroundColor = new JBColor(Color.decode("#FF3B30"), Color.decode("#FF3B30"));
        } else if ("CRITICAL".equals(suggestion.getLevel())) {
            backgroundColor = new JBColor(Color.decode("#FF9500"), Color.decode("#FF9500"));
        } else if ("MAJOR".equals(suggestion.getLevel())) {
            backgroundColor = new JBColor(Color.decode("#FFCC00"), Color.decode("#FFCC00"));
        } else if ("MINOR".equals(suggestion.getLevel())) {
            backgroundColor = new JBColor(Color.decode("#34C759"), Color.decode("#34C759"));
        } else if ("INFO".equals(suggestion.getLevel())) {
            backgroundColor = new JBColor(Color.decode("#A7C7E7"), Color.decode("#A7C7E7"));
        }
        return backgroundColor;
    }

    private JLabel getLabel(String suggestion, JBColor backgroundColor) {
        JLabel categoryLabel = createTagLabel(suggestion);
        categoryLabel.setBackground(backgroundColor);
        categoryLabel.setForeground(new JBColor(Color.decode("#FFFFFF"), Color.decode("#FFFFFF")));
        categoryLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        categoryLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        return categoryLabel;
    }

    private JLabel createTagLabel(String text) {
        // 创建一个标签
        JLabel label = new JLabel(text);
        label.setFont(new Font("Microsoft YaHei", Font.BOLD, 13));
        label.setForeground(JBColor.WHITE); // 设置文本为白色

        // 设置标签背景颜色
        label.setOpaque(true);

        // 设置标签的边框和内边距
        label.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));

        label.setHorizontalAlignment(SwingConstants.CENTER); // 水平方向居中
        label.setVerticalAlignment(SwingConstants.CENTER);   // 垂直方向居中

        label.setPreferredSize(new Dimension(80, 25));

        return label;
    }

}