package com.sentinel.nocalhost.ui.console;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentManager;

public final class NocalhostConsoleManager {
    public static void activateOutputWindow(Project project) {
        if (project.isDisposed()) {
            return;
        }
        ToolWindow toolWindow = ToolWindowManager.getInstance(project)
                .getToolWindow("JetDev Console");
        if (toolWindow == null) {
            return;
        }
        ApplicationManager.getApplication().invokeAndWait(() -> {
            toolWindow.activate(() -> {
                ContentManager contentManager = toolWindow.getContentManager();
                Content content = contentManager.getContent(0);
                if (content != null) {
                    contentManager.setSelectedContent(content);
                }
            });
        });
    }

}
