package com.sentinel.nocalhost.ui.console;

import com.intellij.execution.ui.ConsoleView;
import com.intellij.execution.ui.ConsoleViewContentType;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.ActionToolbar;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.sentinel.nocalhost.topic.NocalhostOutputAppendNotifier;

public class NocalhostOutputWindow extends LogsToolWindowPanel implements Disposable, NocalhostOutputAppendNotifier {

    private final Project project;
    private final ConsoleView consoleView;

    public NocalhostOutputWindow(Project project) {
        super(false);

        this.project = project;

        consoleView = new OutputConsoleView(project);
        Disposer.register(this, consoleView);
        add(consoleView.getComponent());

        DefaultActionGroup actionGroup = new DefaultActionGroup(consoleView.createConsoleActions());
        ActionToolbar actionToolbar = ActionManager.getInstance().createActionToolbar(
                "JetDev.Output.Window.Toolbar", actionGroup, false);
        actionToolbar.setTargetComponent(this);
        setToolbar(actionToolbar.getComponent());

        project.getMessageBus().connect().subscribe(
                NocalhostOutputAppendNotifier.NOCALHOST_OUTPUT_APPEND_NOTIFIER_TOPIC,
                this
        );
    }

    @Override
    public void action(String text) {
        if (project.isDisposed()) {
            return;
        }
        ApplicationManager.getApplication().invokeLater(() -> consoleView.print(text, ConsoleViewContentType.LOG_INFO_OUTPUT));
    }

    @Override
    public void dispose() {

    }


}
