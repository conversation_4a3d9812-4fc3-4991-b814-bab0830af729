package com.sentinel.nocalhost.ui.console;

import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.ui.content.*;
import com.sentinel.nocalhost.topic.NocalhostExceptionPrintNotifier;
import org.jetbrains.annotations.NotNull;

public class NocalhostConsoleWindowFactory implements ToolWindowFactory, DumbAware, NocalhostExceptionPrintNotifier {

    private Project project;
    private ToolWindow toolWindow;

    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        this.project = project;
        this.toolWindow = toolWindow;

        toolWindow.getContentManager().addContentManagerListener(new ContentManagerListener() {
            @Override
            public void contentRemoved(@NotNull ContentManagerEvent event) {
                Object component = event.getContent().getComponent();
                if (component instanceof NocalhostLogs nocalhostLogs) {
                    nocalhostLogs.terminateCommandProcess();
                }
            }
        });

        createOutputWindow();

        project.getMessageBus().connect().subscribe(
                NocalhostExceptionPrintNotifier.NOCALHOST_EXCEPTION_PRINT_NOTIFIER_TOPIC,
                this
        );
    }


    @Override
    public void action(String title, String contentMsg, String eMessage) {
        NocalhostErrorWindow nocalhostErrorWindow = new NocalhostErrorWindow(project, title, contentMsg, eMessage);
        ContentManager contentManager = toolWindow.getContentManager();
        Content content = ContentFactory.getInstance().createContent(nocalhostErrorWindow, nocalhostErrorWindow.getTitle(), false);
        content.setDisposer(nocalhostErrorWindow);
        contentManager.addContent(content);
        contentManager.setSelectedContent(content);
    }

    private void createOutputWindow() {
        NocalhostOutputWindow nocalhostOutputWindow = new NocalhostOutputWindow(project);

        ContentManager contentManager = toolWindow.getContentManager();
        Content content = ContentFactory.getInstance().createContent(nocalhostOutputWindow, "OUTPUT", false);
        content.setCloseable(false);
        content.setDisposer(nocalhostOutputWindow);
        contentManager.addContent(content);
        contentManager.setSelectedContent(content);
    }


}
