package com.sentinel.nocalhost.ui.input;

import com.intellij.ui.JBColor;

import javax.swing.*;
import java.awt.*;

public class AppSearchJTextField extends JTextField {

    private final String placeholder;

    public AppSearchJTextField(String placeholder) {
        this.placeholder = placeholder;
    }

    @Override
    protected void paintComponent(Graphics g) {
        super.paintComponent(g);
        if (getText().isEmpty() && !isFocusOwner()) {
            Graphics2D g2 = (Graphics2D) g.create();
            g2.setColor(JBColor.GRAY);
            g2.setFont(getFont().deriveFont(Font.ITALIC));
            int padding = (getHeight() - getFont().getSize()) / 2;
            g2.drawString(placeholder, getInsets().left, getHeight() - padding - 1);
            g2.dispose();
        }
    }


}
