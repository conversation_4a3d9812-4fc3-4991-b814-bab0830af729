package com.sentinel.nocalhost.ui.panel;

import javax.swing.*;
import java.awt.*;

public class APIQAPanel {

    public APIQAPanel() {

    }

    public JPanel getPanel() {
        JPanel qaPanel = new JPanel(new BorderLayout());
        JTextArea conversationArea = new JTextArea();
        conversationArea.setEditable(false);
        JTextField inputField = new JTextField();
        inputField.addActionListener(e -> {
            String input = inputField.getText();
            conversationArea.append("User: " + input + "\n");
            inputField.setText("");
            conversationArea.append("Bot: " + input + "\n");
        });
        qaPanel.add(new JScrollPane(conversationArea), BorderLayout.CENTER);
        qaPanel.add(inputField, BorderLayout.SOUTH);
        return qaPanel;
    }

}
