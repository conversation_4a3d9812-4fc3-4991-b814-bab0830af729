package com.sentinel.nocalhost.ui.panel;

import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.ui.components.JBScrollPane;
import com.intellij.ui.treeStructure.Tree;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostAppQueryManager;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;
import com.sentinel.nocalhost.ui.input.AppSearchJTextField;
import com.sentinel.nocalhost.ui.tree.NocalhostTree;

import javax.swing.*;
import java.awt.*;

public class LocalDevPanel implements Disposable  {
    private final Project project;
    private Tree tree;

    public LocalDevPanel(Project project) {
        this.project = project;
    }

    public JPanel getPanel() {
        tree = new NocalhostTree(project);
        tree.setBorder(null);


        JPanel inputPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        inputPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 5, 5));

        JLabel label = new JLabel("服务");
        AppSearchJTextField inputField = new AppSearchJTextField("");
        inputField.setColumns(10);

        inputPanel.add(label);
        inputPanel.add(inputField);

        inputField.addActionListener(e -> {
            NocalhostAppQueryManager.getInstance(project).setAppQuery(inputField.getText().trim());
            ApplicationManager.getApplication().getMessageBus()
                    .syncPublisher(NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC).action(Constants.HUMAN_OPERATE, null);
        });

        JBScrollPane scrollPane = new JBScrollPane(tree);
        scrollPane.setBorder(null);

        JPanel treePanel = new JPanel(new BorderLayout());
        treePanel.setBorder(null);
        treePanel.setBorder(BorderFactory.createEmptyBorder(0, 5, 0, 0));

        treePanel.add(inputPanel, BorderLayout.NORTH);
        treePanel.add(scrollPane, BorderLayout.CENTER);

        return treePanel;
    }



    @Override
    public void dispose() {
        if (null != tree) {
            Disposer.dispose((Disposable) tree);
        }
    }
}
