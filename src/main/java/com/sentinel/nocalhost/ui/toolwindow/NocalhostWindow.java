package com.sentinel.nocalhost.ui.toolwindow;

import com.intellij.execution.ExecutionManager;
import com.intellij.execution.process.ProcessHandler;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.actionSystem.ActionManager;
import com.intellij.openapi.actionSystem.ActionToolbar;
import com.intellij.openapi.actionSystem.DefaultActionGroup;
import com.intellij.openapi.actionSystem.Separator;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.SimpleToolWindowPanel;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostAppQueryManager;
import com.sentinel.nocalhost.service.NocalhostBinService;
import com.sentinel.nocalhost.settings.NocalhostSettings;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;
import com.sentinel.nocalhost.topic.RefreshToolWindowNotifier;
import com.sentinel.nocalhost.ui.action.toolbar.HelpAction;
import com.sentinel.nocalhost.ui.action.toolbar.LoginAccountsAction;
import com.sentinel.nocalhost.ui.action.toolbar.RefreshAction;
import com.sentinel.nocalhost.ui.action.toolbar.SetTrafficMarkAction;
import com.sentinel.nocalhost.ui.panel.LocalDevPanel;
import org.apache.commons.lang3.StringUtils;

import javax.swing.*;
import java.awt.*;
import java.util.Arrays;

public class NocalhostWindow implements RefreshToolWindowNotifier, Disposable {
    private final NocalhostSettings settings = ApplicationManager.getApplication().getService(NocalhostSettings.class);
    private final NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);

    private final Project project;
    private final SimpleToolWindowPanel mainPanel;

    public NocalhostWindow(Project project) {
        this.project = project;
        mainPanel = new SimpleToolWindowPanel(true);
        init();
    }

    private void init() {
        NocalhostAppQueryManager.getInstance(project).del();
        initToolWindowsPanel();
    }

    public void refresh() {
        mainPanel.removeAll();
        initToolWindowsPanel();
    }

    private void initToolWindowsPanel() {
        if (StringUtils.isNotEmpty(settings.getUserToken())) {
            mainPanel.setContent(new LocalDevPanel(project).getPanel());

            ApplicationManager.getApplication().getMessageBus().syncPublisher(
                    NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC).action(Constants.HUMAN_OPERATE, null);

            updateJetDevCommandTool();
        } else {
            noticeLogin();
        }
        setToolbar(getActionGroup());
    }

    private void updateJetDevCommandTool() {
        ApplicationManager.getApplication().invokeLater(()-> {
            try {
                if (StringUtils.isNotEmpty(settings.getUserToken())) {
                    NocalhostBinService nocalhostBinService = new NocalhostBinService();
                    nocalhostBinService.checkBin(project);
                    nocalhostBinService.checkVersion(project);
                    nhctlCommand.setToken(settings.getUserToken());
                }
            } catch (Exception ignored) {
            }
        });
    }

    private void noticeLogin() {
        mainPanel.setLayout(new BorderLayout());
        JPanel topPanel = new JPanel();
        topPanel.setLayout(new BoxLayout(topPanel, BoxLayout.Y_AXIS));
        JLabel textLabel = new JLabel("Please login first");
        textLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        topPanel.add(Box.createVerticalStrut(20));
        topPanel.add(textLabel);
        mainPanel.add(topPanel, BorderLayout.CENTER);
    }

    private DefaultActionGroup getActionGroup() {
        DefaultActionGroup actionGroup = new DefaultActionGroup();
        actionGroup.add(new LoginAccountsAction(project));
        if (StringUtils.isNotEmpty(settings.getUserToken())) {
            actionGroup.add(new Separator());
            actionGroup.add(new RefreshAction());
            actionGroup.add(new Separator());
            actionGroup.add(new SetTrafficMarkAction(project));
            actionGroup.add(new Separator());
            actionGroup.add(new HelpAction());
        }
        return actionGroup;
    }

    private void setToolbar(DefaultActionGroup actionGroup) {
        ActionToolbar actionToolbar = ActionManager.getInstance().createActionToolbar("JetDev.Toolbar", actionGroup, false);
        actionToolbar.setTargetComponent(mainPanel);
        mainPanel.setToolbar(actionToolbar.getComponent());
    }

    public JPanel getPanel() {
        return mainPanel;
    }

    @Override
    public void dispose() {
        try {
            var processes = ExecutionManager.getInstance(project).getRunningProcesses();
            if (processes.length > 0) {
                Arrays.stream(processes).forEach(ProcessHandler::destroyProcess);
                Thread.sleep(1000);
            }
        } catch (Exception ignored) {}
    }

}
