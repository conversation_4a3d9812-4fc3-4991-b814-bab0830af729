package com.sentinel.nocalhost.ui.toolwindow;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowFactory;
import com.intellij.ui.content.Content;
import com.intellij.ui.content.ContentFactory;
import com.sentinel.nocalhost.topic.RefreshReviewToolWindowNotifier;
import com.sentinel.nocalhost.topic.RefreshToolWindowNotifier;
import com.sentinel.nocalhost.ui.codereview.panel.ReviewWorkspacePanel;
import com.sentinel.nocalhost.intellicode.ui.IntellicodeHomePanel;
import org.jetbrains.annotations.NotNull;

public class NocalhostWindowFactory implements ToolWindowFactory, DumbAware {

    @Override
    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        // Intellicode Home 页面 - 作为第一个 tab
        IntellicodeHomePanel intellicodeHomePanel = new IntellicodeHomePanel(project);
        Content intellicodeTab = ContentFactory.getInstance().createContent(intellicodeHomePanel.getMainPanel(), "Intellicode Home", true);
        intellicodeTab.setDisposer(intellicodeHomePanel);
        
        NocalhostWindow nocalhostWindow = new NocalhostWindow(project);
        Content localDevTab = ContentFactory.getInstance().createContent(nocalhostWindow.getPanel(), "本地开发", true);
        localDevTab.setDisposer(nocalhostWindow);

        ReviewWorkspacePanel reviewWorkspacePanel = new ReviewWorkspacePanel(project);
        Content reviewTab = ContentFactory.getInstance().createContent(reviewWorkspacePanel.getMainPanel(), "啄木鸟", true);
        reviewTab.setDisposer(() -> {
            // ReviewWorkspacePanel 没有实现 Disposable，所以需要手动清理
        });

        // 按顺序添加 tabs，Intellicode Home 在最前面
        toolWindow.getContentManager().addContent(intellicodeTab);
        toolWindow.getContentManager().addContent(localDevTab);
        toolWindow.getContentManager().addContent(reviewTab);
        
        // 默认选中 Intellicode Home tab
        toolWindow.getContentManager().setSelectedContent(intellicodeTab);

         ApplicationManager.getApplication().getMessageBus().connect(nocalhostWindow)
                 .subscribe(RefreshToolWindowNotifier.REFRESH_TOOL_WINDOW_TOPIC, nocalhostWindow);

         ApplicationManager.getApplication().getMessageBus().connect(toolWindow.getDisposable())
                .subscribe(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC, reviewWorkspacePanel);
    }
}
