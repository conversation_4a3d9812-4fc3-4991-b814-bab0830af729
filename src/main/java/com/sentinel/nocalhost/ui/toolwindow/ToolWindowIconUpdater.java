package com.sentinel.nocalhost.ui.toolwindow;

import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.util.ui.UIUtil;
import icons.NocalhostIcons;

public class ToolWindowIconUpdater {

    public static void updateToolWindowIcons(Project project) {
        ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
        ToolWindow jetDev = toolWindowManager.getToolWindow("JetDev");
        ToolWindow jetDevConsole = toolWindowManager.getToolWindow("JetDev Console");

        if (jetDev != null) {
            setIcon(jetDev);
        }

        if (jetDevConsole != null) {
            setIcon(jetDevConsole);
        }
    }

    private static void setIcon(ToolWindow jetDevConsole) {
        try {
            if (UIUtil.isUnderDarcula()) {
                jetDevConsole.setIcon(NocalhostIcons.ConfigurationLogoRight);
            } else {
                jetDevConsole.setIcon(NocalhostIcons.ConfigurationLogoRightDark);
            }
        } catch (Exception ignored) {
        }
    }

}
