package com.sentinel.nocalhost.ui.notification;

import com.intellij.notification.Notification;
import com.intellij.notification.NotificationGroup;
import com.intellij.notification.NotificationGroupManager;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;

public class Notifications {


    public static void showNotification(Project project, String group, String title, String content, NotificationType type) {
        ApplicationManager.getApplication().invokeLater(() -> {
            NotificationGroup notificationGroup =
                    NotificationGroupManager.getInstance()
                            .getNotificationGroup(group);
            Notification notification =
                    notificationGroup.createNotification(
                            title,
                            content,
                            type
                    );
            notification.notify(project);
        });
    }

}
