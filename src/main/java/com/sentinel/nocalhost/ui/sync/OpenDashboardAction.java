package com.sentinel.nocalhost.ui.sync;

import com.intellij.ide.BrowserUtil;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.exception.NocalhostNotifier;
import org.jetbrains.annotations.NotNull;


public class OpenDashboardAction extends DumbAwareAction {
    private final Project project;
    private final String gui;

    public OpenDashboardAction(@NotNull Project project, @NotNull String gui) {
        super("Open Sync Dashboard");
        this.gui = gui;
        this.project = project;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent event) {
        try {
            BrowserUtil.browse("http" + "://" + gui);
        } catch (Exception ex) {
            NocalhostNotifier
                    .getInstance(project)
                    .notifyError("Failed to open sync dashboard", "Error occurred while opening sync dashboard", ex.getMessage());
        }
    }
}
