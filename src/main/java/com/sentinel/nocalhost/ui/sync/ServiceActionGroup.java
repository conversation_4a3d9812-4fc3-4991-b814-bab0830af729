package com.sentinel.nocalhost.ui.sync;

import com.google.common.collect.Lists;
import com.intellij.dvcs.ui.PopupElementWithAdditionalInfo;
import com.intellij.icons.AllIcons;
import com.intellij.openapi.actionSystem.ActionGroup;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.Separator;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.UserApp;
import icons.NocalhostIcons;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nls;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import javax.swing.*;
import java.util.List;

public class ServiceActionGroup extends ActionGroup implements PopupElementWithAdditionalInfo {
    private final Project project;

    @Setter
    private UserApp result;

    public ServiceActionGroup(@NotNull Project project, @NotNull UserApp result) {
        super(getTitle(result), true);
        this.result = result;
        this.project = project;
        var presentation = getTemplatePresentation();
        presentation.setIcon(getIcon(result));
    }

    private static @Nullable Icon getIcon(@NotNull UserApp result) {
        if (StringUtils.isEmpty(result.getSyncStatus().getStatus())) {
            return null;
        }

        return switch (result.getSyncStatus().getStatus()) {
            case "disconnected" -> AllIcons.Nodes.Pluginnotinstalled;
            case "outOfSync" -> AllIcons.General.Warning;
            case "scanning", "syncing" -> NocalhostIcons.CloudUpload;
            case "error" -> AllIcons.General.Error;
            case "idle" -> AllIcons.Actions.Commit;
            case "end" -> AllIcons.Actions.Exit;
            default -> null;
        };
    }

    private static @NotNull String getTitle(@NotNull UserApp result) {
        return result.getName();
    }

    @Override
    public AnAction @NotNull [] getChildren(@Nullable AnActionEvent e) {
        List<AnAction> actions = Lists.newArrayList();
        var status = result.getSyncStatus().getStatus();

        if (null == status) {
            status = StringUtils.EMPTY;
        }

        actions.add(new Separator("[" + result.getCluster() + "/" + result.getNamespace() + "]"));
        switch (status) {
            case "disconnected", "end", "":
                break;
            default:
                actions.add(new OverrideSyncAction(project, result));
                break;
        }
        if (StringUtils.isNotEmpty(result.getSyncStatus().getGui())) {
            actions.add(new OpenDashboardAction(project, result.getSyncStatus().getGui()));
        }
        return actions.toArray(new AnAction[0]);
    }

    @Override
    public @Nls @Nullable String getInfoText() {
        return result.getSyncStatus().getMsg();
    }
}
