package com.sentinel.nocalhost.ui.sync;

import com.intellij.dvcs.ui.BranchActionGroupPopup;
import com.intellij.ide.DataManager;
import com.intellij.openapi.actionSystem.ActionGroup;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.wm.WindowManager;
import com.intellij.ui.popup.PopupFactoryImpl;

import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.topic.NocalhostSyncUpdateNotifier;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class NocalhostSyncPopup {
    private final Project project;
    private final ActionGroup actions;
    private NocalhostSyncPopup(@NotNull Project project, @NotNull ActionGroup actions) {
        this.project = project;
        this.actions = actions;
    }

    public static NocalhostSyncPopup getInstance(@NotNull Project project, @NotNull ActionGroup actions) {
        return new NocalhostSyncPopup(project, actions);
    }

    public BranchActionGroupPopup asListPopup() {
        var statusBar = WindowManager.getInstance().getStatusBar(project);
        var popup = new BranchActionGroupPopup("File Sync Manage", project,
                (action) -> false, actions, "File.Sync.Manage", DataManager.getInstance().getDataContext(statusBar.getComponent()));
        project.getMessageBus().connect(popup).subscribe(
                NocalhostSyncUpdateNotifier.NOCALHOST_SYNC_UPDATE_NOTIFIER_TOPIC,
                (NocalhostSyncUpdateNotifier) results -> update(popup, results)
        );
        return popup;
    }

    private void update(@NotNull BranchActionGroupPopup popup, @NotNull List<UserApp> results) {
        List<Object> items = popup.getListStep().getValues();
        items.forEach(x -> {
            var item = (PopupFactoryImpl.ActionItem) x;
            if (item.getAction() instanceof ServiceActionGroup group) {
                results.forEach(group::setResult);
            }
        });
        popup.update();
    }
}
