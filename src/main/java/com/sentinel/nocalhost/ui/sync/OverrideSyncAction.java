package com.sentinel.nocalhost.ui.sync;

import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.DumbAwareAction;
import com.intellij.openapi.project.Project;

import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.commands.NhctlCommand;
import com.sentinel.nocalhost.commands.data.NhctlSyncStatusOptions;
import com.sentinel.nocalhost.utils.ErrorUtil;
import org.jetbrains.annotations.NotNull;


public class OverrideSyncAction extends DumbAwareAction {
    private final NhctlCommand nhctlCommand = ApplicationManager.getApplication().getService(NhctlCommand.class);
    private final Project project;
    private final UserApp result;

    public OverrideSyncAction(@NotNull Project project, @NotNull UserApp result) {
        super("Override Remote Files");
        this.result = result;
        this.project = project;
    }

    @Override
    public void actionPerformed(@NotNull AnActionEvent event) {
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                NhctlSyncStatusOptions nhctlSyncStatusOptions =
                        new NhctlSyncStatusOptions(String.valueOf(result.getId()), result.getCluster(), result.getNamespace());
                nhctlSyncStatusOptions.setOverride(true);
                nhctlCommand.syncStatus(nhctlSyncStatusOptions);
            } catch (Exception ex) {
                ErrorUtil.dealWith(project, "Failed to override sync", "Error occurred while override sync.", ex);
            }
        });
    }
}
