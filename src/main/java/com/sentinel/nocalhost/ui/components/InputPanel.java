package com.sentinel.nocalhost.ui.components;

import com.intellij.util.ui.JBUI;

import javax.swing.*;
import java.awt.*;

public class InputPanel {
    public static JPanel getInputPanel(JTextField inputField) {
        JPanel panel = new JPanel(new GridBagLayout());

        GridBagConstraints gbc = getGridBagConstraints();

        panel.add(inputField, gbc);

        // Add the additional sentence below the input field
        JLabel infoLabel = new JLabel("按\"Enter\"以确认或按\"ESC\"以取消");
        gbc.gridy = 1;
        gbc.anchor = GridBagConstraints.LINE_END;
        gbc.insets = JBUI.insets(0, 5, 10, 10);  // Adjust insets for spacing and alignment
        panel.add(infoLabel, gbc);
        return panel;
    }

    public static GridBagConstraints getGridBagConstraints() {
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.insets = JBUI.insets(5);
        return gbc;
    }
}
