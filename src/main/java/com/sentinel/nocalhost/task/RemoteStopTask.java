package com.sentinel.nocalhost.task;

import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.RunningApp;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.api.data.UserProject;
import com.sentinel.nocalhost.commands.OutputCapturedNhctlCommand;
import com.sentinel.nocalhost.commands.data.NhctlDevEndOptions;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.service.NocalhostAppListManager;
import com.sentinel.nocalhost.service.NocalhostContextManager;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;
import com.sentinel.nocalhost.topic.StopProgressIndicatorNotifier;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public class RemoteStopTask extends BaseBackgroundTask {
    private final UserAppNode userAppNode;
    private final Project project;
    private final OutputCapturedNhctlCommand outputCapturedNhctlCommand;


    public RemoteStopTask(Project project, String title, UserAppNode userAppNode) {
        super(project, title, false, userAppNode);
        this.project = project;
        this.userAppNode = userAppNode;
        this.outputCapturedNhctlCommand = project.getService(OutputCapturedNhctlCommand.class);
    }

    @SneakyThrows
    @Override
    public void runTask(@NotNull ProgressIndicator indicator) {

        stopProcess();

        NhctlDevEndOptions nhctlDevEndOptions = new NhctlDevEndOptions(
                String.valueOf(userAppNode.getUserApp().getId()), userAppNode.getUserApp().getCluster(),
                userAppNode.getUserApp().getNamespace());
        outputCapturedNhctlCommand.devEnd(nhctlDevEndOptions);

        closeAppTerminal(userAppNode);

        updateContext();

        ApplicationManager.getApplication().invokeLater(
                () -> ApplicationManager.getApplication().getMessageBus()
                        .syncPublisher(StopProgressIndicatorNotifier.REFRESH_TOOL_WINDOW_TOPIC)
                        .stop(userAppNode.getUserApp().getId()));
    }

    private void updateContext() {

        UserApp context = NocalhostContextManager.getInstance(project).getContext();
        if (null != context) {
            if (context.getId() == userAppNode.getUserApp().getId()) {
                NocalhostContextManager.getInstance(project).setContext(null);
            }
        }

        context = NocalhostContextManager.getInstance(project).getContext();

        if (null == context) {
            List<RunningApp> runningApps = NocalhostAppListManager.getInstance(project).getRunningAppList();
            if (null != runningApps && !runningApps.isEmpty()) {
                NocalhostContextManager.getInstance(project).setContext(runningApps.get(0).getUserApp());
            } else {
                List<UserProject> userProjectList = NocalhostAppListManager.getInstance(project).get();
                if (null != userProjectList) {
                    for (UserProject userProject : userProjectList) {
                        if (null != userProject.getApps()) {
                            for (UserApp userApp : userProject.getApps()) {
                                if (Constants.DEBUG_STATUS_SUCCESSFUL.equals(userApp.getStatus())
                                        && userApp.getId() != userAppNode.getUserApp().getId()) {
                                    NocalhostContextManager.getInstance(project).setContext(userApp);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    @Override
    public void onSuccess() {
        super.onSuccess();
        ApplicationManager.getApplication().getMessageBus()
                .syncPublisher(NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC)
                .action(Constants.HUMAN_OPERATE, null);
    }

}
