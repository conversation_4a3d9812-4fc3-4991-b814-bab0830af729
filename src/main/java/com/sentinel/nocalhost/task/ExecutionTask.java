package com.sentinel.nocalhost.task;

import com.intellij.execution.*;
import com.intellij.execution.configurations.ConfigurationType;
import com.intellij.execution.executors.DefaultDebugExecutor;
import com.intellij.execution.executors.DefaultRunExecutor;
import com.intellij.execution.runners.ExecutionEnvironmentBuilder;
import com.intellij.openapi.application.ApplicationInfo;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.project.Project;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.commands.OutputCapturedNhctlCommand;
import com.sentinel.nocalhost.commands.data.NhctlPortForwardStartOptions;
import com.sentinel.nocalhost.commands.data.NhctlRawConfig;
import com.sentinel.nocalhost.commands.data.NhctlSyncStatus;
import com.sentinel.nocalhost.commands.data.NhctlSyncStatusOptions;
import com.sentinel.nocalhost.configuration.go.NocalhostGoConfigurationType;
import com.sentinel.nocalhost.configuration.java.NocalhostJavaConfigurationType;
import com.sentinel.nocalhost.configuration.python.NocalhostPythonConfiguration;
import com.sentinel.nocalhost.configuration.python.NocalhostPythonConfigurationType;
import com.sentinel.nocalhost.exception.NocalhostExecuteCmdException;
import com.sentinel.nocalhost.service.NocalhostContextManager;
import com.sentinel.nocalhost.service.NocalhostIdeHubApIService;
import com.sentinel.nocalhost.topic.StopProgressIndicatorNotifier;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import com.sentinel.nocalhost.utils.DataUtils;
import com.sentinel.nocalhost.utils.ErrorUtil;
import com.sentinel.nocalhost.utils.NhctlUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.net.ServerSocket;
import java.util.HashMap;
import java.util.Map;

public class ExecutionTask extends BaseBackgroundTask implements StopProgressIndicatorNotifier {
    public final static String kRun = "Run";
    public final static String kRunBackground = "Run-Background";
    public final static String kDebug = "Debug";
    ProgressIndicator indicator;
    private final NocalhostIdeHubApIService hubApIService = ApplicationManager.getApplication().getService(NocalhostIdeHubApIService.class);
    private final OutputCapturedNhctlCommand outputCapturedNhctlCommand;


    private final String action;
    private final Project project;
    private final UserAppNode userAppNode;
    private final Map<String, ConfigurationType> code2conf = new HashMap<>() {
        {
            put("GO", new NocalhostGoConfigurationType());
            put("IC", new NocalhostJavaConfigurationType());
            put("IU", new NocalhostJavaConfigurationType());
            put("PY", new NocalhostPythonConfigurationType());
        }
    };

    private final Map<String, ConfigurationType> lang2conf = new HashMap<>() {
        {
            put("go", new NocalhostGoConfigurationType());
            put("java", new NocalhostJavaConfigurationType());
            put("python", new NocalhostPythonConfigurationType());
        }
    };

    public ExecutionTask(Project project, String action, UserAppNode userAppNode) {
        super(project, userAppNode.getUserApp().getName() + " " + action, true, userAppNode);
        this.action = action;
        this.project = project;
        this.userAppNode = userAppNode;
        this.outputCapturedNhctlCommand = project.getService(OutputCapturedNhctlCommand.class);

        project.getMessageBus().connect().subscribe(
                StopProgressIndicatorNotifier.REFRESH_TOOL_WINDOW_TOPIC,
                this
        );
    }

    @Override
    public void onThrowable(@NotNull Throwable ex) {
        ErrorUtil.dealWith(this.getProject(), "Failed to start " + action,
                String.format("Error occurred while starting `%s`", action), ex);
        if (null != indicator) {
            try {
                if (!indicator.isCanceled()) {
                    indicator.cancel();
                }
                if (indicator.isRunning()) {
                    indicator.stop();
                }
            } catch (Exception ignored) {
            }
        }
        stopFakerRunning();
        // stop(userAppNode);
    }

    @Override
    public void onSuccess() {
        super.onSuccess();
        var context = NocalhostContextManager.getInstance(project).getContext();
        hubApIService.openRemoteTerminal(project, context);
    }

    @Override
    @SneakyThrows
    public void runTask(@NotNull ProgressIndicator indicator) {
        this.indicator = indicator;
        startAndSync(userAppNode, outputCapturedNhctlCommand);
        UserApp userApp = userAppNode.getUserApp();
        NocalhostContextManager.getInstance(project).setContext(userApp);
        waitSync(userApp);
    }

    private void waitSync(UserApp userApp) throws InterruptedException, NocalhostExecuteCmdException, IOException {
        NhctlSyncStatusOptions nhctlSyncStatusOptions =
                new NhctlSyncStatusOptions(String.valueOf(userApp.getId()), userApp.getCluster(), userApp.getNamespace());
        nhctlSyncStatusOptions.setWait(true);
        String status = outputCapturedNhctlCommand.syncStatus(nhctlSyncStatusOptions);
        var syncStatus = DataUtils.GSON.fromJson(status, NhctlSyncStatus.class);
        userApp.setSyncStatus(syncStatus);
        NocalhostContextManager.getInstance(project).setContext(userApp);

        if ("idle".equals(syncStatus.getStatus())) {

            nhctlSyncStatusOptions.setOverride(true);
            nhctlSyncStatusOptions.setWait(false);
            outputCapturedNhctlCommand.syncStatus(nhctlSyncStatusOptions);

            nhctlSyncStatusOptions.setWait(true);
            nhctlSyncStatusOptions.setOverride(false);
            status = outputCapturedNhctlCommand.syncStatus(nhctlSyncStatusOptions);
            syncStatus = DataUtils.GSON.fromJson(status, NhctlSyncStatus.class);

            if ("idle".equals(syncStatus.getStatus())) {
                ApplicationManager.getApplication().invokeLater(this::doRun);
            }
        }
    }

    @Override
    public void stop(int appId) {
        if (userAppNode.getUserApp().getId() == appId) {
            if (!indicator.isCanceled()) {
                indicator.cancel();
            }
        }
    }

    private Executor getExecutor() {
        if (kDebug.equals(action)) {
            return DefaultDebugExecutor.getDebugExecutorInstance();
        }
        return DefaultRunExecutor.getRunExecutorInstance();
    }

    @SneakyThrows
    protected void doRun() {
        // kill active session
        stopProcess();

        Thread.sleep(500);

        // start new session
        var builder = ExecutionEnvironmentBuilder.createOrNull(getExecutor(), getConf());
        if (builder != null) {
            ExecutionManager.getInstance(project).restartRunProfile(builder.build());
        }
    }

    @SneakyThrows
    private @NotNull RunnerAndConfigurationSettings getConf() {
        var manager = RunManager.getInstance(project);
        RunnerAndConfigurationSettings conf =
                manager.createConfiguration(userAppNode.getUserApp().getName(),
                        getConfType().getConfigurationFactories()[0]);
        manager.addConfiguration(conf);
        // Python
        if (conf.getType() instanceof NocalhostPythonConfigurationType) {
            var port = getDebugPort();
            var pyConf = (NocalhostPythonConfiguration) conf.getConfiguration();
            pyConf.setPort(port);
            pyConf.setHost("127.0.0.1");
            conf = (RunnerAndConfigurationSettings) pyConf;
        }
        manager.setSelectedConfiguration(conf);
        return conf;
    }

    private NhctlRawConfig getDevConfig() throws ExecutionException {
        var context = NocalhostContextManager.getInstance(project).getContext();
        if (null != context) {
            return NhctlUtil.getDevConfig(context);
        }
        return null;
    }

    private ConfigurationType getConfType() throws ExecutionException {
        var config = getDevConfig();
        if (config != null && !config.getContainers().isEmpty()) {
            String lang = "";
            try {
                lang = config.getContainers().get(0).getDev().getDebug().getLanguage().toLowerCase();
            } catch (Exception ex) {
                // ignore
            }
            if (StringUtils.isNotEmpty(lang)) {
                var conf = lang2conf.getOrDefault(lang, null);
                if (conf == null) {
                    throw new ExecutionException(String.format("Failed to create configuration, language: %s.", lang));
                }
                return conf;
            }
        }
        var code = ApplicationInfo.getInstance().getBuild().getProductCode();
        var conf = code2conf.getOrDefault(code, null);
        if (conf == null) {
            throw new ExecutionException(String.format("Failed to create configuration, IDE: %s.", code));
        }
        return conf;
    }

    private @NotNull String getDebugPort() throws InterruptedException, NocalhostExecuteCmdException, IOException {
        UserApp userApp =  NocalhostContextManager.getInstance(project).getContext();
        String localPort;
        try (ServerSocket socket = new ServerSocket(0)) {
            localPort = String.valueOf(socket.getLocalPort());
        }
        NhctlPortForwardStartOptions portForwardStartOptions =
                new NhctlPortForwardStartOptions(String.valueOf(userApp.getId()),
                        userApp.getCluster(), userApp.getNamespace());
        portForwardStartOptions.setPort(localPort);
        outputCapturedNhctlCommand.startPortForward(portForwardStartOptions);
        return localPort;
    }

}
