package com.sentinel.nocalhost.task;

import com.intellij.notification.NotificationType;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.progress.ProgressIndicator;
import com.intellij.openapi.progress.Task;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.NlsContexts;
import com.intellij.terminal.TerminalTitle;
import com.intellij.terminal.ui.TerminalWidget;
import com.intellij.util.concurrency.Semaphore;
import com.sentinel.nocalhost.api.data.UserApp;
import com.sentinel.nocalhost.commands.OutputCapturedNhctlCommand;
import com.sentinel.nocalhost.commands.data.NhctlDevStartOptions;
import com.sentinel.nocalhost.commands.data.NhctlSyncOptions;
import com.sentinel.nocalhost.configuration.NocalhostDevProcessHandler;
import com.sentinel.nocalhost.constants.Constants;
import com.sentinel.nocalhost.exception.NocalhostExecuteCmdException;
import com.sentinel.nocalhost.service.JetDevExecutionManager;
import com.sentinel.nocalhost.service.NocalhostAppListManager;
import com.sentinel.nocalhost.service.ProgressProcessManager;
import com.sentinel.nocalhost.topic.NocalhostTreeUpdateNotifier;
import com.sentinel.nocalhost.ui.notification.Notifications;
import com.sentinel.nocalhost.ui.tree.node.UserAppNode;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.plugins.terminal.TerminalToolWindowManager;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

public abstract class BaseBackgroundTask extends Task.Backgroundable {
    private final ProgressProcessManager progressProcessManager = ApplicationManager
            .getApplication().getService(ProgressProcessManager.class);
    private static final Logger LOG = Logger.getInstance(BaseBackgroundTask.class);

    private final AtomicReference<Throwable> throwableAtomicReference = new AtomicReference<>(null);

    private final Project project;
    private final UserAppNode userAppNode;
    private ProgressIndicator indicator;

    public BaseBackgroundTask(Project project,
                              @NlsContexts.ProgressTitle @NotNull String title,
                              boolean canBeCancelled, UserAppNode userAppNode) {
        super(project, title, canBeCancelled);
        this.project = project;
        this.userAppNode = userAppNode;
    }

    @Override
    public void onCancel() {
        UserApp runningApp = NocalhostAppListManager.getInstance(project).getRunningApp(userAppNode.getUserApp().getId());
        if (null != runningApp) {
            appRunningNotice();
            return;
        }
        super.onCancel();
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            List<Process> processList = progressProcessManager.get(BaseBackgroundTask.this);
            if (processList == null) {
                return;
            }
            for (Process process : processList) {
                if (process.isAlive()) {
                    ApplicationManager.getApplication().executeOnPooledThread(process::destroy);
                }
            }
            progressProcessManager.del(BaseBackgroundTask.this);
        });
        if (!indicator.isCanceled()) {
            indicator.cancel();
        }
    }

    @Override
    public void onFinished() {
        super.onFinished();
        ApplicationManager.getApplication().executeOnPooledThread(() ->
                progressProcessManager.del(BaseBackgroundTask.this));
        if (!indicator.isCanceled()) {
            indicator.cancel();
        }
        if (indicator.isRunning()) {
            indicator.stop();
        }
    }

    public abstract void runTask(@NotNull ProgressIndicator indicator);

    @SneakyThrows
    @Override
    public final void run(@NotNull ProgressIndicator indicator) {
        this.indicator = indicator;
        final Semaphore semaphore = new Semaphore();
        semaphore.down();

        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                runTask(indicator);
            } catch (Throwable e) {
                stopFakerRunning();
                if (!indicator.isCanceled()) {
                    indicator.cancel();
                }
                throwableAtomicReference.set(e);
            } finally {
                semaphore.up();
            }
        });

        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            while (true) {
                if (indicator.isCanceled() || !indicator.isRunning()) {
                    semaphore.up();
                    break;
                }
                try {
                    //noinspection SynchronizeOnThis
                    synchronized (this) {
                        //noinspection SynchronizeOnThis
                        wait(500L);
                    }
                } catch (InterruptedException ignore) {
                }
            }
        });

        semaphore.waitFor();

        if (throwableAtomicReference.get() != null) {
            throw throwableAtomicReference.get();
        }
    }

    public void stopFakerRunning() {
        try {
            NocalhostAppListManager.getInstance(project).delRunningAppById(userAppNode.getUserApp().getId());
            ApplicationManager.getApplication().getMessageBus()
                    .syncPublisher(NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC)
                    .action(StringUtils.EMPTY, null);
        } catch (Exception ignored) {
        }
    }

    public void stopProcess() {
        List<NocalhostDevProcessHandler> processHandlers = JetDevExecutionManager.getInstance(project).get();
        for (NocalhostDevProcessHandler processHandler : processHandlers) {
            if (userAppNode.getUserApp().getId() == processHandler.appId) {
                if (!processHandler.isProcessTerminating()
                        && !processHandler.isProcessTerminated()) {
                    processHandler.destroyProcess();
                }
            }
        }
    }

    public void startAndSync(UserAppNode userAppNode, OutputCapturedNhctlCommand outputCapturedNhctlCommand)
            throws IOException, NocalhostExecuteCmdException, InterruptedException {
        if (!Constants.DEBUG_STATUS_SUCCESSFUL.equals(userAppNode.getUserApp().getStatus())) {
            indicator.setText(userAppNode.getUserApp().getName() + " Deploying");
            start(userAppNode, outputCapturedNhctlCommand);
            stopFakerRunning();
            indicator.setText(userAppNode.getUserApp().getName() + " Syncing");
            sync(userAppNode, outputCapturedNhctlCommand);
        }
    }

    public void closeAppTerminal(UserAppNode userAppNode) {
        ApplicationManager.getApplication().invokeLater(()-> {
            TerminalToolWindowManager terminalToolWindowManager =  TerminalToolWindowManager.getInstance(project);
            Set<TerminalWidget> terminalWidgets = terminalToolWindowManager.getTerminalWidgets();
            List<TerminalWidget> terminalWidgetList = new ArrayList<>(terminalWidgets);
            for (TerminalWidget widget : terminalWidgetList) {
                TerminalTitle terminalTitle = widget.getTerminalTitle();
                assert terminalTitle.getDefaultTitle() != null;
                if (terminalTitle.getDefaultTitle().contains(userAppNode.getUserApp().getName())) {
                    Objects.requireNonNull(terminalToolWindowManager.getContainer(widget)).closeAndHide();
                }
            }
        });
    }

    private void sync(UserAppNode userAppNode, OutputCapturedNhctlCommand outputCapturedNhctlCommand) throws IOException, NocalhostExecuteCmdException, InterruptedException {
        NhctlSyncOptions nhctlSyncOptions = new NhctlSyncOptions(
                String.valueOf(userAppNode.getUserApp().getId()), userAppNode.getUserApp().getCluster(),
                userAppNode.getUserApp().getNamespace());
        String syncResult = outputCapturedNhctlCommand.sync(nhctlSyncOptions);
        LOG.info(syncResult);
    }

    private void start(UserAppNode userAppNode, OutputCapturedNhctlCommand outputCapturedNhctlCommand) throws IOException, NocalhostExecuteCmdException, InterruptedException {
        NhctlDevStartOptions nhctlDevStartOptions = new NhctlDevStartOptions(
                String.valueOf(userAppNode.getUserApp().getId()), userAppNode.getUserApp().getCluster(),
                userAppNode.getUserApp().getNamespace());
        String startResult = outputCapturedNhctlCommand.devStart(nhctlDevStartOptions);
        LOG.info(startResult);
    }

    private void appRunningNotice() {
        ApplicationManager.getApplication().invokeLater(
                () -> Notifications.showNotification(
                        project,
                        Constants.NHCTL_NOTIFICATION_GROUP,
                        "服务正在部署中，禁止操作",
                        "请等待服务部署完成，如遇异常情况未完成部署，请等待两分钟手动停止。",
                        NotificationType.INFORMATION
                ));
    }

}
