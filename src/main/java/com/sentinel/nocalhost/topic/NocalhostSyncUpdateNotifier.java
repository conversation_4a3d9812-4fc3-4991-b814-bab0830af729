package com.sentinel.nocalhost.topic;

import com.intellij.util.messages.Topic;
import com.sentinel.nocalhost.api.data.UserApp;
import org.jetbrains.annotations.NotNull;

import java.util.List;

public interface NocalhostSyncUpdateNotifier {
    @Topic.ProjectLevel
    Topic<NocalhostSyncUpdateNotifier> NOCALHOST_SYNC_UPDATE_NOTIFIER_TOPIC =
            new Topic<>(NocalhostSyncUpdateNotifier.class);

    void action(@NotNull List<UserApp> results);
}
