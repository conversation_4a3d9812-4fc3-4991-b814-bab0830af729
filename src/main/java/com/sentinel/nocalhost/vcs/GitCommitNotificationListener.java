package com.sentinel.nocalhost.vcs;

import com.intellij.notification.NotificationGroupManager;
import com.intellij.notification.NotificationType;
import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vcs.CheckinProjectPanel;
import com.intellij.openapi.vcs.changes.CommitContext;
import com.intellij.openapi.vcs.checkin.CheckinHandler;
import com.intellij.openapi.vcs.checkin.CheckinHandlerFactory;
import com.intellij.openapi.wm.ToolWindow;
import com.intellij.openapi.wm.ToolWindowManager;
import com.intellij.ui.content.Content;
import com.sentinel.nocalhost.intellicode.ui.IntellicodeHomePanel;
import org.jetbrains.annotations.NotNull;

/**
 * Git Commit 通知监听器
 * 当用户进行Git commit操作后，在右下角弹出通知，建议用户使用啄木鸟分析提交的代码
 */
public class GitCommitNotificationListener extends CheckinHandlerFactory {

    @NotNull
    @Override
    public CheckinHandler createHandler(@NotNull CheckinProjectPanel panel, @NotNull CommitContext commitContext) {
        return new GitCommitHandler(panel.getProject());
    }

    /**
     * Git Commit 处理器
     */
    private static class GitCommitHandler extends CheckinHandler {
        private final Project project;

        public GitCommitHandler(@NotNull Project project) {
            this.project = project;
        }

        @Override
        public void checkinSuccessful() {
            // 在commit成功后显示通知
            showCommitNotification();
        }

        /**
         * 显示commit成功后的通知
         */
        private void showCommitNotification() {
            ApplicationManager.getApplication().invokeLater(() -> {
                try {
                    // 创建通知消息（不包含HTML链接，使用Action按钮代替）
                    String message = "你提交了代码，让AI Review一下吧";
                    
                    // 使用新的通知API，不使用废弃的setListener方法
                    NotificationGroupManager.getInstance()
                        .getNotificationGroup("Intellicode Notifications")
                        .createNotification(
                            "JetDev",
                            message,
                            NotificationType.INFORMATION
                        )
                        .addAction(new AnAction("Vibe Review") {
                            @Override
                            public void actionPerformed(@NotNull AnActionEvent e) {
                                // 打开Intellicode工具窗口
                                openIntellicodeWindow();
                            }
                        })
                        .notify(project);
                        
                } catch (Exception e) {
                    // 记录异常日志，但不影响正常的commit流程
                    System.err.println("Error showing commit notification: " + e.getMessage());
                    e.printStackTrace();
                }
            });
        }

        /**
         * 打开Intellicode工具窗口并切换到committed视图
         */
        private void openIntellicodeWindow() {
            try {
                ToolWindowManager toolWindowManager = ToolWindowManager.getInstance(project);
                ToolWindow jetDevWindow = toolWindowManager.getToolWindow("JetDev");
                
                if (jetDevWindow != null) {
                    // 激活JetDev工具窗口
                    jetDevWindow.activate(() -> {
                        // 查找Intellicode Home tab
                        Content[] contents = jetDevWindow.getContentManager().getContents();
                        for (Content content : contents) {
                            if ("Intellicode Home".equals(content.getDisplayName())) {
                                // 切换到Intellicode Home tab
                                jetDevWindow.getContentManager().setSelectedContent(content);
                                
                                // 获取IntellicodeHomePanel并切换到committed视图
                                javax.swing.JComponent component = content.getComponent();
                                IntellicodeHomePanel homePanel = findHomePanelInComponent(component);
                                if (homePanel != null) {
                                    homePanel.switchToCommittedView();
                                }
                                break;
                            }
                        }
                    });
                }
                
            } catch (Exception e) {
                // 记录异常日志
                System.err.println("Error opening Intellicode window: " + e.getMessage());
                e.printStackTrace();
            }
        }

        /**
         * 在组件树中查找 IntellicodeHomePanel 实例
         */
        private IntellicodeHomePanel findHomePanelInComponent(javax.swing.JComponent component) {
            // 检查当前组件是否包含 IntellicodeHomePanel
            if (component instanceof javax.swing.JPanel) {
                javax.swing.JPanel panel = (javax.swing.JPanel) component;
                
                // 检查是否是 SimpleToolWindowPanel，这是 IntellicodeHomePanel 的主容器
                if (panel instanceof com.intellij.openapi.ui.SimpleToolWindowPanel) {
                    // 尝试通过客户端属性获取 IntellicodeHomePanel 实例
                    Object homePanelRef = panel.getClientProperty("IntellicodeHomePanel");
                    if (homePanelRef instanceof IntellicodeHomePanel) {
                        return (IntellicodeHomePanel) homePanelRef;
                    }
                }
            }
            
            // 递归搜索子组件
            if (component instanceof java.awt.Container) {
                java.awt.Container container = (java.awt.Container) component;
                for (java.awt.Component child : container.getComponents()) {
                    if (child instanceof javax.swing.JComponent) {
                        IntellicodeHomePanel found = findHomePanelInComponent((javax.swing.JComponent) child);
                        if (found != null) {
                            return found;
                        }
                    }
                }
            }
            
            return null;
        }
    }
}