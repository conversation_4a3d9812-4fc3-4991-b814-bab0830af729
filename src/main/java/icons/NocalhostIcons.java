package icons;

import com.intellij.openapi.util.IconLoader;

import javax.swing.*;

public interface NocalhostIcons {

    Icon ConfigurationLogo = IconLoader.getIcon("/icons/configuration-logo.svg", NocalhostIcons.class);
    Icon ConfigurationLogoRight = IconLoader.getIcon("/icons/logo-right.svg", NocalhostIcons.class);
    Icon ConfigurationLogoRightDark = IconLoader.getIcon("/icons/logo-right-dark.svg", NocalhostIcons.class);

    Icon Login = IconLoader.getIcon("/icons/login.svg", NocalhostIcons.class);
    Icon LoginDark = IconLoader.getIcon("/icons/login-dark.svg", NocalhostIcons.class);

    Icon Golang = IconLoader.getIcon("/icons/golang.svg", NocalhostIcons.class);
    Icon Python = IconLoader.getIcon("/icons/python.svg", NocalhostIcons.class);
    Icon Java = IconLoader.getIcon("/icons/java.svg", NocalhostIcons.class);


    interface Status {
        Icon Running = IconLoader.getIcon("/icons/status_running.svg", NocalhostIcons.class);
        Icon Unknown = IconLoader.getIcon("/icons/status_unknown.svg", NocalhostIcons.class);
        Icon Normal = IconLoader.getIcon("/icons/status_normal.svg", NocalhostIcons.class);
        Icon Failed = IconLoader.getIcon("/icons/status-failed.svg", NocalhostIcons.class);
        Icon Deploying = IconLoader.getIcon("/icons/loading.svg", NocalhostIcons.class);
    }

    Icon CloudUpload = IconLoader.getIcon("/icons/cloud_upload.svg", NocalhostIcons.class);

}
