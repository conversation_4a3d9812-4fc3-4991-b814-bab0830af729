syntax = "proto3";

package intellicode.api.v1;

option go_package = "intellicode/pkg/proto/api";

import "google/protobuf/descriptor.proto";
import "google/api/annotations.proto";
import "event.proto";

// 1. 声明扩展字段，附加到 MethodOptions（RPC 方法的 option）
extend google.protobuf.MethodOptions {
  bool sse_enabled = 50001;
}

enum IDE {
  IDE_UNSPECIFIED = 0;
  IDE_JETBRAINS = 1;
  IDE_VSCODE = 2;
}

// ReviewStatus 审查状态枚举
enum ReviewStatus {
  REVIEW_STATUS_UNSPECIFIED = 0;
  REVIEW_STATUS_UNDONE = 1;      // 未处理
  REVIEW_STATUS_DONE = 2;        // 已处理
  REVIEW_STATUS_CHECKING = 3;    // 检查中
  REVIEW_STATUS_FAILED = 4;      // 失败
}

// SuggestionStatus 建议状态枚举
enum SuggestionStatus {
  SUGGESTION_STATUS_UNSPECIFIED = 0;
  SUGGESTION_STATUS_UNDONE = 1;      // 未处理
  SUGGESTION_STATUS_DONE = 2;        // 已处理
}

// FeedbackAction 反馈操作枚举
enum FeedbackAction {
  FEEDBACK_ACTION_UNSPECIFIED = 0;
  FEEDBACK_ACTION_REJECT = 1;        // 不采纳
  FEEDBACK_ACTION_AGREE = 2;         // 采纳
}

message BaseReq {
    string cicd_token = 1;
}

// ReviewRequest 代码审查请求
message ReviewRequest {
  BaseReq base_req = 1;
  // 仓库路径，绝对路径
  string repo_path = 2;
  // 需要审查的文件列表，如果为空则审查所有变更文件
  repeated string review_files = 3;
  IDE ide = 4;
}

// ListReviewRecordsRequest 查询审查记录请求
message ListReviewRecordsRequest {
  BaseReq base_req = 1;
  // 仓库路径
  string repo_path = 2;
  // 分页限制
  int32 limit = 3;
}

// ReviewRecord 审查记录
message ReviewRecord {
  // 审查记录 ID
  int32 review_id = 1;
  // 会话 ID
  string session_id = 2;
  // 仓库路径
  string repo_path = 3;
  // 审查状态
  ReviewStatus status = 4;
  // 触发者
  string trigger_by = 5;
  // 邮箱
  string email = 6;
  // 创建时间
  int64 created_at = 7;
}

// ListReviewRecordsResponse 查询审查记录响应
message ListReviewRecordsResponse {
  repeated ReviewRecord records = 1;
  // 实际返回的记录数量
  int32 count = 2;
}

// GetReviewRecordEventsRequest 获取审查事件请求
message GetReviewRecordEventsRequest {
  BaseReq base_req = 1;
  // 仓库路径
  string repo_path = 2;
  // 会话 ID
  string session_id = 3;
}

// GetReviewRecordEventsResponse 获取审查事件响应
message GetReviewRecordEventsResponse {
  // 事件列表
  repeated intellicode.event.v1.Event events = 1;
  // 会话信息
  string session_id = 2;
  string repo_path = 3;
  int64 created_at = 4;
  // 审查的文件列表
  repeated string review_files = 5;
}

// GetReviewRecordSuggestionsRequest 获取审查建议请求
message GetReviewRecordSuggestionsRequest {
  BaseReq base_req = 1;
  // 审查记录 ID
  string review_id = 2;
}

// ReviewSuggestion 审查建议
message ReviewSuggestion {
  // 建议 ID
  int32 id = 1;
  // 智能代码审查 ID
  int32 intellicode_review_id = 2;
  // 文件路径
  string file_path = 3;
  // 起始行号
  int32 start_line_number = 4;
  // 结束行号
  int32 end_line_number = 5;
  // 级别
  string level = 6;
  // 原始代码片段
  string original_code_snippet = 7;
  // 建议代码片段
  string suggested_code_snippet = 8;
  // 建议分类
  string suggestion_category = 9;
  // 建议描述
  string suggestion_description = 10;
  // 状态
  SuggestionStatus status = 11;

  FeedbackAction action = 12;
}

// GetReviewRecordSuggestionsResponse 获取审查建议响应
message GetReviewRecordSuggestionsResponse {
  repeated ReviewSuggestion suggestions = 1;
  string review_id = 2;
}

// CreateSuggestionFeedBackRequest 创建建议反馈请求
message CreateSuggestionFeedBackRequest {
  BaseReq base_req = 1;
  // Issue ID (具体建议ID)
  int32 intellicode_issue_id = 2;
  // Review ID
  int32 intellicode_review_id = 3;
  // 处理结果
  FeedbackAction result = 4;
  // 原因类别
  string reason = 5;
  // 自定义原因
  string other_reason = 6;
}

// CreateSuggestionFeedBackResponse 创建建议反馈响应
message CreateSuggestionFeedBackResponse {
  // 响应状态码
  int32 code = 1;
  // 响应消息
  string msg = 2;
}

// ReviewService 代码审查服务接口
service ReviewService {
  // StreamReview 执行代码审查，返回 SSE 事件流
  // 使用 HTTP POST /api/review
  // 支持 Server-Sent Events (SSE) 流式响应
  rpc StreamReview(ReviewRequest) returns (intellicode.event.v1.Event) {
    option (sse_enabled) = true;
    option (google.api.http) = {
      post: "/api/review"
    };
  };

  // ListReviewRecords 查询项目的所有审查记录
  rpc ListReviewRecords(ListReviewRecordsRequest) returns (ListReviewRecordsResponse) {
    option (google.api.http) = {
      get: "/api/review/records"
    };
  };

  // GetReviewRecordEvents 获取指定审查的详细事件数据
  rpc GetReviewRecordEvents(GetReviewRecordEventsRequest) returns (GetReviewRecordEventsResponse) {
    option (google.api.http) = {
      get: "/api/review/events"
    };
  };

  // GetReviewRecordSuggestions 获取审查建议和反馈状态
  rpc GetReviewRecordSuggestions(GetReviewRecordSuggestionsRequest) returns (GetReviewRecordSuggestionsResponse) {
    option (google.api.http) = {
      get: "/api/review/suggestions"
    };
  };

  // CreateSuggestionFeedBack 创建用户对建议的反馈
  rpc CreateSuggestionFeedBack(CreateSuggestionFeedBackRequest) returns (CreateSuggestionFeedBackResponse) {
    option (google.api.http) = {
      post: "/api/review/feedback"
      body: "*"
    };
  };
}
