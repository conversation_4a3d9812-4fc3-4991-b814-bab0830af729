syntax = "proto3";

package intellicode.event.v1;

option go_package = "intellicode/pkg/proto/event";

// EventType 定义事件类型枚举
enum EventType {
  EVENT_TYPE_UNSPECIFIED = 0;
  // EventTypeToolStart 工具开始执行事件
  EVENT_TYPE_TOOL_START = 1;
  // EventTypeToolEnd 工具执行完成事件  
  EVENT_TYPE_TOOL_END = 2;
  // EventTypeAssistant LLM 流式响应事件
  EVENT_TYPE_ASSISTANT = 3;
  // EventTypeSuggestions 修复建议事件
  EVENT_TYPE_SUGGESTIONS = 4;
  // EventTypeSessionStart 会话开始事件
  EVENT_TYPE_SESSION_START = 5;
  // EventTypeSessionEnd 会话结束事件
  EVENT_TYPE_SESSION_END = 6;
}

// ToolStatus 定义工具状态枚举
enum ToolStatus {
  TOOL_STATUS_UNSPECIFIED = 0;
  // 工具正在运行
  TOOL_STATUS_RUNNING = 1;
  // 工具执行成功
  TOOL_STATUS_SUCCESS = 2;
  // 工具执行出错
  TOOL_STATUS_ERROR = 3;
}

// Event 基础事件结构
message Event {
  EventType type = 1;
  oneof data {
    ToolStartData tool_start = 2;
    ToolEndData tool_end = 3;
    AssistantData assistant = 4;
    SuggestionsData suggestions = 5;
    SessionStartData session_start = 6;
    SessionEndData session_end = 7;
  }
}

// ToolStartData 工具开始事件数据
message ToolStartData {
  string name = 1;
  string input = 2;
  ToolStatus status = 3;
  string tool_call_id = 4;
}

// ToolEndData 工具结束事件数据
message ToolEndData {
  string name = 1;
  string input = 2;
  string output = 3;
  ToolStatus status = 4;
  string tool_call_id = 5;
}

// AssistantData LLM 响应事件数据
message AssistantData {
  string chunk = 1;
}

// SuggestionsData 修复建议事件数据
message SuggestionsData {
  repeated Suggestion suggestions = 1;
}

// Level 定义问题严重级别枚举
enum Level {
  LEVEL_UNSPECIFIED = 0;
  // 信息级别
  LEVEL_INFO = 1;
  // 轻微问题
  LEVEL_MINOR = 2;
  // 主要问题
  LEVEL_MAJOR = 3;
  // 严重问题
  LEVEL_CRITICAL = 4;
  // 阻塞问题
  LEVEL_BLOCKER = 5;
}

// Suggestion 修复建议
message Suggestion {
  int32 id = 1;
  string category = 2;
  Level level = 3;
  string file_path = 4;
  int32 start_line = 5;
  int32 end_line = 6;
  string description = 7;
  string old_string = 8;
  string new_string = 9;
}

// SessionStartData 会话开始事件数据
message SessionStartData {
  string session_id = 1;
  repeated string review_files = 2;
}

// SessionEndData 会话结束事件数据
message SessionEndData {
  string session_id = 1;
  bool success = 2;
}
