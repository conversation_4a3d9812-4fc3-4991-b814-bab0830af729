# Protocol Buffers 编译配置

本项目已配置了 Protocol Buffers 编译支持，用于生成 Java 客户端代码。

## 文件结构

```
proto/
├── api.proto          # API 服务定义
├── event.proto        # 事件消息定义
└── README.md          # 本文档
```

## 编译命令

### 生成 Proto 代码
```bash
./gradlew generateProto
```

### 完整构建项目
```bash
./gradlew build
```

## 生成的代码位置

编译后的 Java 代码将生成在以下目录：

- **Java 消息类**: `build/generated/source/proto/main/java/com/intellicode/`
  - `api/v1/` - API 相关的消息类和枚举
  - `event/v1/` - 事件相关的消息类和枚举

- **gRPC 服务类**: `build/generated/source/proto/main/grpc/com/intellicode/`
  - `api/v1/ReviewServiceGrpc.java` - 代码审查服务的 gRPC 客户端

## 主要功能

### API 服务 (api.proto)
- `ReviewService` - 代码审查服务
- `StreamReview` - 支持 SSE 流式响应的代码审查方法
- `ReviewRequest` - 代码审查请求消息
- `IDE` - IDE 类型枚举

### 事件系统 (event.proto)
- `Event` - 基础事件结构
- `EventType` - 事件类型枚举
- 各种事件数据类型：工具执行、LLM 响应、修复建议等

## 依赖项

项目已自动配置以下依赖：
- `com.google.protobuf:protobuf-java:3.24.4`
- `io.grpc:grpc-stub:1.58.0`
- `io.grpc:grpc-protobuf:1.58.0`
- `com.google.api.grpc:proto-google-common-protos:2.25.1`
- `javax.annotation:javax.annotation-api:1.3.2`

## 使用示例

```java
// 创建代码审查请求
ReviewRequest request = ReviewRequest.newBuilder()
    .setBaseReq(BaseReq.newBuilder().setCicdToken("your-token"))
    .setRepoPath("/path/to/repo")
    .setIde(IDE.IDE_JETBRAINS)
    .build();

// 使用 gRPC 客户端
ReviewServiceGrpc.ReviewServiceStub stub = ReviewServiceGrpc.newStub(channel);
```