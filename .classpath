<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" output="bin/main" path="src/main/java">
		<attributes>
			<attribute name="gradle_scope" value="main"/>
			<attribute name="gradle_used_by_scope" value="main,test"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="bin/main" path="build/generated/source/proto/main/java">
		<attributes>
			<attribute name="protobuf_generated_source" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="bin/main" path="build/generated/source/proto/main/grpc">
		<attributes>
			<attribute name="protobuf_generated_source" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="bin/main" path="src/main/resources">
		<attributes>
			<attribute name="gradle_scope" value="main"/>
			<attribute name="gradle_used_by_scope" value="main,test"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="bin/test" path="build/generated/source/proto/test/java">
		<attributes>
			<attribute name="protobuf_generated_source" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="test" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="bin/test" path="build/generated/source/proto/test/grpc">
		<attributes>
			<attribute name="protobuf_generated_source" value="true"/>
			<attribute name="optional" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="test" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17/"/>
	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
	<classpathentry kind="src" path="bin/generated-sources/annotations">
		<attributes>
			<attribute name="optional" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="bin/test" path="bin/generated-test-sources/annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="test" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="bin/default"/>
</classpath>
