# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

JetDev 是一个 IntelliJ IDEA 插件，集成了 Nocalhost 开发环境和代码审查功能。该插件支持 Java、Go、Python 三种语言的远程开发和调试。

## Architecture

### Core Components

- **API 层**: 与后端服务交互的 API 接口
  - `api/IdeHubAPI.java`: IDE Hub 服务接口
  - `api/CodeReviewAPI.java`: 代码审查服务接口
- **Service 层**: 核心业务逻辑
  - `service/NocalhostContextManager.java`: 上下文管理
  - `service/CodeReviewTaskManager.java`: 代码审查任务管理
  - `service/NocalhostAppListManager.java`: 应用列表管理
- **UI 层**: 用户界面组件
  - `ui/toolwindow/`: 工具窗口实现
  - `ui/codereview/`: 代码审查界面
  - `ui/console/`: 控制台界面
- **Configuration**: 运行配置
  - `configuration/go/`: Go 语言调试配置
  - `configuration/java/`: Java 语言调试配置
  - `configuration/python/`: Python 语言调试配置

### Plugin Configuration

插件通过 `plugin.xml` 注册服务和扩展点，支持可选的语言特定功能。

## Development Commands

### 构建和运行

```bash
# 构建插件
gradle build

# 运行插件（在 IntelliJ IDEA 中测试）
gradle runIde

# 构建发布包
gradle buildPlugin
```

### 配置说明

- `gradle.properties`: 包含版本信息和插件配置
- `build.gradle.kts`: Gradle 构建脚本，支持多种 IDE 平台
- 插件支持 IntelliJ IDEA、GoLand、PyCharm 等多个 IDE

### 环境配置

- Java 17+
- 插件版本兼容 IntelliJ IDEA 2023.2+
- 依赖的主要库：OkHttp、FastJSON、CommonMark、Lombok

## Important File Locations

- Configuration: `src/main/resources/config.properties`
- Plugin manifest: `src/main/resources/META-INF/plugin.xml`
- Icons: `src/main/resources/icons/`
- Language-specific configs: `src/main/resources/META-INF/{go,java,python}-only.xml`

## Development Notes

- 插件名称在不同上下文中可能显示为 "JetDev" 或 "Sentinel Nocalhost"
- 支持代码审查功能，包括任务管理和建议反馈
- 使用 Lombok 简化代码，编译时需要注解处理器
- 集成了 Sentry 用于错误追踪


- 每次完成修改任务后，都要运行./gradlew build来校验代码是否可以运行
- 构建UI、UX时，优先使用IntelliJ IDE 的主题色
- 构建UI、UX时，优先使用IntelliJ IDE 提供的组件
