plugins {
    id("java")
    id("org.jetbrains.kotlin.jvm") version "1.9.21"
    id("org.jetbrains.intellij") version "1.16.1"
    java
    id("io.franzbecker.gradle-lombok") version "2.1"
    id("net.saliman.properties") version "1.5.1"
    id("com.google.protobuf") version "0.9.4"
}

java {
    sourceCompatibility = JavaVersion.valueOf(prop("javaCompatibility"))
    targetCompatibility = JavaVersion.valueOf(prop("javaCompatibility"))
}

group = "com.sentinel"

repositories {
    mavenCentral()
}

dependencies {

    implementation("com.vladsch.flexmark:flexmark:0.62.2")
    implementation("com.vladsch.flexmark:flexmark-util:0.62.2")

    implementation("io.sentry:sentry:1.7.23") {
        exclude("org.slf4j")
    }

    implementation("com.squareup.okio:okio-jvm:3.4.0")
    implementation("com.github.zafarkhaja:java-semver:0.9.0")
    implementation("com.squareup.okhttp3:okhttp:5.0.0-alpha.3")
    implementation("com.alibaba:fastjson:1.2.83")
    implementation("org.commonmark:commonmark:0.20.0")


    implementation("com.github.briandilley.jsonrpc4j:jsonrpc4j:1.6") {
        exclude("org.slf4j")
    }

    // Protobuf dependencies
    implementation("com.google.protobuf:protobuf-java:3.24.4")
    implementation("com.google.protobuf:protobuf-java-util:3.24.4")
    implementation("io.grpc:grpc-stub:1.58.0")
    implementation("io.grpc:grpc-protobuf:1.58.0")
    implementation("javax.annotation:javax.annotation-api:1.3.2")
    implementation("com.google.api.grpc:proto-google-common-protos:2.25.1")

    implementation("org.projectlombok:lombok:1.18.30")
    annotationProcessor("org.projectlombok:lombok:1.18.30")
    testAnnotationProcessor("org.projectlombok:lombok:1.18.30")
    testImplementation("org.projectlombok:lombok:1.18.30")

    // Test dependencies
    testImplementation("junit:junit:4.13.2")
    testImplementation("org.mockito:mockito-core:4.11.0")

}

var baseIDE = "IU"
if (project.hasProperty("baseIDE")) {
    baseIDE = project.property("baseIDE") as String
}
val ideaVersion = prop("ideaVersion")

val terminalPlugin = "terminal"
val javaPlugin = "com.intellij.java"
val goPlugin = "org.jetbrains.plugins.go:" + prop("goPluginVersion")
var pythonPlugin = "Pythonid:" + prop("pythonPluginVersion")

// Configure Gradle IntelliJ Plugin
// Read more: https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html
intellij {
    type.set("IC") // Target IDE Platform

    version.set(ideaVersion)

    plugins.set(listOf(
        terminalPlugin,
        pythonPlugin,
        javaPlugin,
        goPlugin,
        "org.jetbrains.plugins.terminal",
        "Git4Idea"
    ))

    pluginName.set(prop("pluginName"))
}

sourceSets {
    main {
        java.srcDirs("src/main/java")
        proto {
            srcDir("proto")
        }
    }
}

// Protobuf configuration
protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:3.24.4"
    }
    plugins {
        create("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:1.58.0"
        }
    }
    generateProtoTasks {
        all().forEach { task ->
            task.plugins {
                create("grpc")
            }
        }
    }
}

tasks.runIde {
    if (baseIDE == "IC") {
        ideDir.set(File("/Applications/IntelliJ IDEA CE.app/Contents"))
    }
    if (baseIDE == "GO") {
        ideDir.set(File("/Applications/GoLand.app/Contents"))
    }
    if (baseIDE == "Python") {
        ideDir.set(File("/Applications/PyCharm.app/Contents"))
    }
}


tasks {
    // Set the JVM compatibility versions
    withType<JavaCompile> {
        sourceCompatibility = "17"
        targetCompatibility = "17"
    }
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        kotlinOptions.jvmTarget = "17"
    }

    patchPluginXml {
        sinceBuild.set("232.8660.185")
        untilBuild.set("251.*")
    }

    signPlugin {
        certificateChain.set(System.getenv("CERTIFICATE_CHAIN"))
        privateKey.set(System.getenv("PRIVATE_KEY"))
        password.set(System.getenv("PRIVATE_KEY_PASSWORD"))
    }

    publishPlugin {
        token.set(System.getenv("PUBLISH_TOKEN"))
    }
}

tasks.withType(JavaCompile::class) {
    options.compilerArgs.add("-Xlint:unchecked")
    options.compilerArgs.add("-Xlint:deprecation")
}

tasks.buildPlugin {
    archiveFileName = "${project.name}.zip"
}

// 添加运行 SSE 测试应用的任务
tasks.register<JavaExec>("runSSETest") {
    group = "application"
    description = "Run SSE Test Application"
    mainClass.set("com.sentinel.nocalhost.intellicode.test.SSETestApp")
    classpath = sourceSets.main.get().runtimeClasspath
}

// 添加运行独立 SSE 测试应用的任务
tasks.register<JavaExec>("runStandaloneSSETest") {
    group = "application"
    description = "Run Standalone SSE Test Application"
    mainClass.set("com.sentinel.nocalhost.intellicode.test.StandaloneSSETestApp")
    classpath = sourceSets.main.get().runtimeClasspath
}

// 添加运行模拟 SSE 测试应用的任务
tasks.register<JavaExec>("runMockSSETest") {
    group = "application"
    description = "Run Mock SSE Test Application"
    mainClass.set("com.sentinel.nocalhost.intellicode.test.MockSSETestApp")
    classpath = sourceSets.main.get().runtimeClasspath
}

// 添加运行UI测试应用的任务
tasks.register<JavaExec>("runUITest") {
    group = "verification"
    description = "Run UI test application for IntellicodeAgentPanel"
    classpath = sourceSets.main.get().runtimeClasspath
    mainClass.set("com.sentinel.nocalhost.intellicode.test.UITestApp")
    // 设置系统属性以避免IntelliJ平台的一些限制
    systemProperty("idea.is.internal", "true")
    systemProperty("idea.test.mode", "true")
}

tasks.register<JavaExec>("runSimpleUITest") {
    group = "verification"
    description = "Run simple UI test for chunk accumulation"
    classpath = sourceSets.main.get().runtimeClasspath
    mainClass.set("com.sentinel.nocalhost.intellicode.test.SimpleUITest")
}

tasks.register<JavaExec>("runMarkdownTest") {
    group = "verification"
    description = "Run Markdown renderer test"
    classpath = sourceSets.main.get().runtimeClasspath
    mainClass.set("com.sentinel.nocalhost.intellicode.test.MarkdownRenderTest")
}

fun prop(name: String): String =
    extra.properties[name] as? String
        ?: error("Property `$name` is not defined in gradle.properties")