# JetDev 插件 UI 架构文档

## 概述

JetDev 是一个 IntelliJ IDEA 插件，集成了 Nocalhost 开发环境和代码审查功能。该插件支持 Java、Go、Python 三种语言的远程开发和调试，并提供 AI 驱动的代码审查服务。

## 整体 UI 布局结构

### 1. 主工具窗口（JetDev）- 右侧面板

**定义位置**: `plugin.xml:48`
```xml
<toolWindow id="JetDev" anchor="right" icon="NocalhostIcons.ConfigurationLogoRight"
            factoryClass="com.sentinel.nocalhost.ui.toolwindow.NocalhostWindowFactory"/>
```

**组件层次**:
- **工厂类**: `NocalhostWindowFactory`
- **主要组件**:
  - 工具栏：包含登录、刷新、流量标记、帮助等按钮
  - 主内容区：根据登录状态显示不同内容
    - 未登录：显示"Please login first"提示
    - 已登录：显示服务管理界面

### 2. 控制台窗口（JetDev Console）- 底部面板

**定义位置**: `plugin.xml:51-53`
```xml
<toolWindow id="JetDev Console" anchor="bottom" icon="NocalhostIcons.ConfigurationLogoRight"
            factoryClass="com.sentinel.nocalhost.ui.console.NocalhostConsoleWindowFactory"
            canCloseContents="true"/>
```

**主要组件**:
- OUTPUT 标签页：默认输出窗口
- 错误窗口：动态添加的错误信息标签页
- 支持多标签页切换和关闭

### 3. 状态栏组件

**定义位置**: `plugin.xml:44-46`
```xml
<statusBarWidgetFactory
        implementation="com.sentinel.nocalhost.ui.widget.SyncStatusWidgetFactory"
        order="first" id="File Sync Status"/>
```

## Tab 页面实现

### Tab 定义和实现

JetDev 主工具窗口包含两个主要 Tab，在 `NocalhostWindowFactory.java:18-33` 中实现：

#### 1. 本地开发 Tab
```java
NocalhostWindow nocalhostWindow = new NocalhostWindow(project);
Content firstTab = ContentFactory.getInstance().createContent(nocalhostWindow.getPanel(), "本地开发", true);
toolWindow.getContentManager().addContent(firstTab);
```

**核心组件**: `src/main/java/com/sentinel/nocalhost/ui/toolwindow/NocalhostWindow.java:56`
- **内容**: `LocalDevPanel` - 服务搜索和管理界面
- **组件结构**:
  - 搜索框（标签为"服务"）
  - 服务树形列表（`NocalhostTree`）

#### 2. 啄木鸟 Tab（CodeReview）
```java
ReviewWorkspacePanel reviewWorkspacePanel = new ReviewWorkspacePanel(project);
Content secondTab = ContentFactory.getInstance().createContent(reviewWorkspacePanel.getMainPanel(), "啄木鸟", true);
toolWindow.getContentManager().addContent(secondTab);
```

**核心组件**: `src/main/java/com/sentinel/nocalhost/ui/codereview/panel/ReviewWorkspacePanel.java:38`
- **内容状态**:
  - 空状态：显示欢迎信息和功能说明
  - 任务列表：显示代码审查任务（`ReviewTaskPanel`）
  - 建议面板：显示具体的代码审查建议（`ReviewSuggestionPanel`）

### Tab 消息订阅机制

```java
// 本地开发 Tab 刷新
ApplicationManager.getApplication().getMessageBus().connect()
    .subscribe(RefreshToolWindowNotifier.REFRESH_TOOL_WINDOW_TOPIC, nocalhostWindow);

// 代码审查 Tab 刷新  
ApplicationManager.getApplication().getMessageBus().connect()
    .subscribe(RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC, reviewWorkspacePanel);
```

## 核心 UI 组件

### 本地开发面板组件

#### LocalDevPanel (`src/main/java/com/sentinel/nocalhost/ui/panel/LocalDevPanel.java`)
```java
public JPanel getPanel() {
    // 服务搜索输入框
    JLabel label = new JLabel("服务");
    AppSearchJTextField inputField = new AppSearchJTextField("");
    
    // 服务树形列表
    tree = new NocalhostTree(project);
    JBScrollPane scrollPane = new JBScrollPane(tree);
    
    // 布局组装
    treePanel.add(inputPanel, BorderLayout.NORTH);
    treePanel.add(scrollPane, BorderLayout.CENTER);
}
```

#### NocalhostTree (`src/main/java/com/sentinel/nocalhost/ui/tree/NocalhostTree.java`)
- 继承自 IntelliJ 的 `Tree` 组件
- 使用 `NocalhostTreeModel` 管理数据
- 支持单选模式和自定义渲染器
- 集成鼠标事件监听

### 代码审查面板组件

#### ReviewWorkspacePanel 核心方法
```java
private void initToolWindowsPanel() {
    if (StringUtils.isNotEmpty(settings.getUserToken())) {
        List<CodeReviewTaskInfo> reviewTaskList = codeReviewAPI.queryCodeReviewTaskList(project);
        if (null == reviewTaskList || reviewTaskList.isEmpty()) {
            setEmptyPanel();  // 空状态界面
        } else {
            // 显示任务列表或建议面板
        }
    } else {
        noticeLogin();  // 登录提示
    }
}
```

### 对话框组件

1. **应用配置对话框**（`AppConfigDialog`）
2. **流量标记对话框**（`TrafficMarkDialog`）  
3. **拒绝建议对话框**（`RejectSuggestionDialog`）

## UI 架构特点

### 1. 技术栈
- **基础框架**: IntelliJ Platform SDK
- **UI 组件**: Java Swing + IntelliJ UI 扩展
- **布局管理**: BorderLayout、FlowLayout、BoxLayout
- **主题支持**: 通过 `ThemeChangeListener` 适配 IDE 主题

### 2. 设计模式
- **工厂模式**: `ToolWindowFactory` 创建工具窗口
- **观察者模式**: 通过消息总线实现组件间通信
- **状态模式**: 根据登录状态和任务状态切换 UI 显示

### 3. 消息驱动架构
```java
// 消息总线主题定义
RefreshToolWindowNotifier.REFRESH_TOOL_WINDOW_TOPIC
RefreshReviewToolWindowNotifier.REFRESH_CODE_REVIEW_TOOL_WINDOW_TOPIC
NocalhostTreeUpdateNotifier.NOCALHOST_TREE_UPDATE_NOTIFIER_TOPIC
```

### 4. 响应式特性
- **实时更新**: 通过定时器和消息总线实现界面实时刷新
- **状态感知**: UI 根据服务状态、登录状态、任务状态动态调整
- **用户交互**: 支持搜索过滤、右键菜单、工具栏操作

## 扩展性设计

### 添加新 Tab
1. 创建面板类（继承或实现相应接口）
2. 在 `NocalhostWindowFactory` 中添加 Content
3. 订阅相应的刷新消息主题

### 自定义 UI 组件
- 继承 IntelliJ 平台标准组件
- 实现 `Disposable` 接口管理资源
- 使用平台提供的颜色和图标资源

## 文件结构总览

```
src/main/java/com/sentinel/nocalhost/ui/
├── toolwindow/              # 主工具窗口
│   ├── NocalhostWindowFactory.java
│   ├── NocalhostWindow.java
│   └── ToolWindowIconUpdater.java
├── console/                 # 控制台窗口
│   ├── NocalhostConsoleWindowFactory.java
│   └── NocalhostConsoleManager.java
├── codereview/             # 代码审查相关 UI
│   ├── panel/              # 面板组件
│   └── event/              # 事件处理
├── tree/                   # 树形组件
│   ├── NocalhostTree.java
│   └── NocalhostTreeModel.java
├── panel/                  # 通用面板
│   └── LocalDevPanel.java
├── dialog/                 # 对话框
├── action/                 # 工具栏和上下文菜单动作
└── widget/                 # 状态栏组件
```

这种模块化的 UI 架构设计确保了代码的可维护性和扩展性，同时充分利用了 IntelliJ IDEA 平台的原生组件和设计规范。