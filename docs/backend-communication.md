# JetDev 插件后端通信与进程管理架构

## 概述

JetDev 插件采用混合架构，同时与多个后端系统进行通信，包括远程 API 服务和本地命令行工具。本文档详细描述了插件的通信机制和进程管理策略。

## 通信架构概览

### 后端系统分类

1. **远程后端服务**
   - IDE Hub API：用户管理和应用配置
   - CodeReview API：代码审查服务
   - Intelliforge API：流量标记和认证服务

2. **本地后端工具**
   - nhctl 命令行工具：核心功能执行
   - 本地 CodeReview 服务：运行在 localhost:30555

## 远程后端通信

### 1. IDE Hub API (`IdeHubAPI.java`)

**服务端点**: 
- 生产环境: `https://cicd.ttyuyin.com` (`config.properties:8`)
- 测试环境: `http://cicd-test0.ttyuyin.com` (已注释)

**核心功能**:
```java
// 查询用户项目应用列表
public List<UserProject> queryUserProjectAppList(Project project)
// 查询用户应用配置
public UserAppConfigs queryUserAppConfigs(Project project, UserAppConfigsRequest request)
// 更新用户应用配置
public void updateUserAppConfigs(Project project, UserAppConfigs userAppConfigs)
// 查询 Nhctl 设置
public Nhctl queryNhctlSetting(Project project)
```

**HTTP 客户端配置**:
```java
private final OkHttpClient client = new OkHttpClient.Builder()
    .connectTimeout(2, TimeUnit.SECONDS)
    .writeTimeout(2, TimeUnit.SECONDS)
    .readTimeout(2, TimeUnit.SECONDS)
    .hostnameVerifier((hostName, session) -> true)
    .retryOnConnectionFailure(true)
    .build();
```

**认证方式**: Bearer Token（通过 `Authorization` header）

### 2. CodeReview API (`CodeReviewAPI.java`)

**服务端点**: 
- CodeReview 服务: `http://localhost:30555` (`config.properties:9`)
- Intelliforge 服务: `https://intelliforge.ttyuyin.com` (`config.properties:11`)

**核心功能**:
```java
// 执行代码审查
public int executeCodeReview(Project project)
// 查询代码审查任务列表
public List<CodeReviewTaskInfo> queryCodeReviewTaskList(Project project)
// 查询建议列表
public void querySuggestionList(Project project)
// 提交反馈
public void checkCodeReview(Project project, FeedbackCodeReviewRequest request)
// 查询流量标记
public List<TrafficMark> queryTrafficMarks(Project project)
```

**HTTP 客户端配置**:
```java
private final OkHttpClient client = new OkHttpClient.Builder()
    .connectTimeout(6, TimeUnit.SECONDS)
    .writeTimeout(6, TimeUnit.SECONDS) 
    .readTimeout(6, TimeUnit.SECONDS)
    .hostnameVerifier((hostName, session) -> true)
    .retryOnConnectionFailure(true)
    .build();
```

**认证方式**: 
- CodeReview: `Cicd-Authorization` header
- Intelliforge: `Authorization: Bearer` header

### 错误处理机制

两个 API 都实现了统一的错误处理：
```java
public static boolean dealErrorCode(Project project, int statusCode, String data) {
    switch (statusCode) {
        case 200: return false; // 成功
        case 401: // 登录过期，清除 token 并刷新 UI
        case 403: // 无权限访问
        case 400: // 请求错误，解析并显示错误消息
        default: // 其他错误，显示原始消息
    }
}
```

## 本地后端通信

### 1. Nhctl 命令行工具 (`NhctlCommand.java`)

**工具管理**: 通过 `NocalhostBinService` 自动下载、安装和更新

**核心命令**:
```java
// CodeReview 服务管理
public void reviewStart() // 启动服务
public void reviewStop()  // 停止服务  
public void reviewInit()  // 初始化服务

// 开发环境管理
public String devStart(NhctlDevStartOptions opts)
public void devEnd(NhctlDevEndOptions opts)

// 文件同步
public String sync(NhctlSyncOptions opts)
public String syncStatus(NhctlSyncStatusOptions opts)

// 端口转发
public void startPortForward(NhctlPortForwardStartOptions opts)
public void endPortForward(NhctlPortForwardEndOptions opts)

// 流量标记
public String getTrafficMark()
public String setTrafficMark(String trafficMark)

// 工具管理
public String version()
public void setToken(String token)
```

**命令执行流程**:
```java
protected String execute(List<String> args, NhctlGlobalOptions opts, String sudoPassword) {
    // 1. 添加全局选项
    addGlobalOptions(args, opts);
    
    // 2. 处理 sudo 权限
    if (sudoPassword != null) {
        args = SudoUtil.toSudoCommand(args);
    }
    
    // 3. 创建命令行进程
    GeneralCommandLine commandLine = getCommandline(args);
    Process process = commandLine.createProcess();
    
    // 4. 注册进程到管理器
    if (opts != null && opts.getTask() != null) {
        ApplicationManager.getApplication().getService(ProgressProcessManager.class)
            .add(opts.getTask(), process);
    }
    
    // 5. 执行并处理结果
    String output = CharStreams.toString(reader);
    int exitCode = process.waitFor();
    if (exitCode != 0) {
        throw new NhctlCommandException(cmd, exitCode, output, errorOutput.get());
    }
    return output;
}
```

### 2. 二进制工具管理 (`NocalhostBinService.java`)

**版本管理策略**:
- 启动时检查工具是否存在和可执行
- 比较本地版本与服务端要求版本
- 自动下载和更新工具

**下载流程**:
```java
private void downloadJetDevCommandTool(Project project, String downloadUrl, String title) {
    ProgressManager.getInstance().run(new Task.WithResult<Void, Exception>(project, title, true) {
        @Override
        protected Void compute(@NotNull ProgressIndicator indicator) {
            // 1. 下载二进制文件
            startDownload(downloadUrl, Paths.get(NhctlUtil.binaryDir()), indicator);
            
            // 2. 设置可执行权限
            boolean success = nocalhostBin.setExecutable(true);
            
            // 3. 重启 CodeReview 服务
            nhctlCommand.reviewStop();
            Thread.sleep(15000L);
            nhctlCommand.reviewInit();
            
            return null;
        }
    });
}
```

**平台适配**:
```java
private static String getDownloadFilename() {
    if (SystemInfo.isMac) {
       return "nhctl-darwin-" + arch();  // arm64/amd64
    }
    if (SystemInfo.isLinux) {
        return "nhctl-linux-" + arch();   // arm64/amd64
    }
    if (SystemInfo.isWindows) {
        return "nhctl-windows-amd64.exe";
    }
}
```

## 进程管理

### 1. 进程生命周期管理

**ProgressProcessManager** (`ProgressProcessManager.java`):
- 将后台任务与进程关联
- 支持任务取消时自动终止相关进程
- 使用 ConcurrentMap 确保线程安全

```java
public class ProgressProcessManager {
    private final Map<Task, List<Process>> map = new MapMaker().makeMap();
    
    public synchronized void add(Task task, Process process) {
        map.computeIfAbsent(task, key -> Lists.newArrayList());
        map.get(task).add(process);
    }
}
```

**JetDevExecutionManager** (`JetDevExecutionManager.java`):
- 管理开发环境相关的进程处理器
- 自动清理已终止的进程
- 项目级别的进程管理

```java
public synchronized void add(NocalhostDevProcessHandler processHandler) {
    // 清理已终止的进程
    ref.get().removeIf(handler -> handler.isProcessTerminated() 
                                || handler.isProcessTerminating());
    ref.get().add(processHandler);
}
```

### 2. 进程清理机制

**项目关闭时清理** (`NocalhostWindow.java:118-127`):
```java
@Override
public void dispose() {
    try {
        var processes = ExecutionManager.getInstance(project).getRunningProcesses();
        if (processes.length > 0) {
            Arrays.stream(processes).forEach(ProcessHandler::destroyProcess);
            Thread.sleep(1000);
        }
    } catch (Exception ignored) {}
}
```

**超时保护机制**:
```java
// 为 get 命令添加 10 秒超时保护
if (!args.isEmpty() && StringUtils.equals(args.get(1), "get")) {
    new Alarm(Alarm.ThreadToUse.POOLED_THREAD, ApplicationManager.getApplication())
        .addRequest(process::destroy, 10 * 1000);
}
```

## 配置管理

### 系统配置 (`config.properties`)

```properties
# 版本信息
version=1.0.0
report_version=1.1.4

# 服务端点
cicd_url=https://cicd.ttyuyin.com
code_review_url=http://localhost:30555
intelliforge_url=https://intelliforge.ttyuyin.com
login_url=https://intelliforge.ttyuyin.com/user/jetdev/login

# 帮助文档
help_url=https://q9jvw0u5f5.feishu.cn/wiki/TrM6whxMGi8PttkQUT3c3L65npA
review_help_url=https://q9jvw0u5f5.feishu.cn/wiki/SmJUwjFQXiPmwKk4fe8cz8JjnFe
```

### 用户配置

通过 `NocalhostSettings` 管理：
- 用户认证 Token
- CodeReview 模型选择
- Intelliforge Token

## 安全机制

### 1. 认证管理
- 支持 Token 过期自动检测和 UI 刷新
- 多种认证方式：Bearer Token、自定义 Header
- Token 安全存储和清理

### 2. 网络安全
- HTTPS 优先的服务端点配置
- hostname 验证跳过（用于内部服务）
- 连接超时和重试机制

### 3. 进程安全
- 进程权限管理（sudo 支持）
- 文件锁机制防止并发下载
- 自动清理僵尸进程

## 扩展性设计

### 1. API 扩展
- 统一的错误处理机制
- 可配置的服务端点
- 标准化的请求/响应格式

### 2. 命令扩展
- 插件化的命令选项系统
- 统一的命令执行框架
- 可扩展的全局选项

### 3. 进程管理扩展
- 任务与进程的解耦设计
- 多种进程处理器支持
- 可插拔的生命周期管理

这种分层的通信和进程管理架构确保了插件的稳定性、安全性和可扩展性，同时提供了良好的用户体验。