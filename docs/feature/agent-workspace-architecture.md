# Agent Workspace 架构设计方案

## 一、架构概述

采用 **Action-Service-Model-UI** 分层架构，遵循 IntelliJ Platform 规范和"Fat Model, Thin UI"设计原则。

```
┌────────────────────────────────────────────────────────┐
│                    Action Layer                        │
│         用户交互入口，触发业务操作                        │
└────────────────────────────────────────────────────────┘
                         ↓
┌────────────────────────────────────────────────────────┐
│                   Service Layer                        │
│         业务逻辑协调，管理Model生命周期                   │
└────────────────────────────────────────────────────────┘
                         ↓
┌────────────────────────────────────────────────────────┐
│                    Model Layer                         │
│         数据管理、状态维护、业务规则                      │
└────────────────────────────────────────────────────────┘
                         ↓
┌────────────────────────────────────────────────────────┐
│                      UI Layer                          │
│         纯展示逻辑，数据绑定，用户界面                    │
└────────────────────────────────────────────────────────┘
```

## 二、核心模块建模

### 2.1 Service 层模块

```
AgentWorkspaceService (主服务)
├── SSEEventService (事件流服务)
├── SuggestionOperationService (建议操作服务)
└── EditorIntegrationService (编辑器集成服务)
```

**职责分配：**

- `AgentWorkspaceService`：会话生命周期管理、模式切换、整体协调
- `SSEEventService`：SSE 连接管理、事件流处理、历史数据加载
- `SuggestionOperationService`：建议的接受/拒绝、批量操作、反馈提交
- `EditorIntegrationService`：代码定位、高亮显示、文本替换

### 2.2 Model 层模块

```
AgentWorkspaceModel (主模型)
├── EventFlowModel (事件流模型)
├── SuggestionModel (建议数据模型)
└── SessionStateModel (会话状态模型)
```

**职责分配：**

- `AgentWorkspaceModel`：全局状态管理、子模型协调、对外数据接口
- `EventFlowModel`：事件存储、渲染转换、流式内容聚合
- `SuggestionModel`：建议状态管理、筛选排序、统计计算
- `SessionStateModel`：会话信息、连接状态、错误状态

### 2.3 UI 层模块

```
AgentWorkspacePanel (主面板)
├── ExecutionDisplayPanel (执行显示区)
├── SuggestionListPanel (建议列表区)
```

**职责分配：**

- `AgentWorkspacePanel`：布局管理、模型绑定、子面板协调
- `ExecutionDisplayPanel`：事件内容渲染、滚动控制
- `SuggestionListPanel`：建议项展示、交互响应

## 四、数据流设计

### 4.1 实时模式数据流

```
SSE事件流 → SSEEventService → EventFlowModel → 触发PropertyChange
    ↓                              ↓
建议事件                      事件渲染处理
    ↓                              ↓
SuggestionModel             ExecutionDisplayPanel
    ↓
SuggestionListPanel
```

### 4.2 用户操作数据流

```
用户点击Accept → AcceptSuggestionAction → SuggestionOperationService
                                              ↓
                                    EditorIntegrationService (修改代码)
                                              ↓
                                    API反馈提交 → SuggestionModel更新
                                              ↓
                                    PropertyChange → View自动刷新
```

### 4.3 模型变更通知流

```
Model数据变更 → firePropertyChange(property, oldValue, newValue)
                        ↓
            所有注册的PropertyChangeListener
                        ↓
            UI层组件自动更新显示
```

## 五、控制流设计

### 5.1 会话启动控制流

```
1. StartReviewAction.actionPerformed()
2. AgentWorkspaceService.startReviewSession()
3. Model状态更新 → CONNECTING
4. SSEEventService.connect()
5. 建立SSE连接
6. Model状态更新 → CONNECTED
7. 事件流开始处理
```

### 5.2 事件处理控制流

```
1. SSE事件到达
2. SSEEventService解析事件
3. 根据事件类型分发：
   - Tool事件 → EventFlowModel.addToolEvent()
   - Assistant事件 → EventFlowModel.appendAssistantChunk()
   - Suggestion事件 → SuggestionModel.addSuggestions()
4. Model触发PropertyChange
5. UI监听器响应更新
```

### 5.3 错误处理控制流

```
1. 异常发生（网络/API/操作失败）
2. Service捕获异常
3. 更新SessionStateModel错误状态
4. 发布ErrorEvent到MessageBus
5. UI显示错误提示
6. 记录日志
```

## 六、关键设计决策

### 6.1 状态管理

- **单一数据源**：所有状态存储在 Model 层
- **不可变更新**：状态变更通过方法调用，不直接修改
- **观察者模式**：使用 PropertyChangeListener 实现响应式更新

### 6.2 异步处理

- **CompletableFuture**：所有 IO 操作异步执行
- **EDT 安全**：UI 更新确保在 EDT 线程
- **后台任务**：使用 ProgressManager 管理长时间操作

### 6.3 生命周期管理

- **Service 生命周期**：由 IntelliJ Platform 管理
- **Model 生命周期**：由 Service 管理
- **UI 生命周期**：实现 Disposable 接口，确保资源释放

### 6.4 扩展性设计

- **事件类型扩展**：新事件类型只需添加渲染器
- **操作扩展**：新操作只需添加 Action 和 Service 方法
- **UI 扩展**：新 UI 组件只需监听 Model 变化

## 七、部署架构

```
plugin.xml 注册
    ↓
Services:
- AgentWorkspaceService (project级别)
- SSEEventService (project级别)
- SuggestionOperationService (project级别)

Actions:
- StartReviewAction
- AcceptSuggestionAction
- FilterSuggestionsAction

MessageBus Topics:
- SSEEventTopic
- SuggestionOperationTopic
```
