# Intellicode Agent 迭代开发计划

## 项目背景

Intellicode Agent 是 JetDev 插件的全新功能，基于全新的后端 Intellicode 服务实现智能代码分析和建议生成。与现有的 CodeReview 功能完全独立，采用 protobuf + SSE 的技术架构。

## 整体技术架构

```
现有插件架构
├── 可重用组件
│   ├── 插件注册系统 (plugin.xml)
│   ├── UI基础组件和主题系统
│   ├── 登录Token管理
│   └── Git操作监听机制
└── 全新开发组件
    ├── IntellicodeAPI (独立于现有CodeReviewAPI)
    ├── SSE客户端和会话管理
    ├── Agent页面完整UI架构
    └── 建议系统和代码交互功能
```

## 迭代计划

### 🔄 迭代1: UI框架和Git集成 (2周)

**目标**: UI优先开发策略，建立完整的用户界面和Git文件状态集成

#### 开发策略
采用**UI优先**的开发方式，优势如下：
- **快速可视化验证** - 团队和用户可以立即看到进展
- **早期用户反馈** - UI布局和交互逻辑可以提前验证
- **技术风险前置** - UI框架问题比通信问题更容易解决
- **并行开发** - UI完成后，后端通信可以并行集成

#### 🎯 第一阶段 (1周): UI框架 + Git集成

**优先级1: IntellicodeHome页面UI框架**
- 工具窗口注册和基础布局
- 文件列表组件 (支持勾选功能)
- uncommitted/committed切换按钮
- Review触发按钮和基础交互

**优先级2: Git文件状态集成**
- ChangeListManager集成 (获取未提交文件)
- Git历史API集成 (获取最近提交文件)
- 文件状态图标和显示
- 文件路径点击跳转功能

**技术实现要点**：
```java
// 主要Git API使用
ChangeListManager.getInstance(project).getChanges() // 获取uncommitted文件
GitRepository.getInstance(project) // Git仓库实例
VcsLogProvider.readLastCommit() // 获取最近提交的文件
```

#### 🔌 第二阶段 (1周): 基础通信准备

**优先级3: IntellicodeAPI基础架构**
- API类框架 (暂时使用Mock数据)
- 基础HTTP客户端结构
- 错误处理机制框架

**优先级4: Agent页面UI框架**
- 页面跳转逻辑实现
- 基础布局 (为SSE渲染预留空间)
- 返回导航功能

#### 可重用组件
- 插件注册框架 (`plugin.xml`)
- 基础UI工具类和主题系统
- 项目服务注册机制
- IntelliJ VCS API系统

#### 可验证制品

**第一阶段结束时**:
- ✅ 能打开Intellicode工具窗口，看到完整的Home页面
- ✅ 文件列表正确显示uncommitted和committed文件
- ✅ 可以勾选/取消勾选文件，切换文件类型视图
- ✅ 点击文件路径能跳转到编辑器对应位置

**第二阶段结束时**:
- ✅ 点击Review按钮能跳转到Agent页面(虽然功能尚未完整)
- ✅ 基础的API架构已搭建，为迭代2的SSE集成做好准备
- ✅ 所有UI交互流程可以完整演示

---

### 🔄 迭代2: SSE流式通信和实时输出 (3周)

**目标**: 实现Agent页面的实时模式和主输出视图，专注于SSE通信和实时内容渲染

#### 技术任务
1. **SSE客户端实现**
   - Server-Sent Events 的完整接收和处理机制
   - 长连接稳定性和重连机制
   - 事件解析和分发系统
   - 网络异常处理和自动重连策略
   
2. **Agent页面实时架构**
   - 全新的实时会话页面框架
   - 支持实时模式的UI渲染引擎
   - 页面导航和基础状态管理
   - 实时数据流的UI响应机制
   
3. **主输出视图(Main Output View)**
   - 支持 `toolstart`/`toolend`/`assistant` 事件的实时渲染
   - Markdown 内容的流式显示和增量更新
   - 自动滚动和焦点管理
   - 大数据量时的性能优化

#### 技术挑战
- SSE长连接的稳定性处理
- 实时Markdown内容的渲染性能
- 大数据量时的UI响应性

#### 可验证制品
- ✅ 点击Review按钮能跳转到Agent页面
- ✅ 可以实时看到工具执行过程(`> ReadMultipleFiles: [file1.go, file2.go]`)
- ✅ LLM输出内容能实时追加显示
- ✅ 支持会话中断和错误处理

---

### 🔄 迭代3: 建议系统和代码交互 (3周)

**目标**: 实现完整的建议管理和Apply/Reject功能

#### 技术任务
1. **建议视图组件(Suggestions View)**
   - 独立的suggestion列表管理界面
   - 支持折叠/展开显示模式
   - 建议状态管理(Pending/Applied/Rejected)
   
2. **代码Diff显示**
   - 集成IDE的差异显示能力
   - 代码片段高亮和对比
   - 文件路径点击跳转功能
   
3. **Apply/Reject机制**
   - 直接的文件修改和应用功能
   - 建议反馈和确认系统
   - 操作历史和撤销机制
   
4. **文件跳转集成**
   - 与IDE编辑器的深度集成
   - 精确的代码位置定位
   - 修改后的自动跳转

#### 可重用组件
- IDE的文件编辑和跳转API
- 现有的通知系统框架

#### 可验证制品
- ✅ 可以看到所有建议的结构化列表显示
- ✅ 点击建议能展开查看详细的代码diff
- ✅ Apply建议能正确修改文件内容并跳转到修改位置
- ✅ Reject建议有反馈确认机制

---

### 🔄 迭代4: 历史记录和完整体验 (2周)

**目标**: 完善历史功能和用户体验闭环

#### 技术任务
1. **会话状态管理系统**
   - 新的 `IntellicodeSessionManager` 服务
   - 会话生命周期管理(创建、运行、完成、失败)
   - 会话状态持久化和恢复机制
   - 会话元数据管理(创建时间、文件信息、状态等)
   
2. **历史记录页面**
   - 会话列表和元数据展示
   - 支持分页加载和搜索筛选
   - 会话状态显示(running/completed/failed)
   
3. **历史模式Agent页面**
   - 支持查看完整历史会话的静态版本
   - 完整结果渲染和建议状态展示
   - 历史会话的导航和操作
   
4. **登录集成**
   - 与现有登录系统的Token复用
   - 网页登录后直接跳转打开IDE
   - 北斗协同登录状态同步
   
5. **Commit Hook**
   - Git提交时的自动提醒弹窗机制
   - 智能的触发时机判断
   - 用户偏好设置和开关

#### 集成点
- 复用现有的登录Token管理
- 集成现有的Git操作监听
- 利用现有的通知系统

#### 可验证制品
- ✅ 会话状态能正确保存和恢复，支持异常中断后的状态重建
- ✅ 历史记录页面显示所有过往会话，包含详细的状态和元数据
- ✅ 点击历史会话能完整查看分析结果和建议状态
- ✅ Git commit时能自动弹出Review提醒弹窗
- ✅ 支持会话搜索、筛选和管理功能，包括按状态筛选

## 迭代依赖关系

```
迭代1 (基础架构) 
    ↓ [protobuf通信 + UI框架]
迭代2 (实时通信) 
    ↓ [SSE流处理 + 实时输出]
迭代3 (建议交互) 
    ↓ [代码操作 + 用户反馈]
迭代4 (历史记录 + 会话管理)
```

## 技术风险和缓解方案

### 高风险项
1. **SSE长连接稳定性**
   - 风险: 网络断连、超时处理
   - 缓解: 实现自动重连和状态恢复机制

2. **大文件实时渲染性能**
   - 风险: UI卡顿、内存溢出
   - 缓解: 分块渲染和虚拟滚动技术

3. **文件修改冲突处理**
   - 风险: Apply建议时的文件冲突
   - 缓解: 文件锁定和冲突检测机制

### 中风险项
1. **protobuf版本兼容性**
2. **跨IDE版本的UI适配**
3. **Git操作的权限处理**

## 开发资源估算

- **总工期**: 10周
- **核心开发**: 2人
- **UI/UX**: 1人(兼职)
- **测试**: 1人(后期介入)

## 质量保证

### 测试策略
- **单元测试**: protobuf通信、会话管理
- **集成测试**: SSE连接、文件操作
- **UI测试**: 关键用户流程验证
- **性能测试**: 大文件处理、长会话稳定性

### 发布标准
- 所有可验证制品测试通过
- 性能指标满足要求
- 用户体验流程完整
- 错误处理机制完善

---

*文档版本: v1.0*  
*创建时间: 2025-07-30*  
*最后更新: 2025-07-30*