# Agent Workspace 页面需求

用户旅途

- Home 页面点击 Review 按钮，跳转到 Agent Workspace，进入实时模式
- History 页面点击查看历史记录，跳转到 Agent Workspace，进入历史模式

## Agent Workspace 页面设计

### 页面模式

**实时模式**：

- 新建 Review 会话，接收 SSE 流式数据
- 支持用户实时操作建议项
- UI 设计稿：![Streaming Agent Workspace](./streaming-agent-page.png)

**历史模式**：

- 查看历史 Review 会话，展示完整结果
- UI 设计稿：![History Agent Workspace](./history-agent-page.png)

### 整体布局

页面采用上下分区布局设计：

- **上半部分**：Execution Display Area
- **下半部分**：Suggestion Area

### Execution Display Area

- 使用可支持长文本垂直滚动的组件展示
- 滚动条默认置底，方便查看最新的内容

#### 内容渲染

Execution Display Area 显示审查过程中的工具执行和助手响应事件。

**事件渲染规则**：详见 [Event 设计文档](./event-design.md)

**事件处理逻辑**：

1. **会话开始事件** (`EVENT_TYPE_SESSION_START`)：

   - 初始化页面状态为 `CONNECTED`
   - 显示会话开始提示
   - 不渲染到 UI

2. **工具事件** (`EVENT_TYPE_TOOL_START` / `EVENT_TYPE_TOOL_END`)：

   - 输出到 Execution Display Area
   - 具体渲染格式见 [Event 设计文档](./event-design.md)

3. **助手响应事件** (`EVENT_TYPE_ASSISTANT`)：

   - 输出到 Execution Display Area
   - 具体渲染格式见 [Event 设计文档](./event-design.md)

4. **建议事件** (`EVENT_TYPE_SUGGESTIONS`)：

   - 触发调用 `GET /api/review/suggestions` 接口
   - 将获取到的建议数据动态添加到 Suggestion Area
   - 不渲染到 Execution Display Area

5. **会话结束事件** (`EVENT_TYPE_SESSION_END`)：
   - 更新页面状态为 `READY`
   - 显示会话完成提示
   - 停止接收新的 SSE 事件

**渲染方式**：

- **实时模式**：逐步接收 SSE 事件，动态追加内容
- **历史模式**：一次性渲染完整历史数据

### Suggestion Area

#### 数据获取逻辑

**实时模式**：

- 通过 SSE 接收 `EVENT_TYPE_SUGGESTIONS` 事件
- 收到 suggestion event 后，触发调用 `GET /api/review/suggestions` 接口获取详细数据
- 动态添加新的建议项到 Suggestion Area 列表中

**历史模式**：

- 进入页面时直接调用 `GET /api/review/suggestions` 接口
- 一次性获取完整的建议数据
- 静态渲染所有建议项到列表中

#### 列表布局和状态

**建议项显示格式**：

```
[状态图标] [摘要信息] [折叠图标]
```

建议列表需要使用滚动条，默认显示最上面 3 个，按照状态排序，SUGGESTION_STATUS_UNDONE 在最上面。

**状态图标**：

- ○ 待处理 (SUGGESTION_STATUS_UNDONE)
- ✓ 已应用 (SUGGESTION_STATUS_DONE + FEEDBACK_ACTION_AGREE)
- ✗ 已拒绝 (SUGGESTION_STATUS_DONE + FEEDBACK_ACTION_REJECT)

**折叠/展开逻辑**：

- 默认状态：所有建议项折叠显示，只显示标题行
- 点击建议项标题：展开该项，显示详细描述和操作按钮
- 同时只能展开一个建议项，展开新项时自动折叠其他项

#### 交互操作流程

**点击建议项标题**：

1. 展开建议详情面板，显示：
   - 完整的建议描述
   - 代码位置信息（起始行号-结束行号）
   - Apply/Reject 操作按钮
     - status 为 SUGGESTION_STATUS_UNDONE 可用
     - status 为 SUGGESTION_STATUS_DONE 则 disable
2. 在 IDE 编辑器中打开对应文件并定位到建议位置
3. 在编辑器中以 diff 样式高亮显示：
   - 红色背景：原始代码 (original_code_snippet)
   - 绿色背景：建议代码 (suggested_code_snippet)

**Apply 操作流程**：

1. 点击 Apply 按钮
2. 调用 `/api/review/feedback` 接口
3. 成功后：
   - 状态图标变为 ✓ (已应用)
   - 按钮变为不可点击状态
   - 在编辑器中应用代码更改
4. 失败时显示错误提示

**Reject 操作流程**：

1. 点击 Reject 按钮
2. 确认后调用反馈接口
3. 成功后状态图标变为 ✗ (已拒绝)

#### 状态变更和反馈

**加载状态**：

- 操作按钮显示加载动画
- 禁用其他交互操作

**成功反馈**：

- 状态图标立即更新
- 显示简短的成功提示 (Toast)

**错误反馈**：

- 显示错误消息和重试按钮
- 保持原有状态不变

### 用户交互流程

#### 实时模式操作流程

1. **进入页面**：从 Home 页面点击 Review 按钮
2. **建立连接**：显示连接状态，开始接收 SSE 事件
3. **查看输出**：实时观察工具执行和助手响应
4. **处理建议**：当建议出现时，逐个查看和处理
5. **完成会话**：收到 SESSION_END 事件，显示完成状态

#### 历史模式浏览流程

1. **进入页面**：从 History 页面选择历史记录
2. **加载数据**：显示加载状态，获取完整历史数据
3. **浏览内容**：查看完整的执行历史和所有建议
4. **查看详情**：点击建议项查看详细信息和最终状态

#### 建议处理完整流程

```
查看建议列表 → 点击建议项 → 展开详情 → 查看 diff → 做出决策 → 执行操作 → 查看结果
      ↓              ↓           ↓          ↓         ↓          ↓          ↓
   折叠状态      展开单个项    显示描述   编辑器高亮   Apply/Reject  API调用   状态更新
```

### 状态管理

#### 页面状态

- `LOADING`：页面数据加载中
- `CONNECTED`：SSE 连接已建立（实时模式）
- `READY`：数据加载完成，可以交互
- `ERROR`：连接失败或数据加载失败

#### 建议状态

基于 `ReviewSuggestion.status` 值确定按钮状态：

- `SUGGESTION_STATUS_UNDONE`：待处理，显示 Apply/Reject 操作按钮
- `SUGGESTION_STATUS_DONE`：已处理，按钮不可点击，根据 `action` 字段显示最终状态：
  - `FEEDBACK_ACTION_AGREE`：显示 ✓ 已应用
  - `FEEDBACK_ACTION_REJECT`：显示 ✗ 已拒绝

操作过程中的临时状态：

- `PROCESSING`：操作中，按钮显示加载动画并禁用交互

#### 错误状态

- 网络连接错误：显示重连按钮
- API 调用失败：显示重试按钮
- 数据解析错误：显示错误详情

## 异常场景处理

### 连接异常

- **连接失败/中断**：显示连接状态提示
- **长时间无响应**：显示友好的等待提示，保持静止状态

### 操作异常

- **建议操作失败**：显示错误提示信息
- **页面意外关闭**：实时模式下显示离开确认对话框

### 数据异常

- **历史数据加载失败**：显示错误状态页面
- **异常数据**：静默跳过，不影响正常显示

## API 接口定义

### 概述

Agent Workspace 页面与后端通过以下 API 进行数据交互：

- **实时模式**: 使用 `POST /api/review` 创建审查会话，通过 SSE 流式接收事件数据
- **历史模式**: 使用 `GET /api/review/events` 获取完整的历史事件数据

数据模型定义参考：

- `proto/api.proto`: API 接口和消息结构定义
- `proto/event.proto`: SSE 事件类型和数据结构定义

### 实时模式 API

#### 创建审查会话

**接口**: `POST /api/review`
**SSE 连接**: 自动建立 Server-Sent Events 连接

**请求结构**:

```json
{
  "base_req": {
    "cicd_token": "string"
  },
  "repo_path": "string", // 仓库绝对路径
  "review_files": ["string"], // 可选，指定审查文件列表
  "ide": "IDE_JETBRAINS" // IDE 类型标识
}
```

**SSE 事件流**:
SSE 连接建立后，将接收到以下类型的事件：

1. **会话开始事件** (`EVENT_TYPE_SESSION_START`)

```json
{
  "type": "EVENT_TYPE_SESSION_START",
  "session_start": {
    "session_id": "string",
    "review_files": ["string"]
  }
}
```

2. **工具开始事件** (`EVENT_TYPE_TOOL_START`)

```json
{
  "type": "EVENT_TYPE_TOOL_START",
  "tool_start": {
    "name": "string",
    "input": "string",
    "status": "TOOL_STATUS_RUNNING",
    "tool_call_id": "string"
  }
}
```

3. **工具结束事件** (`EVENT_TYPE_TOOL_END`)

```json
{
  "type": "EVENT_TYPE_TOOL_END",
  "tool_end": {
    "name": "string",
    "input": "string",
    "output": "string",
    "status": "TOOL_STATUS_SUCCESS|TOOL_STATUS_ERROR",
    "tool_call_id": "string"
  }
}
```

4. **助手响应事件** (`EVENT_TYPE_ASSISTANT`)

```json
{
  "type": "EVENT_TYPE_ASSISTANT",
  "assistant": {
    "chunk": "string" // Markdown 内容片段
  }
}
```

5. **建议事件** (`EVENT_TYPE_SUGGESTIONS`)

```json
{
  "type": "EVENT_TYPE_SUGGESTIONS",
  "suggestions": {
    "suggestions": [
      {
        "id": 123,
        "category": "string",
        "level": "LEVEL_MAJOR|LEVEL_MINOR|LEVEL_CRITICAL|LEVEL_BLOCKER|LEVEL_INFO",
        "file_path": "string",
        "start_line": 10,
        "end_line": 15,
        "description": "string",
        "old_string": "string", // 原始代码
        "new_string": "string" // 建议代码
      }
    ]
  }
}
```

6. **会话结束事件** (`EVENT_TYPE_SESSION_END`)

```json
{
  "type": "EVENT_TYPE_SESSION_END",
  "session_end": {
    "session_id": "string",
    "success": true
  }
}
```

### 历史模式 API

#### 获取历史事件数据

**接口**: `GET /api/review/events`
**描述**: 获取指定会话的完整事件历史

**请求参数**:

```json
{
  "base_req": {
    "cicd_token": "string"
  },
  "repo_path": "string",
  "session_id": "string"
}
```

**响应结构**:

```json
{
  "events": [Event],              // 完整的事件列表，结构同 SSE 事件
  "session_id": "string",
  "repo_path": "string",
  "created_at": 1640995200000,    // 时间戳
  "review_files": ["string"]      // 审查的文件列表
}
```

#### 获取建议数据

**接口**: `GET /api/review/suggestions`
**描述**: 获取审查建议和处理状态

**数据结构**: 参考 `proto/api.proto` 中的定义：

- 请求: `GetReviewRecordSuggestionsRequest` (`proto/api.proto:114`)
- 响应: `GetReviewRecordSuggestionsResponse` (`proto/api.proto:149`)
- 建议数据: `ReviewSuggestion` (`proto/api.proto:121`)
- 状态枚举: `SuggestionStatus` (`proto/api.proto:31`)
- 操作枚举: `FeedbackAction` (`proto/api.proto:38`)

### 建议反馈 API

#### 提交建议反馈

**接口**: `POST /api/review/feedback`
**描述**: 用户对建议的接受/拒绝操作

**数据结构**: 参考 `proto/api.proto` 中的定义：

- 请求: `CreateSuggestionFeedBackRequest` (`proto/api.proto:155`)
- 响应: `CreateSuggestionFeedBackResponse` (`proto/api.proto:170`)
- 操作类型: `FeedbackAction` (`proto/api.proto:38`)
  - `FEEDBACK_ACTION_AGREE`: 接受建议
  - `FEEDBACK_ACTION_REJECT`: 拒绝建议
