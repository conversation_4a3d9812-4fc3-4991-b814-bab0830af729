# Intellicode Agent 功能设计文档

## 概述

Intellicode Agent 是 JetDev 插件的代码审查功能，提供智能代码分析、建议生成和交互式修复能力。

## 核心功能

### 1. 自动触发机制
- **Commit Hook**: 监听 commit 动作，触发 Review 提醒弹窗
- **手动触发**: 用户主动点击 Review 按钮进入审查流程

### 2. 文件列表管理
- **优先级逻辑**: 
  - 直接进入插件：优先显示 uncommitted 文件列表
  - 无 uncommitted 文件时：显示 latest committed 文件列表
  - Hook 后进入：默认显示 committed 文件列表
- **文件选择**: 支持用户选择需要审查的文件

### 3. 插件集成
- **IDE 跳转**: 网页登录后直接跳转打开 IDE（类似 Augment Code）
- **北斗协同**: 考虑与神机的登录状态同步

## 技术架构

后端： Intellicode 服务, api 对应为 proto/api.proto 文件

### 前后端交互
- **协议**: 使用 protobuf 定义协议、数据结构
- **通信**: 通过 HTTP 通信，protojson 完成 JSON 序列化
- **实时通信**: SSE (Server-Sent Events) 用于流式数据传输


## UI 设计

### 导航结构
插件顶部需要有一个工具栏，包含 Login、Home、History、Help 四个按钮，点击跳转对应的页面。

工具栏
├── Login
├── Home
├── 历史记录
└── Help

### Home Page
![Home page with uncommitted files](./home-uncommit.png)

Home 页面
├── 工具栏
├── head 区域 (展示信息，如欢迎语、功能介绍)
├── 文件列表区域
└── 按钮交互区域
    ├── git 文件切换按钮
    ├──  Review 按钮

#### 功能说明
进入页面需要判断 git repo状态，根据状态显示：
- 如果有未提交文件，文件列表展示所有未提交文件，“git 文件切换”按钮显示：【未提交文件】
- 如果没有未提交文件且有commit，文件列表展示最近一次文件，“git 文件切换”按钮显示：【最新提交文件】；且文件列表勾选框不可交互
- 如果是空仓库或者没有git init，则通过head区域提示用户。

文件列表交互：
- 点击文件列表项，勾选框选中状态切换
- 只点击文件列表，则在编辑器区域显示当前文件的git diff内容，以便用户查看review的变更是什么
- 支持批量选择文件
- 支持上下滚动

Review 按钮：
- 有文件选中时才能触发
- 对应触发接口：`post: "/api/review"`,后端返回 SSE 流。
- 点击后，需要跳转到Agent 页面



### 历史记录页面
![History List Page](./history-list.png)

#### UI 布局
页面采用垂直列表布局设计：

**顶部工具栏**：
- 包含登录状态、主页、历史记录、帮助等功能按钮
- 当前页面标题显示"历史记录"

**会话列表区域**：
- 采用卡片式列表展示历史会话
- 每行显示完整的会话信息和操作按钮
- 支持垂直滚动查看更多历史记录

#### 会话列表项设计

**状态图标**：
-  **已完成** (completed)
-  **失败** (failed)
-  **处理中** (running)

**会话信息显示**：
- **会话 ID**: `session-id-xxxxxx` 格式的唯一标识符
- **文件数量**: 显示本次审查涉及的文件数量（如"2 个文件"）
- **时间戳**: 会话创建/完成时间，格式为 `YYYY/MM/DD HH:mm:ss`

**操作**：
- 每行右侧包含复制session id 按钮
- 点击记录，可重新进入Agent 页面
    - running，则进入实时模式
    - completed/failed，则进入历史模式

#### 交互行为

**列表项点击**：
- 点击会话列表项进入历史模式的 Agent 页面
- 传递 sessionId 参数，切换到 HISTORY 显示模式
- 展示该会话的完整历史数据和最终结果

#### 数据获取与管理

**API 接口**：
- **审查记录列表**: `GET /api/review/records` 获取用户的历史审查记录


## 登录逻辑
检测登录状态逻辑可参考 CodeReview
未登录触发重新登录可以弹右下角提示，点击提示触发登录
登录按钮只是兜底的功能