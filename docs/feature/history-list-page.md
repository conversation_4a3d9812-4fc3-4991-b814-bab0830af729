# 历史记录页面需求

> 历史记录页面是Intellicode Agent的运行历史记录列表展示页面

## 功能描述
用户点击工具栏【History】按钮可以进入历史记录页面，通过API接口获取列表数据，并且按照返回的数据展示页面信息。

## 技术实现

### 页面集成
- 利用现有的 `IntellicodeHistoryAction` 工具栏按钮（已存在但 actionPerformed 方法待实现）
- 在 `IntellicodeHomePanel` 中实现页面切换逻辑，参考现有的 showAgentPanel/showHomePanel 模式  
- 创建独立的 `IntellicodeHistoryListPanel` 历史记录面板
- 通过 setToolbar() 方法动态切换工具栏按钮组
- 保持与现有 Home/Agent 页面的状态独立性

### API 接口

#### 主要接口：`ListReviewRecords`
- **路径**: `GET /api/review/records`
- **请求参数**: 
  - `base_req.cicd_token`: 认证令牌
  - `repo_path`: 仓库路径  
  - `limit`: 分页限制（默认10）
- **响应数据**:
  - `records[]`: ReviewRecord 数组
  - `count`: 实际返回记录数

#### ReviewRecord 数据结构
```
- review_id: 审查记录ID
- session_id: 会话ID  
- repo_path: 仓库路径
- status: 审查状态 (UNDONE/DONE/CHECKING/FAILED)
- trigger_by: 触发者
- email: 邮箱地址
- created_at: 创建时间戳
```

## UI 设计

### 列表展示
- **排序**: 按创建时间倒序排列
- **分页**: 首次加载最近10条记录
- **列表项内容**:
  - **状态标识**: 根据 API status 字段显示
    - `DONE` 
    - `CHECKING`
    - `FAILED`
    - `UNDONE`
  - **Session ID**: 显示完整 session_id
  - **复制按钮**: 点击复制 session_id 到剪贴板

### 交互行为
- **点击列表项**: 打开对应 session 的详情页面（功能预留）
- **复制按钮**: 复制 session_id 到系统剪贴板
- **状态显示**: 使用 IntelliJ 主题色彩系统
- **加载状态**: 显示加载指示器和错误处理

## 实现细节

### 组件结构
- `IntellicodeHistoryListPanel`: 主面板容器
- `ReviewRecordItem`: 数据模型类

### 设计规范
- 使用 IntelliJ Platform UI 组件（JBList、JBScrollPane 等）
- 遵循 IntelliJ 设计指南的色彩和间距规范
- 支持暗色/亮色主题自适应
- 使用 Disposable 模式管理资源生命周期

### 数据处理
- 实现错误处理和重试机制
- 缓存策略：进入页面时重新加载数据
- 定时刷新：默认10秒刷新一次列表数据
