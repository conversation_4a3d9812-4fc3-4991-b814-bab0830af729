# Intellicode Agent API 文档

## 概述

本文档描述了 Intellicode Agent 的 API 接口，用于代码审查和建议功能。API 基于 Protocol Buffers (protobuf) 定义，支持 gRPC 通信。

## API 定义

### 核心消息类型

#### ReviewSuggestion

代码审查建议的核心数据结构：

```protobuf
message ReviewSuggestion {
  string file_path = 1;                // 文件路径
  int32 start_line = 2;                // 起始行号
  int32 end_line = 3;                  // 结束行号
  string description = 4;              // 建议描述
  string old_string = 5;               // 原始代码
  string new_string = 6;               // 建议的新代码
  SuggestionStatus status = 7;         // 建议状态
  string intellicode_review_id = 8;    // Intellicode 审查 ID
}
```

#### SuggestionStatus

建议状态枚举：

```protobuf
enum SuggestionStatus {
  SUGGESTION_STATUS_UNSPECIFIED = 0;  // 未指定
  SUGGESTION_STATUS_UNDONE = 1;       // 未完成
  SUGGESTION_STATUS_DONE = 2;         // 已完成
}
```

#### ReviewStatus

审查状态枚举：

```protobuf
enum ReviewStatus {
  REVIEW_STATUS_UNSPECIFIED = 0;  // 未指定
  REVIEW_STATUS_DONE = 1;         // 已完成
  REVIEW_STATUS_CHECKING = 2;     // 检查中
  REVIEW_STATUS_FAILED = 3;       // 失败
}
```

#### IDE

IDE 类型枚举：

```protobuf
enum IDE {
  IDE_UNSPECIFIED = 0;  // 未指定
  IDE_JETBRAINS = 1;    // JetBrains IDE
  IDE_VSCODE = 2;       // VS Code
}
```

### 事件系统

#### EventType

事件类型枚举：

```protobuf
enum EventType {
  EVENT_TYPE_UNSPECIFIED = 0;  // 未指定
  TOOL_START = 1;              // 工具开始
  TOOL_END = 2;                // 工具结束
  ASSISTANT = 3;               // 助手消息
  SUGGESTIONS = 4;             // 建议
  SESSION_START = 5;           // 会话开始
  SESSION_END = 6;             // 会话结束
}
```

#### Event

事件消息结构：

```protobuf
message Event {
  EventType type = 1;  // 事件类型
  
  oneof data {
    ToolStartData tool_start = 2;        // 工具开始数据
    ToolEndData tool_end = 3;            // 工具结束数据
    AssistantData assistant = 4;         // 助手数据
    SuggestionsData suggestions = 5;     // 建议数据
    SessionStartData session_start = 6;  // 会话开始数据
    SessionEndData session_end = 7;      // 会话结束数据
  }
}
```

#### ToolStatus

工具状态枚举：

```protobuf
enum ToolStatus {
  TOOL_STATUS_UNSPECIFIED = 0;  // 未指定
  RUNNING = 1;                  // 运行中
  SUCCESS = 2;                  // 成功
  ERROR = 3;                    // 错误
}
```

#### Level

级别枚举：

```protobuf
enum Level {
  LEVEL_UNSPECIFIED = 0;  // 未指定
  INFO = 1;               // 信息
  MINOR = 2;              // 轻微
  MAJOR = 3;              // 重要
}
```

#### Suggestion

建议消息结构：

```protobuf
message Suggestion {
  string file_path = 1;     // 文件路径
  int32 start_line = 2;     // 起始行号
  int32 end_line = 3;       // 结束行号
  string description = 4;   // 描述
  string old_string = 5;    // 原始字符串
  string new_string = 6;    // 新字符串
  Level level = 7;          // 级别（使用 Level 枚举）
}
```

#### 会话相关消息

```protobuf
message SessionStartData {
  string session_id = 1;           // 会话 ID
  repeated string review_files = 2; // 审查文件列表
}

message SessionEndData {
  string session_id = 1;  // 会话 ID
  bool success = 2;       // 是否成功
}
```

## Java 代码生成

### 生成的 Java 类

protobuf 编译后会生成以下 Java 类：

- `Api.java`: 包含所有 API 相关的消息类型和枚举
- `EventOuterClass.java`: 包含所有事件相关的消息类型和枚举

### 生成位置

生成的 Java 代码位于：
- `build/generated/source/proto/main/java/intellicode/api/v1/Api.java`
- `build/generated/source/proto/main/java/intellicode/event/v1/EventOuterClass.java`

### 使用示例

#### 创建 ReviewSuggestion

```java
Api.ReviewSuggestion suggestion = Api.ReviewSuggestion.newBuilder()
    .setFilePath("src/main/java/Example.java")
    .setStartLine(10)
    .setEndLine(15)
    .setDescription("建议优化此方法")
    .setOldString("public void oldMethod() { ... }")
    .setNewString("public void newMethod() { ... }")
    .setStatus(Api.SuggestionStatus.SUGGESTION_STATUS_UNDONE)
    .setIntellicodeReviewId("review-123")
    .build();
```

#### 创建 Event

```java
EventOuterClass.Event event = EventOuterClass.Event.newBuilder()
    .setType(EventOuterClass.EventType.SUGGESTIONS)
    .setSuggestions(EventOuterClass.SuggestionsData.newBuilder()
        .addSuggestions(EventOuterClass.Suggestion.newBuilder()
            .setFilePath("src/main/java/Example.java")
            .setStartLine(10)
            .setEndLine(15)
            .setDescription("建议优化此方法")
            .setOldString("public void oldMethod() { ... }")
            .setNewString("public void newMethod() { ... }")
            .setLevel(EventOuterClass.Level.MINOR)
            .build())
        .build())
    .build();
```

#### 创建会话事件

```java
// 会话开始事件
EventOuterClass.Event sessionStartEvent = EventOuterClass.Event.newBuilder()
    .setType(EventOuterClass.EventType.SESSION_START)
    .setSessionStart(EventOuterClass.SessionStartData.newBuilder()
        .setSessionId("session-456")
        .addReviewFiles("src/main/java/Example1.java")
        .addReviewFiles("src/main/java/Example2.java")
        .build())
    .build();

// 会话结束事件
EventOuterClass.Event sessionEndEvent = EventOuterClass.Event.newBuilder()
    .setType(EventOuterClass.EventType.SESSION_END)
    .setSessionEnd(EventOuterClass.SessionEndData.newBuilder()
        .setSessionId("session-456")
        .setSuccess(true)
        .build())
    .build();
```

## API 接口规范

### 1. 服务端点定义

基于 `proto/api.proto` 的定义：

```protobuf
// ReviewService 代码审查服务接口
service ReviewService {
  // Review 执行代码审查，返回 SSE 事件流
  // 使用 HTTP POST /api/review
  // 支持 Server-Sent Events (SSE) 流式响应
  rpc StreamReview(ReviewRequest) returns (Event) {
    option (sse_enabled) = true;
  };
}
```

**HTTP 端点**:
- **URL**: `POST /api/review`
- **Content-Type**: `application/json`
- **Response**: `text/event-stream` (SSE)
- **认证**: `Authorization: Bearer <token>`

### 2. 请求数据结构

```protobuf
// ReviewRequest 代码审查请求
message ReviewRequest {
  BaseReq base_req = 1;
  // 仓库路径，绝对路径
  string repo_path = 2;
  // 需要审查的文件列表，如果为空则审查所有变更文件
  repeated string review_files = 3;
  IDE ide = 4;
}

message BaseReq {
    string cicd_token = 1;
}

enum IDE {
  IDE_UNSPECIFIED = 0;
  IDE_JETBRAINS = 1;
  IDE_VSCODE = 2;
}
```

**请求示例**:
```json
{
  "base_req": {
    "cicd_token": "your-auth-token"
  },
  "repo_path": "/path/to/your/project",
  "review_files": ["src/main.go", "src/utils.go"],
  "ide": "IDE_JETBRAINS"
}
```

### 3. 响应格式

服务端返回 Server-Sent Events (SSE) 流，每个事件包含：
- `event`: 事件类型
- `data`: JSON 格式的事件数据

## SSE 事件规范

基于 `proto/event.proto` 的定义：

### 1. 事件类型定义

```protobuf
enum EventType {
  EVENT_TYPE_UNSPECIFIED = 0;
  EVENT_TYPE_TOOL_START = 1;     // 工具开始执行事件
  EVENT_TYPE_TOOL_END = 2;       // 工具执行完成事件  
  EVENT_TYPE_ASSISTANT = 3;      // LLM 流式响应事件
  EVENT_TYPE_SUGGESTIONS = 4;    // 修复建议事件
  EVENT_TYPE_SESSION_START = 5;  // 会话开始事件
  EVENT_TYPE_SESSION_END = 6;    // 会话结束事件
}
```

### 2. 事件数据结构

#### 会话管理事件

**会话开始事件** (`EVENT_TYPE_SESSION_START`):
```protobuf
message SessionStartData {
  string session_id = 1;           // 会话唯一ID
  repeated string review_files = 2; // 审查文件列表
}
```

**会话结束事件** (`EVENT_TYPE_SESSION_END`):
```protobuf
message SessionEndData {
  string session_id = 1;  // 会话ID
  bool success = 2;       // 是否成功完成
}
```

#### 工具执行事件

**工具开始事件** (`EVENT_TYPE_TOOL_START`):
```protobuf
message ToolStartData {
  string name = 1;        // 工具名称，如 "ReadMultipleFiles"
  string input = 2;       // 输入参数，如 "[file1.go, file2.go]"
  ToolStatus status = 3;  // 状态：RUNNING
}
```

**工具结束事件** (`EVENT_TYPE_TOOL_END`):
```protobuf
message ToolEndData {
  string name = 1;        // 工具名称
  string input = 2;       // 输入参数
  string output = 3;      // 执行结果，如 "Read 2 files, total 150 lines"
  ToolStatus status = 4;  // 状态：SUCCESS/ERROR
}
```

**工具状态枚举**:
```protobuf
enum ToolStatus {
  TOOL_STATUS_UNSPECIFIED = 0;
  TOOL_STATUS_RUNNING = 1;     // 工具正在运行
  TOOL_STATUS_SUCCESS = 2;     // 工具执行成功
  TOOL_STATUS_ERROR = 3;       // 工具执行出错
}
```

#### LLM 响应事件

**助手响应事件** (`EVENT_TYPE_ASSISTANT`):
```protobuf
message AssistantData {
  string chunk = 1;  // Markdown 内容片段
}
```

#### 建议事件

**修复建议事件** (`EVENT_TYPE_SUGGESTIONS`):
```protobuf
message SuggestionsData {
  repeated Suggestion suggestions = 1;
}

message Suggestion {
  int32 id = 1;              // 建议唯一ID
  string category = 2;       // 类别：code_defect/performance/style
  string level = 3;          // 严重程度：major/minor/info
  string file_path = 4;      // 文件路径
  int32 start_line = 5;      // 起始行号
  int32 end_line = 6;        // 结束行号
  string description = 7;    // 问题描述
  string old_string = 8;     // 原始代码
  string new_string = 9;     // 建议修改后的代码
}
```

### 3. SSE 事件流示例

完整的代码审查会话事件流：

```
event: session_start
data: {"session_id": "uuid-123", "review_files": ["src/main.go"]}

event: tool_start
data: {"name": "ReadMultipleFiles", "input": "[src/main.go]", "status": "RUNNING"}

event: tool_end
data: {"name": "ReadMultipleFiles", "output": "Read 1 file, total 50 lines", "status": "SUCCESS"}

event: assistant
data: {"chunk": "## 代码审查结果\n\n发现以下问题：\n"}

event: assistant
data: {"chunk": "\n### 1. 潜在的空指针引用\n"}

event: suggestions
data: {"suggestions": [{"id": 1, "category": "code_defect", "level": "major", "file_path": "src/main.go", "start_line": 15, "end_line": 20, "description": "潜在的空指针引用", "old_string": "if user.Name != nil", "new_string": "if user != nil && user.Name != nil"}]}

event: assistant
data: {"chunk": "\n审查完成，共发现 1 个问题。"}

event: session_end
data: {"session_id": "uuid-123", "success": true}
```

## 客户端实现指南

### 1. SSE 连接管理

在 JetBrains IDE 插件中，需要使用 OkHttp 客户端建立 SSE 连接：

- 发送 POST 请求到 `/api/review` 端点
- 设置 `Accept: text/event-stream` 头部
- 处理 SSE 事件流，解析不同类型的事件
- 实现事件监听器接口处理各种事件类型

### 2. 错误处理

实现重连机制和错误处理：

- 设置最大重试次数（建议 3 次）
- 实现延迟重试机制（建议 5 秒间隔）
- 处理网络异常和连接中断
- 提供错误回调接口

### 3. 状态管理

- **实时模式**: 监听 SSE 事件，动态更新 UI 组件
- **历史模式**: 一次性加载完整事件历史，按时间顺序重放渲染
- **断线重连**: 连接断开时自动重连，支持从断点继续

## 错误码定义

### HTTP 状态码

- `200`: 成功建立 SSE 连接
- `400`: 请求参数错误
- `401`: 认证失败，token 无效或过期
- `403`: 权限不足
- `500`: 服务器内部错误

### 工具执行错误

当 `ToolEndData.status` 为 `TOOL_STATUS_ERROR` 时，`output` 字段包含具体错误信息：

```json
{
  "name": "ReadMultipleFiles",
  "input": "[nonexistent.go]",
  "output": "Error: file not found: nonexistent.go",
  "status": "TOOL_STATUS_ERROR"
}
```

## 性能考虑

### 1. 连接管理
- 单个会话使用一个 SSE 连接
- 连接超时时间：30 分钟
- 心跳检测：每 30 秒发送 ping 事件


## 安全考虑

### 1. 认证授权
- 所有 API 请求必须包含有效的认证 token
- 支持 token 刷新机制
