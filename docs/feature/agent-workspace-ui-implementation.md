# AgentWorkspace UI 实现方案

## 概述

本文档描述 AgentWorkspace 页面的 UI 实现方案，重点实现 Suggestion Area 的建议列表功能，Execution Display Area 暂时保留为空白。

## 整体架构

### 页面布局

- 使用 **JBSplitter** 实现上下分区布局（比例 80:20）
- **上部区域**：Execution Display Area - 暂时保留空白
- **下部区域**：Suggestion Area - 重点实现建议列表

### 入口方式

- 从历史记录列表（IntellicodeHistoryListPanel）单击跳转
- 传递参数：sessionId, reviewId

## 核心组件设计

### 1. AgentWorkspacePanel.java (新文件)

**职责**：主面板容器

- 继承 `SimpleToolWindowPanel`
- 使用 `JBSplitter(true, 0.6f)` 创建垂直分割
- 接收参数：`Project project, String sessionId, int reviewId`
- 调用 API 获取建议数据：`api.getReviewRecordSuggestions(reviewId)`

**布局结构**：

```
┌─────────────────────────────────────┐
│     Execution Display Area         │
│         (空白占位)                   │  80%
├─────────────────────────────────────┤
│     Suggestion Area                 │
│       (建议列表)                     │  20%
└─────────────────────────────────────┘
```

### 2. SuggestionListComponent.java (新文件)

**职责**：建议列表区域的核心实现

#### 组件结构

- **头部面板**：筛选按钮、统计信息
- **列表容器**：建议项的垂直排列
- **滚动面板**：固定高度，支持滚动

#### 关键特性

**1. 高度限制**

- 固定显示高度：约 3 个建议项（110px）
- 超出部分通过垂直滚动条查看
- 使用 `JBScrollPane` 包装

**2. 状态排序**

```
优先级排序：
1. Pending (待处理) - 显示在最上面
2. Applied (已应用) - 中间
3. Rejected (已拒绝) - 最下面
```

**3. 折叠/展开逻辑**

- 默认状态：所有建议项折叠显示
- 点击建议项：展开该项，同时折叠其他项
- 同时只能展开一个建议项

#### UI 布局设计

**建议项折叠状态**：

```
[状态图标] src/main/java/Example.java:45-50 - [MAJOR] - [Code Quality]  [▶]
```

**建议项展开状态**：

```
[状态图标] src/main/java/Example.java:45-50 - [MAJOR] - [Code Quality]  [▼]
    描述：建议使用更具描述性的变量名以提高代码可读性...

    [Apply] [Reject]
```

### 3. 状态图标设计

使用 IntelliJ 内置图标：

- **Pending** (待处理)：`AllIcons.RunConfigurations.TestState.Yellow2` - ○
- **Applied** (已应用)：`AllIcons.RunConfigurations.TestPassed` - ✓
- **Rejected** (已拒绝)：`AllIcons.RunConfigurations.TestFailed` - ✗

### 4. 级别标签设计

彩色标签显示建议级别：

- **CRITICAL/BLOCKER**：红色背景 `JBColor(0xFFE6E6, 0x5C2E2E)`
- **MAJOR**：橙色背景 `JBColor(0xFFF4E6, 0x5C4A2E)`
- **MINOR**：黄色背景 `JBColor(0xFFFCE6, 0x5C5A2E)`
- **INFO**：蓝色背景 `JBColor(0xE6F3FF, 0x2E3C5C)`

## 现有文件修改

### 1. IntellicodeHistoryListPanel.java

**修改内容**：修改单击事件监听

- 修改现有的 `mouseClicked` 事件处理逻辑
- 通过点击位置判断用户意图：
  - 点击右侧复制按钮区域：执行复制 Session ID 功能
  - 点击其他区域：跳转到 AgentWorkspace 页面
- 保留现有的复制功能，避免功能冲突

### 2. IntellicodeHomePanel.java

**修改内容**：添加显示 AgentWorkspace 的方法

- 添加 `showAgentWorkspaceHistoryMode(String sessionId, int reviewId)` 方法
- 创建 AgentWorkspacePanel 实例并替换主面板内容
- 更新工具栏配置，保留 Home 和 History 导航按钮
- 更新页面状态标记

## UI 组件选择

### IntelliJ Platform 组件

- **JBSplitter**：替代 JSplitPane，更好的主题适配
- **JBScrollPane**：支持 IntelliJ 主题的滚动面板
- **JBColor**：自适应暗/亮主题的颜色系统
- **JBUI**：DPI 自适应的间距和尺寸
- **AllIcons**：使用 IntelliJ 内置图标

### 布局管理器

- **BorderLayout**：主面板布局
- **BoxLayout.Y_AXIS**：建议项垂直排列
- **FlowLayout**：状态图标和标签的水平排列

## 视觉设计规范

### 间距系统

- 面板内边距：`JBUI.insets(12)`
- 项目内边距：`JBUI.insets(8, 12)`
- 组件间距：`JBUI.scale(8)`
- 细分割线：`JBUI.scale(2)`

### 颜色系统

- 背景色：`JBUI.CurrentTheme.ToolWindow.background()`
- 边框色：`JBUI.CurrentTheme.CustomFrameDecorations.separatorForeground()`
- 主要文本：`JBUI.CurrentTheme.Label.foreground()`
- 次要文本：`JBUI.CurrentTheme.Label.disabledForeground()`

### 字体规范

- 标题：`JBUI.Fonts.label(16).asBold()`
- 正文：`JBUI.Fonts.label(12)`
- 小标签：`JBUI.Fonts.label(11)`

## 交互设计

### 鼠标交互

- **单击建议项标题**：切换折叠/展开状态
- **悬停效果**：背景色轻微变化
- **单击历史记录**：跳转到 AgentWorkspace（复制按钮区域除外）

### 状态反馈

- **加载状态**：显示 Loading 动画
- **空状态**：显示 "暂无建议" 提示
- **错误状态**：显示错误信息和重试按钮

## 数据流程

### 1. 页面初始化

```
用户单击历史记录
→ 获取 sessionId, reviewId
→ 创建 AgentWorkspacePanel
→ 调用 API 获取建议数据
→ 渲染建议列表
```

### 2. 建议数据处理

```
API 返回建议列表
→ 按状态排序 (Pending > Applied > Rejected)
→ 创建建议项面板
→ 添加到滚动容器
→ 滚动到顶部显示 Pending 项
```

### 3. 折叠/展开处理

```
用户点击建议项
→ 切换当前项状态
→ 折叠其他已展开项
→ 刷新 UI 显示
```

## 实现优势

### 技术优势

- **标准组件**：使用 IntelliJ Platform 官方组件
- **主题适配**：自动支持所有 IntelliJ 主题
- **性能优化**：固定高度避免渲染性能问题
- **扩展性好**：为后续实时模式预留接口

### 用户体验

- **重点突出**：Pending 项优先显示，用户一目了然
- **操作简单**：单击跳转，点击展开，符合直觉
- **视觉统一**：与 IDE 其他部分保持一致的设计语言
- **信息清晰**：状态、级别、文件路径一目了然

## 后续扩展

### 实时模式支持

- 支持 SSE 事件流数据
- 启用操作按钮（Apply/Reject）
- 实时状态更新

### 功能增强

- 搜索和筛选功能
- 批量操作（Accept All/Reject All）
- 代码对比展示（Diff Viewer）
- 编辑器集成（点击定位到代码位置）

### 性能优化

- 虚拟化列表（当建议项过多时）
- 延迟加载建议详情
- 状态持久化（记住展开状态）

## 文件结构

### 新增文件

```
src/main/java/com/sentinel/nocalhost/intellicode/ui/
├── AgentWorkspacePanel.java           # 主面板
└── SuggestionListComponent.java       # 建议列表组件
```

### 修改文件

```
src/main/java/com/sentinel/nocalhost/intellicode/ui/
├── IntellicodeHistoryListPanel.java   # 修改为单击跳转
└── IntellicodeHomePanel.java          # 添加显示方法
```

## 总结

本实现方案重点关注 Suggestion Area 的建议列表功能，通过使用 IntelliJ Platform 标准组件和设计规范，确保与 IDE 的无缝集成。方案具有良好的扩展性，为后续功能增强奠定基础。
