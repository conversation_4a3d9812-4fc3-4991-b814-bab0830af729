# Opt-out flag for bundling Kotlin standard library -> https://jb.gg/intellij-platform-kotlin-stdlib
kotlin.stdlib.default.dependency=false
# Enable Gradle Configuration Cache -> https://docs.gradle.org/current/userguide/configuration_cache.html
org.gradle.configuration-cache=true
# Enable Gradle Build Cache -> https://docs.gradle.org/current/userguide/build_cache.html
org.gradle.caching=true

version=1.1.4

# Existent IDE versions can be found in the following repos:
# https://www.jetbrains.com/intellij-repository/releases/
# https://www.jetbrains.com/intellij-repository/snapshots/
ideaVersion=IU-2023.2

pluginName=JetDev

# see org.gradle.api.JavaVersion
javaCompatibility=VERSION_17

# Get the number from the IDE's download page
# For more info, see https://www.jetbrains.org/intellij/sdk/docs/basics/getting_started/build_number_ranges.html for description
sinceBuild=232.8660.185
untilBuild=251.*

# plugin versions from intellij marketplace https://plugins.jetbrains.com
goPluginVersion=232.8660.48
pythonPluginVersion=232.8660.185
